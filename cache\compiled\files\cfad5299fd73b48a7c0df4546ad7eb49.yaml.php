<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/blueprints/config/site.yaml',
    'modified' => 1711575900,
    'size' => 2289,
    'data' => [
        '@extends' => '@parent',
        'form' => [
            'fields' => [
                'company' => [
                    'type' => 'section',
                    'title' => 'Physical Location',
                    'fields' => [
                        'company.name' => [
                            'type' => 'text',
                            'name' => 'name',
                            'label' => 'Name',
                            'placeholder' => 'ACME Corp.'
                        ],
                        'company.logo' => [
                            'type' => 'file',
                            'label' => 'Company Logo',
                            'destination' => 'user/data/images',
                            'accept' => [
                                0 => 'image/*'
                            ]
                        ],
                        'company.street' => [
                            'type' => 'text',
                            'name' => 'street',
                            'label' => 'Street Address',
                            'placeholder' => '123 Sesame Street'
                        ],
                        'company.city' => [
                            'type' => 'text',
                            'name' => 'city',
                            'label' => 'City',
                            'placeholder' => 'Paradise City'
                        ],
                        'company.state' => [
                            'type' => 'text',
                            'name' => 'state',
                            'label' => 'State/Province',
                            'placeholder' => 'Narnia'
                        ],
                        'company.zip' => [
                            'type' => 'text',
                            'name' => 'zip',
                            'label' => 'Zip/Postal Code',
                            'placeholder' => '33333 or H9X 4C1'
                        ]
                    ]
                ],
                'contact' => [
                    'type' => 'section',
                    'title' => 'Contact Information',
                    'fields' => [
                        'contact.phone' => [
                            'type' => 'tel',
                            'name' => 'phone',
                            'label' => 'Phone Number',
                            'placeholder' => '(*************'
                        ],
                        'contact.fax' => [
                            'type' => 'tel',
                            'name' => 'fax',
                            'label' => 'Fax Number',
                            'placeholder' => '(*************'
                        ],
                        'contact.email' => [
                            'type' => 'email',
                            'name' => 'email',
                            'label' => 'Email Address',
                            'placeholder' => '<EMAIL>'
                        ],
                        'contact.social' => [
                            'name' => 'social',
                            'type' => 'list',
                            'collapsed' => true,
                            'label' => 'Social Media',
                            'fields' => [
                                '.name' => [
                                    'type' => 'select',
                                    'label' => 'Name',
                                    'options' => [
                                        'facebook' => 'Facebook',
                                        'twitter' => 'Twitter',
                                        'linkedin' => 'LinkedIn',
                                        'instagram' => 'Instagram',
                                        'youtube-play' => 'YouTube'
                                    ]
                                ],
                                '.url' => [
                                    'type' => 'text',
                                    'label' => 'URL',
                                    'placeholder' => 'facebook.com/upkeepmedia'
                                ]
                            ]
                        ]
                    ]
                ],
                'footer' => [
                    'type' => 'section',
                    'title' => 'Footer Section',
                    'fields' => [
                        'footer.links' => [
                            'type' => 'array',
                            'name' => 'links',
                            'placeholder_key' => 'Link Text',
                            'placeholder_value' => 'URL',
                            'label' => 'Links'
                        ],
                        'footer.map' => [
                            'type' => 'textarea',
                            'label' => 'Map Code',
                            'name' => 'map'
                        ]
                    ]
                ]
            ]
        ]
    ]
];
