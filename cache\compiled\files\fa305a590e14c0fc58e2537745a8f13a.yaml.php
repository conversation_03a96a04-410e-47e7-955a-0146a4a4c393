<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/advanced-pagecache/blueprints.yaml',
    'modified' => 1650968348,
    'size' => 3031,
    'data' => [
        'name' => 'AdvancedPageCache',
        'slug' => 'advanced-pagecache',
        'type' => 'plugin',
        'version' => '3.1.1',
        'description' => 'AdvancedPageCache turbo charges your site by statically caching pages.',
        'icon' => 'dashboard',
        'author' => [
            'name' => 'Team Grav',
            'email' => '<EMAIL>',
            'url' => 'http://getgrav.org'
        ],
        'homepage' => 'https://github.com/getgrav/grav-plugin-advanced-pagecache',
        'keywords' => 'static, cache, plugin, performance',
        'bugs' => 'https://github.com/getgrav/grav-plugin-advanced-pagecache/issues',
        'license' => 'MIT',
        'dependencies' => [
            0 => [
                'name' => 'grav',
                'version' => '>=1.7.0'
            ]
        ],
        'form' => [
            'validation' => 'strict',
            'fields' => [
                'enabled' => [
                    'type' => 'toggle',
                    'label' => 'Plugin Status',
                    'highlight' => 1,
                    'default' => 1,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ]
                ],
                'disabled_with_params' => [
                    'type' => 'toggle',
                    'label' => 'Disabled with Params',
                    'highlight' => 1,
                    'default' => 1,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ],
                    'help' => 'Disable caching if there are params set on the URI (eg. /color:blue)'
                ],
                'disabled_with_query' => [
                    'type' => 'toggle',
                    'label' => 'Disabled with Query',
                    'highlight' => 1,
                    'default' => 1,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ],
                    'help' => 'Disable caching if there are query options set on the URI (eg. ?color=blue)'
                ],
                'disabled_on_login' => [
                    'type' => 'toggle',
                    'label' => 'Disabled for logged in users',
                    'highlight' => 1,
                    'default' => 1,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ],
                    'help' => 'Disable caching if the user is logged in'
                ],
                'per_user_caching' => [
                    'type' => 'toggle',
                    'label' => 'Per-user caching',
                    'highlight' => 0,
                    'default' => 0,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ],
                    'help' => 'Enable per-user caching for logged in users (if caching is not disabled for logged in users)'
                ],
                'disabled_extensions' => [
                    'type' => 'selectize',
                    'size' => 'large',
                    'placeholder' => 'e.g. rss, xml, json',
                    'label' => 'Disabled Extensions',
                    'help' => 'URL extensions that should not be cached (e.g. for news feeds)',
                    'classes' => 'fancy',
                    'validate' => [
                        'type' => 'commalist'
                    ]
                ],
                'identifying_header' => [
                    'type' => 'toggle',
                    'label' => 'Identifying Header',
                    'highlight' => 1,
                    'default' => 1,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ],
                    'help' => 'Inject an identifying (X-Grav-APCache) header response tag to identify if the plugin is enabled or not'
                ],
                'whitelist' => [
                    'type' => 'array',
                    'value_only' => true,
                    'label' => 'Whitelist',
                    'help' => 'Enable the plugin on these page paths regardless of other configuration options (don\'t include prefix, but do include extension)',
                    'placeholder_key' => NULL,
                    'placeholder_value' => '/cache-this-route'
                ],
                'blacklist' => [
                    'type' => 'array',
                    'value_only' => true,
                    'label' => 'Blacklist',
                    'help' => 'Disable the plugin when calling these page paths (don\'t include language prefix, but do include extension)',
                    'placeholder_key' => NULL,
                    'placeholder_value' => '/dont-cache-this-route'
                ]
            ]
        ]
    ]
];
