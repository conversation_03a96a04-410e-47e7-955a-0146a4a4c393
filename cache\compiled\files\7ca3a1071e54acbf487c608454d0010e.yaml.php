<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/assets/blueprints.yaml',
    'modified' => 1545400840,
    'size' => 648,
    'data' => [
        'name' => 'Assets',
        'version' => '2.0.1',
        'description' => 'This plugin provides a convenient way to add CSS and JS assets directly from your pages.',
        'icon' => 'list-alt',
        'author' => [
            'name' => 'Team Grav',
            'email' => '<EMAIL>',
            'url' => 'http://getgrav.org'
        ],
        'homepage' => 'https://github.com/getgrav/grav-plugin-assets',
        'demo' => 'http://learn.getgrav.org',
        'keywords' => 'assets, javascript, css, inline',
        'bugs' => 'https://github.com/getgrav/grav-plugin-assets/issues',
        'license' => 'MIT',
        'form' => [
            'validation' => 'strict',
            'fields' => [
                'enabled' => [
                    'type' => 'toggle',
                    'label' => 'Plugin status',
                    'highlight' => 1,
                    'default' => 0,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ]
                ]
            ]
        ]
    ]
];
