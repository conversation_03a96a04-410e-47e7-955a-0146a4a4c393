<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/login/languages/en.yaml',
    'modified' => 1722452911,
    'size' => 13379,
    'data' => [
        'PLUGIN_LOGIN' => [
            'USERNAME' => 'Username',
            'EMAIL' => 'Email',
            'USERNAME_EMAIL' => 'Username/Email',
            'PASSWORD' => 'Password',
            'ACCESS_DENIED' => 'Access denied...',
            'LOGIN_FAILED' => 'Login failed...',
            'LOGIN_SUCCESSFUL' => 'You have been successfully logged in.',
            'BTN_LOGIN' => 'Login',
            'BTN_LOGOUT' => 'Logout',
            'BTN_FORGOT' => 'Forgot',
            'BTN_REGISTER' => 'Register',
            'BTN_SUBMIT_PROFILE' => 'Submit',
            'BTN_RESET' => 'Reset',
            'BTN_RESET_PASSWORD' => 'Reset Password',
            'BTN_SEND_INSTRUCTIONS' => 'Send Reset Instructions',
            'BTN_SUBMIT' => 'Submit',
            'RESET_LINK_EXPIRED' => 'Reset link has expired, please try again',
            'RESET_PASSWORD_RESET' => 'Password has been reset',
            'RESET_INVALID_LINK' => 'Invalid reset link used, please try again',
            'FORGOT_INSTRUCTIONS_SENT_VIA_EMAIL' => 'If an account exists, instructions on resetting your password have been sent via email',
            'FORGOT_FAILED_TO_EMAIL' => 'Failed to email instructions, please try again later',
            'FORGOT_EMAIL_NOT_CONFIGURED' => 'Cannot reset password. This site is not configured to send emails',
            'FORGOT_EMAIL_SUBJECT' => '%s Password Reset Request',
            'FORGOT_EMAIL_BODY' => '<h1>Password Reset</h1><p>Dear %1$s,</p><p>A request was made on <b>%4$s</b> to reset your password.</p><p><br /><a href="%2$s" class="btn-primary">Click this to reset your password</a><br /><br /></p><p>Alternatively, copy the following URL into your browser\'s address bar:</p> <p class="word-break"><a href="%2$s">%2$s</a></p> <p><br />Kind regards,<br /><br />%3$s</p>',
            'SESSION' => '&ldquo;Remember Me&rdquo;-Session',
            'REMEMBER_ME' => 'Remember Me',
            'REMEMBER_ME_HELP' => 'Sets a persistent cookie on your browser to allow persistent-login authentication between sessions.',
            'REMEMBER_ME_STOLEN_COOKIE' => 'Someone else has used your login information to access this page! All sessions were logged out. Please log in with your credentials and check your data.',
            'BUILTIN_CSS' => 'Use built in CSS',
            'BUILTIN_CSS_HELP' => 'Include the CSS provided by the admin plugin',
            'ROUTE' => 'Login route',
            'ROUTE_HELP' => 'Custom route to a custom login page that your theme provides',
            'ROUTE_REGISTER' => 'Registration route',
            'ROUTE_REGISTER_HELP' => 'Route to the registration page. Set this if you want to use the built-in registration page. Leave it empty if you have your own registration form',
            'USERNAME_NOT_VALID' => 'Username should be between 3 and 16 characters, including lowercase letters, numbers, underscores, and hyphens. Uppercase letters, spaces, and special characters are not allowed',
            'USERNAME_NOT_AVAILABLE' => 'Username %s already exists, please pick another username',
            'EMAIL_NOT_AVAILABLE' => 'Email address %s already exists, please pick another email address',
            'PASSWORD_NOT_VALID' => 'Password must contain at least one number and one uppercase and lowercase letter, and at least 8 or more characters',
            'PASSWORDS_DO_NOT_MATCH' => 'Passwords do not match. Double-check you entered the same password twice',
            'USER_NEEDS_EMAIL_FIELD' => 'The user needs an email field',
            'EMAIL_SENDING_FAILURE' => 'An error occurred while sending the email',
            'ACTIVATION_EMAIL_SUBJECT' => 'Activate your account on %s',
            'ACTIVATION_EMAIL_BODY' => '<h1>Account Activation</h1><p>Hi %1$s, </p><p>Your account has been successfully created on <b>%3$s</b>, but you cannot login until it is activated.</p><p><br/><a href="%2$s" class="btn-primary">Activate Your Account Now</a><br/><br/></p><p>Alternatively, copy the following URL into your browser\'s address bar:</p><p class="word-break"><a href="%2$s">%2$s</a></p><p><br/>Kind regards,<br/><br/>%4$s</p>',
            'ACTIVATION_NOTICE_MSG' => 'Hi %s, your account is created, please check your email to fully activate it',
            'WELCOME_EMAIL_SUBJECT' => 'Welcome to %s',
            'WELCOME_EMAIL_BODY' => '<h1>Account Created</h1><p>Hi %1$s, </p><p>Your account has been successfully created on <b>%3$s</b>.</p><p><br/><a href="%2$s" class="btn-primary">Login Now</a><br/><br/></p><p>Alternatively, copy the following URL into your browser\'s address bar:</p><p class="word-break"><a href="%2$s">%2$s</a></p><p><br/>Kind regards,<br/><br/>%4$s</p>',
            'WELCOME_NOTICE_MSG' => 'Hi %s, your account has been successfully created',
            'NOTIFICATION_EMAIL_SUBJECT' => 'New user on %s',
            'NOTIFICATION_EMAIL_BODY' => '<h1>New User</h1><p>Hi, a new user registered on %1$s.</p><p><ul><li>Username: <b>%2$s</b></li><li>Email: <b>%3$s</b></ul><p><p><br/><a href="%4$s" class="btn-primary">Visit %1$s</a><br/><br/></p>',
            'EMAIL_FOOTER' => 'GetGrav.org',
            'ACTIVATION_LINK_EXPIRED' => 'Activation link expired',
            'USER_ACTIVATED_SUCCESSFULLY' => 'User account activated successfully',
            'USER_ACTIVATED_SUCCESSFULLY_NOT_ENABLED' => 'User account activated but account is being reviewed',
            'INVALID_REQUEST' => 'Invalid request',
            'USER_REGISTRATION' => 'User Registration',
            'USER_REGISTRATION_ENABLED_HELP' => 'Enable the user registration',
            'VALIDATE_PASSWORD1_AND_PASSWORD2' => 'Validate double entered password',
            'VALIDATE_PASSWORD1_AND_PASSWORD2_HELP' => 'Validate and compare two different fields for the passwords, named `password1` and `password2`. Enable this if you have two password fields in the registration form',
            'SET_USER_DISABLED' => 'Set the user as disabled',
            'SET_USER_DISABLED_HELP' => 'Best used along with the `Send activation email` email. Adds the user to Grav, but sets it as disabled',
            'LOGIN_AFTER_REGISTRATION' => 'Login the user after registration',
            'LOGIN_AFTER_REGISTRATION_HELP' => 'Immediately login the user after the registration. If email activation is required, the user will be logged in immediately after activating the account',
            'SEND_ACTIVATION_EMAIL' => 'Send activation email',
            'SEND_ACTIVATION_EMAIL_HELP' => 'Sends an email to the user to activate his account. Enable the `Set the user as disabled` option when using this feature, so the user will be set as disabled and an email will be sent to activate the account',
            'SEND_NOTIFICATION_EMAIL' => 'Send notification email',
            'SEND_NOTIFICATION_EMAIL_HELP' => 'Notifies the site admin that a new user has registered. The email will be sent to the `To` field in the Email Plugin configuration',
            'SEND_WELCOME_EMAIL' => 'Send welcome email',
            'SEND_WELCOME_EMAIL_HELP' => 'Sends an email to the newly registered user',
            'DEFAULT_VALUES' => 'Default values',
            'DEFAULT_VALUES_HELP' => 'List of field names and values associated, that will be added to the user profile (yaml file) by default, without being configurable by the user. Separate multiple values with a comma, with no spaces between the values',
            'ADDITIONAL_PARAM_KEY' => 'Parameter',
            'ADDITIONAL_PARAM_VALUE' => 'Value',
            'REGISTRATION_FIELDS' => 'Registration fields',
            'REGISTRATION_FIELDS_HELP' => 'Add the fields that will be added to the user Yaml file. Fields not listed here will not be added even if present in the registration form',
            'REGISTRATION_FIELD_KEY' => 'Field name',
            'REDIRECT_TO_LOGIN' => 'Redirect to login',
            'REDIRECT_TO_LOGIN_HELP' => 'Should user be redirected to a login page, or display the login at the current route?',
            'REDIRECT_AFTER_LOGIN' => 'Redirect after login',
            'REDIRECT_AFTER_LOGIN_HELP' => 'Should the user be redirect to a specific route after login?',
            'ROUTE_AFTER_LOGIN' => 'After login route',
            'ROUTE_AFTER_LOGIN_HELP' => 'Custom route to redirect after login',
            'REDIRECT_AFTER_LOGOUT' => 'Redirect after logout',
            'REDIRECT_AFTER_LOGOUT_HELP' => 'Should the user be redirected to a specific route after logout?',
            'ROUTE_AFTER_LOGOUT' => 'After logout route',
            'ROUTE_AFTER_LOGOUT_HELP' => 'Custom route to redirect after logout',
            'REDIRECT_AFTER_REGISTRATION' => 'After registration route',
            'REDIRECT_AFTER_REGISTRATION_HELP' => 'Custom route to redirect after the registration',
            'OPTIONS' => 'Options',
            'EMAIL_VALIDATION_MESSAGE' => 'Must be a valid email address',
            'PASSWORD_VALIDATION_MESSAGE' => 'Password must contain at least one number and one uppercase and lowercase letter, and at least 8 or more characters',
            'TIMEOUT_HELP' => 'Sets the session timeout in seconds when Remember Me is enabled and checked by the user. Minimum is 604800 which means 1 week',
            'GROUPS_HELP' => 'List of groups the new registered user will be part of, if any',
            'SITE_ACCESS_HELP' => 'List of site access levels the new registered user will have. Example: `login` -> `true` ',
            'WELCOME' => 'Welcome',
            'REDIRECT_AFTER_ACTIVATION' => 'After user activation route',
            'REDIRECT_AFTER_ACTIVATION_HELP' => 'Used if the user is required to activate the account via email. Once activated, this route will be shown',
            'REGISTRATION_DISABLED' => 'Registration disabled',
            'USE_PARENT_ACL_LABEL' => 'Use parent access rules',
            'USE_PARENT_ACL_HELP' => 'Check for parent access rules if no rules are defined',
            'PROTECT_PROTECTED_PAGE_MEDIA_LABEL' => 'Protect a login-protected page media',
            'PROTECT_PROTECTED_PAGE_MEDIA_HELP' => 'If enabled, media of a login protected page is login protected as well and cannot be seen unless logged in',
            'SECURITY_TAB' => 'Security',
            'MAX_RESETS_COUNT' => 'Max password resets count',
            'MAX_RESETS_COUNT_HELP' => 'Password reset flood protection setting (0 - not limited)',
            'MAX_RESETS_INTERVAL' => 'Max password resets interval',
            'MAX_RESETS_INTERVAL_HELP' => 'The time interval for the max password resets count value',
            'FORGOT_CANNOT_RESET_IT_IS_BLOCKED' => 'Cannot reset password for %s, password reset functionality temporarily blocked, please try later (maximum %s minutes)',
            'MAX_LOGINS_COUNT' => 'Max logins count',
            'MAX_LOGINS_COUNT_HELP' => 'Flood protection setting (0 - not limited)',
            'MAX_LOGINS_INTERVAL' => 'Max logins interval',
            'MAX_LOGINS_INTERVAL_HELP' => 'The time interval for the login count value',
            'TOO_MANY_LOGIN_ATTEMPTS' => 'Too many failed login attempted in the configured time (%s minutes)',
            'SECONDS' => 'seconds',
            'MINUTES' => 'minutes',
            'RESETS' => 'resets',
            'ATTEMPTS' => 'attempts',
            'ROUTES' => 'Routes',
            'ROUTE_FORGOT' => 'Forgot password route',
            'ROUTE_RESET' => 'Reset password route',
            'ROUTE_PROFILE' => 'User profile route',
            'ROUTE_ACTIVATE' => 'User activation route',
            'LOGGED_OUT' => 'You have been successfully logged out...',
            'PAGE_RESTRICTED' => 'Access is restricted, please login...',
            'DYNAMIC_VISIBILITY' => 'Dynamic Page Visibility',
            'DYNAMIC_VISIBILITY_HELP' => 'Allows dynamic processing of page visibility base on access rules if login.visibility_requires_access is set to true on a page',
            'USER_IS_REMOTE_ONLY' => 'This user authenticated with a remote service, so profile cannot be saved',
            '2FA_TITLE' => '2-Factor Authentication',
            '2FA_INSTRUCTIONS' => '##### 2-Factor Authentication
You have **2FA** enabled on this account. Please use your **2FA** app to enter the current **6-digit code** to complete the login process.',
            '2FA_REGEN_HINT' => 'Regenerating the secret will require you to update your authenticator app',
            '2FA_FAILED' => 'Invalid 2-Factor Authentication code, please try again...',
            '2FA_ENABLED' => '2FA Enabled',
            '2FA_ENABLED_HELP' => 'Enables 2-Factor Authentication for all users',
            '2FA_CODE_INPUT' => '000000',
            '2FA_SECRET' => '2FA Secret',
            '2FA_SECRET_HELP' => 'Scan this QR code into your [Authenticator App](https://learn.getgrav.org/admin-panel/2fa#apps). Also it\'s a good idea to backup the secret in a safe location, in case you need to reinstall your app. Check the [Grav docs](https://learn.getgrav.org/admin-panel/2fa) for more information ',
            '2FA_REGENERATE' => 'Regenerate',
            'BTN_CANCEL' => 'Cancel',
            'MANUALLY_ENABLE' => 'Manually Enable',
            'MANUALLY_ENABLE_HELP' => 'When using \'activation email\' and \'notification email\', you can ensure the user can self activate, but requires manually enabling the user to login',
            'IPV6_SUBNET_SIZE' => 'IPv6 Subnet Size',
            'IPV6_SUBNET_SIZE_HELP' => 'The number of IPv6 addresses typically assigned to a machine',
            'PROFILE_UPDATED' => 'Your profile has been updated',
            'ENTER_EMAIL' => 'Enter your email',
            'ENTER_NEW_PASSWORD' => 'Enter new password',
            'ENTER_PASSWORD' => 'Enter a password',
            'ENTER_PASSWORD_AGAIN' => 'Enter the password again',
            'REGISTRATION_THANK_YOU' => 'Thank you for registering.',
            'USER_ACCOUNT_DISABLED' => 'Your user account is disabled or it has not yet been activated.',
            'SESSION_USER_SYNC' => 'Sync User in Session',
            'SESSION_USER_SYNC_HELP' => 'If Enabled, user in the session is kept in sync with the stored user file. WARNING: This feature may break some existing plugins which update user object in the session without saving it to the filesystem using `$user->save()` method.',
            'PLUGIN_LOGIN_DISABLED' => 'Login plugin has been disabled',
            'USER_REGISTRATION_DISABLED' => 'User registration has been disabled',
            'INVITATION_EMAIL_SUBJECT' => 'You have been invited to join %s',
            'INVITATION_EMAIL_BODY' => '<h1>Account Invitation</h1><p>Hi, </p><p>You have been invited to join <b>%1$s</b>.</p><p>%2$s</p><p><br/><a href="%3$s" class="btn-primary">Create Your Account Now</a><br/><br/></p><p>Alternatively, copy the following URL into your browser\'s address bar:</p><p class="word-break"><a href="%3$s">%3$s</a></p><p><br/>Kind regards,<br/><br/>%4$s</p>',
            'INVITATION_EMAIL_MESSAGE' => 'We welcome you to register an account to on site.',
            'INVALID_INVITE_EMAILS' => '<strong>Error:</strong> An invalid list of emails was provided',
            'INVALID_FORM' => '<strong>Error:</strong> Invalid form',
            'FAILED_TO_SEND_EMAILS' => 'Failed to send emails to: %s',
            'HOST_WARNING' => '<div style="background-color: #FFEDAD; color: #725F1C; border: 1px solid #FFD74E; padding: 10px; margin: 10px 0; border-radius: 5px;">NOTE: If you did not initiate this email or you don\'t recognize the originating site: <strong>"%s"</strong> please ignore or delete this email.</div>',
            'SITE_HOST' => 'Site Host',
            'SITE_HOST_HELP' => 'For extra security, force this URL to be used in all password reset and activation emails. Leave empty to use the default site URL'
        ]
    ]
];
