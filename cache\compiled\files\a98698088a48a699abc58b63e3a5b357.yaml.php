<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/admin/languages/tr.yaml',
    'modified' => 1730089692,
    'size' => 51983,
    'data' => [
        'PLUGIN_ADMIN' => [
            'ADMIN_NOSCRIPT_MSG' => 'Lütfen tarayıcınızda Javascript aktive edin.',
            'ADMIN_BETA_MSG' => 'Bu bir beta sürümüdür! Sunucunuzda kullanmak sizin sorumluluğunuzdadır...',
            'ADMIN_REPORT_ISSUE' => 'Hata mı var? Lütfen GitHub aracılığıyla bildirin.',
            'LOGIN_BTN' => 'Giriş',
            'LOGIN_BTN_FORGOT' => 'Şifremi Unuttum',
            'LOGIN_BTN_RESET' => '<PERSON><PERSON>reni <PERSON>ırl<PERSON>',
            'LOGIN_BTN_SEND_INSTRUCTIONS' => 'Sıfırlama Yönergesini Gönder',
            'LOGIN_BTN_CLEAR' => 'Formu Temizle',
            'LOGIN_BTN_CREATE_USER' => 'Kullanıcı Oluştur',
            'LOGIN_LOGGED_IN' => 'Başarıyla oturum açtınız',
            'LOGIN_FAILED' => 'Giriş başarısız',
            'LOGGED_OUT' => 'Oturum kapatıldı',
            'RESET_NEW_PASSWORD' => 'Lütfen yeni bir parola girin &hellip;',
            'RESET_LINK_EXPIRED' => 'Sıfırlama bağlantısının süresi doldu, lütfen tekrar deneyin',
            'RESET_PASSWORD_RESET' => 'Şifreniz sıfırlandı',
            'RESET_INVALID_LINK' => 'Hatalı sıfırlama bağlantısı, lütfen tekrar deneyin',
            'FORGOT_INSTRUCTIONS_SENT_VIA_EMAIL' => 'Şifre sıfırlama yönergesi %s adresine gönderilmiştir',
            'FORGOT_FAILED_TO_EMAIL' => 'Eposta yönergeleri başarısız, lütfen daha sonra tekrar deneyin',
            'FORGOT_CANNOT_RESET_EMAIL_NO_EMAIL' => '%s: şifre sıfırlanamıyor, eposta adresi atanmamış',
            'FORGOT_USERNAME_DOES_NOT_EXIST' => '<b>%s</b> adlı kullanıcı mevcut değil',
            'FORGOT_EMAIL_NOT_CONFIGURED' => 'Şifre sıfırlaması gönderilemiyor. Eposta sistemi yapılandırılmadı',
            'FORGOT_EMAIL_SUBJECT' => '%s Şifre Sıfırlama İsteği',
            'FORGOT_EMAIL_BODY' => '<h1>Şifre Sıfırlama</h1><p>Sayın %1$s,</p><p><b>%4$s</b> tarafından şifre sıfırlama isteği oluşturulmuştur.</p><p><br /><a href="%2$s" class="btn-primary">Şifrenizi sıfırlamak için bu bağlantıya tıklayın</a><br /><br /></p><p>Alternatif olarak, şu url\'yi tarayıcınızın adres çubuğuna kopyalayabilirsiniz:</p> <p>%2$s</p><p><br />Saygılarımızla,<br /><br />%3$s</p>',
            'MANAGE_PAGES' => 'Sayfaları Yönet',
            'PAGES' => 'Sayfalar',
            'PLUGINS' => 'Eklentiler',
            'PLUGIN' => 'Eklenti',
            'THEMES' => 'Temalar',
            'LOGOUT' => 'Çıkış',
            'BACK' => 'Geri',
            'NEXT' => 'Sonraki',
            'PREVIOUS' => 'Önceki',
            'ADD_PAGE' => 'Sayfa Ekle',
            'MOVE' => 'Taşı',
            'DELETE' => 'Sil',
            'UNSET' => 'Devre dışı bırak',
            'VIEW' => 'Görünüm',
            'SAVE' => 'Kaydet',
            'NORMAL' => 'Normal',
            'EXPERT' => 'Uzman',
            'EXPAND_ALL' => 'Tümünü Genişlet',
            'COLLAPSE_ALL' => 'Tümünü Daralt',
            'ERROR' => 'Hata',
            'CLOSE' => 'Kapat',
            'CANCEL' => 'İptal',
            'CONTINUE' => 'Devam',
            'CONFIRM' => 'Onayla',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_TITLE' => 'Doğrulama Gerekli',
            'MODAL_CHANGED_DETECTED_TITLE' => 'Değişiklikler Tespit Edildi',
            'MODAL_CHANGED_DETECTED_DESC' => 'Kaydedilmemiş değişiklikler var. Kaydetmeden çıkmak istediğinizden emin misiniz?',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_TITLE' => 'Doğrulama Gerekli',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_DESC' => 'Bu dosyayı silmek istiyor musunuz? Bu işlem geri alınamaz.',
            'MODAL_UPDATE_GRAV_CONFIRMATION_REQUIRED_DESC' => 'Grav için yeni bir güncelleme tespit edildi. Güncellemek ister misiniz?',
            'ADD_FILTERS' => 'Filtre Ekle',
            'SEARCH_PAGES' => 'Sayfaları Ara',
            'VERSION' => 'Sürüm',
            'WAS_MADE_WITH' => 'Yapan :',
            'BY' => 'tarafından',
            'UPDATE_THEME' => 'Temayı Güncelle',
            'UPDATE_PLUGIN' => 'Eklentiyi Güncelle',
            'OF_THIS_THEME_IS_NOW_AVAILABLE' => 'bu tema artık kullanılabilir',
            'OF_THIS_PLUGIN_IS_NOW_AVAILABLE' => 'bu eklenti artık kullanılabilir',
            'AUTHOR' => 'Yazar',
            'HOMEPAGE' => 'Anasayfa',
            'DEMO' => 'Demo',
            'BUG_TRACKER' => 'Hata Takipçisi',
            'KEYWORDS' => 'Anahtar Kelimeler',
            'LICENSE' => 'Lisans',
            'DESCRIPTION' => 'Açıklama',
            'README' => 'Benioku',
            'DOCS' => 'Dökümanlar',
            'REMOVE_THEME' => 'Temayı Kaldır',
            'INSTALL_THEME' => 'Tema Yükle',
            'THEME' => 'Tema',
            'BACK_TO_THEMES' => 'Temalara geri dön',
            'BACK_TO_PLUGINS' => 'Eklentilere geri dön',
            'CHECK_FOR_UPDATES' => 'Güncelleştirmeleri Denetle',
            'ADD' => 'Ekle',
            'CLEAR_CACHE' => 'Önbelleği Temizle',
            'CLEAR_CACHE_ALL_CACHE' => 'Tüm Önbellek',
            'CLEAR_CACHE_ASSETS_ONLY' => 'Yalnızca Malzemeler',
            'CLEAR_CACHE_IMAGES_ONLY' => 'Yalnızca Resimler',
            'CLEAR_CACHE_CACHE_ONLY' => 'Yalnızca Önbellek',
            'CLEAR_CACHE_TMP_ONLY' => 'Sadece geçici dosyalar',
            'UPDATES_AVAILABLE' => 'Güncelleştirmeler Var',
            'DAYS' => 'Gün',
            'UPDATE' => 'Güncelle',
            'BACKUP' => 'Yedekle',
            'BACKUPS' => 'Yedeklemeler',
            'BACKUP_NOW' => 'Şimdi Yedekle',
            'BACKUPS_STATS' => 'Yedekleme İstatistikleri',
            'BACKUPS_HISTORY' => 'Yedekleme Geçmişi',
            'BACKUPS_PURGE_CONFIG' => 'Yedek Temizleme Yapılandırması',
            'BACKUPS_PROFILES' => 'Yedekleme Profilleri',
            'BACKUPS_COUNT' => 'Yedekleme sayısı',
            'BACKUPS_PROFILES_COUNT' => 'Profil sayısı',
            'BACKUPS_TOTAL_SIZE' => 'Kullanılan alan',
            'BACKUPS_NEWEST' => 'En yeni yedekleme',
            'BACKUPS_OLDEST' => 'En eski yedekleme',
            'BACKUPS_PURGE' => 'Temizle',
            'BACKUPS_NOT_GENERATED' => 'Henüz bir yedekleme oluşturulmadı...',
            'BACKUPS_PURGE_NUMBER' => '%s kullanılıyor, %s toplam yedek alanı',
            'BACKUPS_PURGE_TIME' => '%s günlük yedek kaldı',
            'BACKUPS_PURGE_SPACE' => '%s kullanılıyor, toplam %s',
            'BACKUP_DELETED' => 'Yedekleme Başarıyla Silindi',
            'BACKUP_NOT_FOUND' => 'Yedekleme dosyası bulunamadı',
            'BACKUP_DATE' => 'Yedekleme Tarihi',
            'STATISTICS' => 'İstatistikler',
            'VIEWS_STATISTICS' => 'Sayfa Görüntüleme İstatistikleri',
            'TODAY' => 'Bugün',
            'WEEK' => 'Hafta',
            'MONTH' => 'Ay',
            'LATEST_PAGE_UPDATES' => 'Son Sayfa Güncellemeleri',
            'MAINTENANCE' => 'Bakım',
            'UPDATED' => 'Güncelleştirildi',
            'MON' => 'Pzt',
            'TUE' => 'Sal',
            'WED' => 'Çrş',
            'THU' => 'Prş',
            'FRI' => 'Cum',
            'SAT' => 'Cmt',
            'SUN' => 'Pzr',
            'COPY' => 'Kopyala',
            'EDIT' => 'Düzenle',
            'CREATE' => 'Oluştur',
            'GRAV_ADMIN' => 'Grav Yönetim Paneli',
            'GRAV_OFFICIAL_PLUGIN' => 'Resmi Grav Eklentisi',
            'GRAV_OFFICIAL_THEME' => 'Resmi Grav Teması',
            'PLUGIN_SYMBOLICALLY_LINKED' => 'Bu eklenti sembolik olarak bağlıdır. Güncelleştirmeler fark edilmeyecektir.',
            'THEME_SYMBOLICALLY_LINKED' => 'Bu tema sembolik olarak bağlıdır. Güncelleştirmeler fark edilmeyecektir',
            'REMOVE_PLUGIN' => 'Eklentiyi Kaldır',
            'INSTALL_PLUGIN' => 'Eklenti Yükle',
            'AVAILABLE' => 'Mevcut',
            'INSTALLED' => 'Yüklü',
            'INSTALL' => 'Yükle',
            'ACTIVE_THEME' => 'Aktif Tema',
            'SWITCHING_TO' => 'Şuna değiştir',
            'SWITCHING_TO_DESCRIPTION' => 'Farklı bir temaya geçerek, tüm düzen sayfalarını, potansiyel olarak değişebileceğinin onayını vermiş olursunuz.',
            'SWITCHING_TO_CONFIRMATION' => 'Temayı değiştirmek istiyor musunuz',
            'CREATE_NEW_USER' => 'Yeni Kullanıcı Oluştur',
            'REMOVE_USER' => 'Kullanıcıyı Sil',
            'ACCESS_DENIED' => 'Erişim reddedildi',
            'ACCOUNT_NOT_ADMIN' => 'hesabınız yönetici yetkilerine sahip değil',
            'PHP_INFO' => 'PHP Bilgisi',
            'INSTALLER' => 'Yükleyici',
            'AVAILABLE_THEMES' => 'Mevcut Temalar',
            'AVAILABLE_PLUGINS' => 'Mevcut Eklentiler',
            'INSTALLED_THEMES' => 'Yüklü Temalar',
            'INSTALLED_PLUGINS' => 'Yüklü Eklentiler',
            'BROWSE_ERROR_LOGS' => 'Hata Kayıtlarına Gözat',
            'SITE' => 'Web sitesi',
            'INFO' => 'Bilgi',
            'SYSTEM' => 'Sistem',
            'USER' => 'Kullanıcı',
            'ADD_ACCOUNT' => 'Hesap Ekle',
            'SWITCH_LANGUAGE' => 'Dili Değiştir',
            'SUCCESSFULLY_ENABLED_PLUGIN' => 'Eklenti başarıyla etkinleştirildi',
            'SUCCESSFULLY_DISABLED_PLUGIN' => 'Eklenti devre dışı bırakıldı',
            'SUCCESSFULLY_CHANGED_THEME' => 'Varsayılan tema başarıyla değiştirildi',
            'INSTALLATION_FAILED' => 'Kurulum Başarısız',
            'INSTALLATION_SUCCESSFUL' => 'Kurulum Başarılı',
            'UNINSTALL_FAILED' => 'Kaldırma Başarısız',
            'UNINSTALL_SUCCESSFUL' => 'Kaldırma Başarılı',
            'SUCCESSFULLY_SAVED' => 'Başarıyla kaydedildi',
            'SUCCESSFULLY_COPIED' => 'Başarıyla kopyalandı',
            'REORDERING_WAS_SUCCESSFUL' => 'Sıralama başarılı',
            'SUCCESSFULLY_DELETED' => 'Başarıyla silindi',
            'SUCCESSFULLY_SWITCHED_LANGUAGE' => 'Dil başarıyla değiştirildi',
            'INSUFFICIENT_PERMISSIONS_FOR_TASK' => 'Bu görev için izniniz yok',
            'CACHE_CLEARED' => 'Önbellek temizlendi',
            'METHOD' => 'Yöntem',
            'ERROR_CLEARING_CACHE' => 'Önbellek temizleme hatası',
            'AN_ERROR_OCCURRED' => 'Bir hata meydana geldi',
            'YOUR_BACKUP_IS_READY_FOR_DOWNLOAD' => 'Yedeğiniz indirilmeye hazır',
            'DOWNLOAD_BACKUP' => 'Yedeği indir',
            'PAGES_FILTERED' => 'Filtrelenen Sayfalar',
            'NO_PAGE_FOUND' => 'Sayfa bulunamadı',
            'INVALID_PARAMETERS' => 'Hatalı Parametreler',
            'NO_FILES_SENT' => 'Dosya gönderilmedi',
            'EXCEEDED_FILESIZE_LIMIT' => 'PHP dosya boyutu sınırı aşıldı',
            'EXCEEDED_POSTMAX_LIMIT' => 'PHP yapılandırması aşıldı post_max_size',
            'UNKNOWN_ERRORS' => 'Bilinmeyen hatalar',
            'EXCEEDED_GRAV_FILESIZE_LIMIT' => 'Grav yapılandırma dosyasındaki boyut sınırı aşıldı',
            'UNSUPPORTED_FILE_TYPE' => 'Desteklenmeyen dosya formatı',
            'FAILED_TO_MOVE_UPLOADED_FILE' => 'Yüklenen dosya taşınamadı',
            'FILE_UPLOADED_SUCCESSFULLY' => 'Dosya başarıyla yüklendi',
            'FILE_DELETED' => 'Dosya silindi',
            'FILE_COULD_NOT_BE_DELETED' => 'Dosya silinemedi',
            'FILE_NOT_FOUND' => 'Dosya bulunamadı',
            'NO_FILE_FOUND' => 'Hiçbir dosya bulunamadı',
            'GRAV_WAS_SUCCESSFULLY_UPDATED_TO' => 'Grav başarıyla güncellendi, sürüm',
            'GRAV_UPDATE_FAILED' => 'Grav güncellemesi başarısız',
            'EVERYTHING_UPDATED' => 'Herşey güncellendi',
            'UPDATES_FAILED' => 'Güncellemeler başarısız',
            'AVATAR_BY' => 'Avatar sahibi :',
            'AVATAR_UPLOAD_OWN' => 'Ya da kendiniz bir dosya yükleyin...',
            'LAST_BACKUP' => 'Son Yedek',
            'FULL_NAME' => 'Tam isim',
            'USERNAME' => 'Kullanıcı Adı',
            'EMAIL' => 'Eposta Adresi',
            'USERNAME_EMAIL' => 'Kullanıcı Adı veya E-Posta',
            'PASSWORD' => 'Şifre',
            'PASSWORD_CONFIRM' => 'Parolayı yeniden girin',
            'TITLE' => 'Başlık',
            'ACCOUNT' => 'Hesap',
            'EMAIL_VALIDATION_MESSAGE' => 'Geçerli bir eposta adresi belirtilmeli',
            'PASSWORD_VALIDATION_MESSAGE' => 'Parola en az bir sayı, bir büyük harf, bir küçük harf ve en az 8 veya daha fazla karakterden oluşmalıdır',
            'LANGUAGE' => 'Dil',
            'LANGUAGE_HELP' => 'Favori dili belirleyin',
            'MEDIA' => 'Ortam',
            'DEFAULTS' => 'Varsayılanlar',
            'SITE_TITLE' => 'Site Başlığı',
            'SITE_TITLE_PLACEHOLDER' => 'Başlığı bütün sayfalara uygula',
            'SITE_TITLE_HELP' => 'Sitenizin varsayılan başlığı, çoğunlukla temalarda kullanılır',
            'SITE_DEFAULT_LANG' => 'Varsayılan Dil',
            'SITE_DEFAULT_LANG_PLACEHOLDER' => 'Temanın <HTML> etiketinde kullanılacak varsayılan dil',
            'SITE_DEFAULT_LANG_HELP' => 'Temanın <HTML> etiketinde kullanılacak varsayılan dil',
            'DEFAULT_AUTHOR' => 'Varsayılan Yazar',
            'DEFAULT_AUTHOR_HELP' => 'Varsayılan yazar adı, genellikle temalarda ya da sayfa içeriklerinde kullanılır',
            'DEFAULT_EMAIL' => 'Varsayılan Eposta',
            'DEFAULT_EMAIL_HELP' => 'Temalar ya da sayfalar için referans olacak varsayılan email adresi',
            'TAXONOMY_TYPES' => 'Taksonomi Tipleri',
            'TAXONOMY_TYPES_HELP' => 'Taksonomi türlerini sayfalarda kullanmak istiyorsanız, burada tanımlanmış olması gerekmektedir',
            'PAGE_SUMMARY' => 'Sayfa Özeti',
            'ENABLED' => 'Etkin',
            'ENABLED_HELP' => 'Sayfa özetini etkinleştir (sayfa özeti, sayfa içeriği ile aynı değeri döndürür)',
            'YES' => 'Evet',
            'NO' => 'Hayır',
            'SUMMARY_SIZE' => 'Özet Boyutu',
            'SUMMARY_SIZE_HELP' => 'İçerik özeti olarak kullanılmak üzere bir sayfadaki karakterlerin toplam sayısı',
            'FORMAT' => 'Biçim',
            'FORMAT_HELP' => 'short = use the first occurrence of delimiter or size; long = summary delimiter will be ignored',
            'SHORT' => 'Kısa',
            'LONG' => 'Uzun',
            'DELIMITER' => 'Ayraç',
            'DELIMITER_HELP' => 'Özet ayracı (varsayılan \'===\')',
            'METADATA' => 'Meta verisi',
            'METADATA_HELP' => 'Sayfa tarafından geçersiz kılınmadığı sürece her sayfada görüntülenen varsayılan meta veri değerleri',
            'NAME' => 'Ad(name)',
            'CONTENT' => 'İçerik',
            'SIZE' => 'Boyut',
            'ACTION' => 'Eylem',
            'REDIRECTS_AND_ROUTES' => 'Yönlendirmeler & Yollar',
            'CUSTOM_REDIRECTS' => 'Özel Yönlendirmeler',
            'CUSTOM_REDIRECTS_HELP' => 'routes to redirect to other pages. Standard Regex replacement is valid',
            'CUSTOM_REDIRECTS_PLACEHOLDER_KEY' => '/takma/adin',
            'CUSTOM_REDIRECTS_PLACEHOLDER_VALUE' => '/yonlendirme/adresin',
            'CUSTOM_ROUTES' => 'Özel Yollar',
            'CUSTOM_ROUTES_HELP' => 'routes to alias to other pages. Standard Regex replacement is valid',
            'CUSTOM_ROUTES_PLACEHOLDER_KEY' => '/takma/ad',
            'CUSTOM_ROUTES_PLACEHOLDER_VALUE' => '/ozel/yol',
            'FILE_STREAMS' => 'Dosya Akışları',
            'DEFAULT' => 'Varsayılan',
            'PAGE_MEDIA' => 'Sayfa İçeriği',
            'OPTIONS' => 'Ayarlar',
            'PUBLISHED' => 'Yayınlandı',
            'PUBLISHED_HELP' => 'By default, a page is published unless you explicitly set published: false or via a publish_date being in the future, or unpublish_date in the past',
            'DATE' => 'Tarih',
            'DATE_HELP' => 'The date variable allows you to specifically set a date associated with this page.',
            'PUBLISHED_DATE' => 'Yayınlanma Tarihi',
            'PUBLISHED_DATE_HELP' => 'Can provide a date to automatically trigger publication.',
            'UNPUBLISHED_DATE' => 'Yayından Kaldırma Tarihi',
            'UNPUBLISHED_DATE_HELP' => 'Can provide a date to automatically trigger un-publication.',
            'ROBOTS' => 'Robot',
            'TAXONOMIES' => 'Bölümlendirmeler(taxonomy)',
            'TAXONOMY' => 'Taksonomi',
            'ADVANCED' => 'Gelişmiş',
            'SETTINGS' => 'Ayarlar',
            'FOLDER_NUMERIC_PREFIX' => 'Nümerik Klasör Öneki',
            'FOLDER_NUMERIC_PREFIX_HELP' => 'Numeric prefix that provides manual ordering and implies visibility',
            'FOLDER_NAME' => 'Klasör Adı',
            'FOLDER_NAME_HELP' => 'Bu sayfa için dosya sisteminde saklanacak klasörün adı',
            'PARENT' => 'Ebeveyn',
            'DEFAULT_OPTION_ROOT' => '- Kök -',
            'DEFAULT_OPTION_SELECT' => '- Seçiniz -',
            'DISPLAY_TEMPLATE' => 'Şablonu Görüntüle',
            'DISPLAY_TEMPLATE_HELP' => 'The page type that translates into which twig template renders the page',
            'ORDERING' => 'Sıralama',
            'PAGE_ORDER' => 'Sayfa Sıralaması',
            'OVERRIDES' => 'Özelleştirmeler',
            'MENU' => 'Menü',
            'MENU_HELP' => 'The string to be used in a menu.  If not set, Title will be used.',
            'SLUG' => 'Kısa Url',
            'SLUG_HELP' => 'The slug variable allows you to specifically set the page\'s portion of the URL',
            'SLUG_VALIDATE_MESSAGE' => 'A slug must contain only lowercase alphanumeric characters and dashes',
            'PROCESS' => 'İşle',
            'PROCESS_HELP' => 'Control how pages are processed. Can be set per-page rather than globally',
            'DEFAULT_CHILD_TYPE' => 'Default Child Type',
            'USE_GLOBAL' => 'Genel Değeri Kullan',
            'ROUTABLE' => 'Yönlendirilebilir',
            'ROUTABLE_HELP' => 'Bu sayfa bir URL tarafından erişilebilir olursa',
            'CACHING' => 'Önbellekleme',
            'VISIBLE' => 'Görünür',
            'VISIBLE_HELP' => 'Bir sayfanın navigasyonda görünüp görünmeyeceğini belirler.',
            'DISABLED' => 'Devre Dışı',
            'ITEMS' => 'Ögeler',
            'ORDER_BY' => 'Sıralama Ölçütü',
            'ORDER' => 'Sırala',
            'FOLDER' => 'Klasör',
            'ASCENDING' => 'Artan',
            'DESCENDING' => 'Azalan',
            'PAGE_TITLE' => 'Sayfa Başlığı',
            'PAGE_TITLE_HELP' => 'Sayfanın başlığı',
            'PAGE' => 'Sayfa',
            'FRONTMATTER' => 'Frontmatter',
            'FILENAME' => 'Dosyaadı',
            'PARENT_PAGE' => 'Ebeveyn Sayfa',
            'HOME_PAGE' => 'Ana Sayfa',
            'HOME_PAGE_HELP' => 'Grav\'in varsayılan açılış sayfası olarak kullanacağı sayfa',
            'DEFAULT_THEME' => 'Varsayılan Tema',
            'DEFAULT_THEME_HELP' => 'Grav\'in kullanacağı varsayılan şablonu ayarlayın (varsayılan Antimadde)',
            'TIMEZONE' => 'Zaman Dilimi',
            'TIMEZONE_HELP' => 'Sunucunun varsayılan saat dilimini geçersiz kıl',
            'SHORT_DATE_FORMAT' => 'Kısa Tarih Görünümü',
            'SHORT_DATE_FORMAT_HELP' => 'Temalar tarafında kullanılabilecek kısa tarih formatını ayarlayın',
            'LONG_DATE_FORMAT' => 'Uzun Tarih Görünümü',
            'LONG_DATE_FORMAT_HELP' => 'Temalar tarafında kullanılabilecek uzun tarih formatını ayarlayın',
            'DEFAULT_ORDERING' => 'Varsayılan sıralama',
            'DEFAULT_ORDERING_HELP' => 'Listede yer alan sayfalar geçersiz kılınmadıkça, bu sıralamaya göre oluşturulacaktır',
            'DEFAULT_ORDERING_DEFAULT' => 'Varsayılan - klasör adına göre',
            'DEFAULT_ORDERING_FOLDER' => 'Klasör - klasör önekine göre',
            'DEFAULT_ORDERING_TITLE' => 'Başlık - başlık alanına göre',
            'DEFAULT_ORDERING_DATE' => 'Tarih - tarihe göre',
            'DEFAULT_ORDER_DIRECTION' => 'Varsayılan sıralama yönü',
            'DEFAULT_ORDER_DIRECTION_HELP' => 'The direction of pages in a list',
            'DEFAULT_PAGE_COUNT' => 'Varsayılan sayfa sayısı',
            'DEFAULT_PAGE_COUNT_HELP' => 'Varsayılan maksimum sayfa listeleme sayısı',
            'DATE_BASED_PUBLISHING' => 'Tarih-bazlı yayınlama',
            'DATE_BASED_PUBLISHING_HELP' => 'Automatically (un)publish posts based on their date',
            'EVENTS' => 'Etkinlikler',
            'EVENTS_HELP' => 'Belirli olayları etkinleştirin ya da devredışı bırakın. Bu işlemler, bazı eklentilerin çalışmasını engelleyebilir',
            'REDIRECT_DEFAULT_ROUTE' => 'Varsayılan yola yönlendir',
            'REDIRECT_DEFAULT_ROUTE_HELP' => 'Automatically redirect to a page\'s default route',
            'LANGUAGES' => 'Diller',
            'SUPPORTED' => 'Desteklenen',
            'SUPPORTED_HELP' => 'Comma separated list of 2 letter language codes (for example \'en,fr,de\')',
            'SUPPORTED_PLACEHOLDER' => 'Örn. en,fr',
            'TRANSLATIONS_FALLBACK' => 'Translations fallback',
            'TRANSLATIONS_FALLBACK_HELP' => 'Fallback through supported translations if active language doesn\'t exist',
            'ACTIVE_LANGUAGE_IN_SESSION' => 'Oturumdaki aktif dil',
            'ACTIVE_LANGUAGE_IN_SESSION_HELP' => 'Etkin dili bu oturumda sakla',
            'HTTP_HEADERS' => 'HTTP Başlıkları',
            'EXPIRES' => 'Sona erme',
            'EXPIRES_HELP' => 'Sets the expires header. The value is in seconds.',
            'CACHE_CONTROL' => 'HTTP Cache-Control',
            'CACHE_CONTROL_HELP' => 'Set to a valid cache-control value such as `no-cache, no-store, must-revalidate`',
            'CACHE_CONTROL_PLACEHOLDER' => 'Örn. paylaşım, yaş sınırı=31536000',
            'LAST_MODIFIED' => 'Son değiştirilme',
            'LAST_MODIFIED_HELP' => 'Sets the last modified header that can help optimize proxy and browser caching',
            'ETAG' => 'ETag',
            'ETAG_HELP' => 'Sets the etag header to help identify when a page has been modified',
            'VARY_ACCEPT_ENCODING' => 'Vary accept encoding',
            'VARY_ACCEPT_ENCODING_HELP' => 'Sets the `Vary: Accept Encoding` header to help with proxy and CDN caching',
            'MARKDOWN' => 'Markdown',
            'MARKDOWN_EXTRA' => 'Ekstra Tanım',
            'MARKDOWN_EXTRA_HELP' => 'Enable default support for Markdown Extra - https://michelf.ca/projects/php-markdown/extra/',
            'MARKDOWN_EXTRA_ESCAPE_FENCES' => 'HTML karakterlerden kaçının',
            'MARKDOWN_EXTRA_ESCAPE_FENCES_HELP' => 'HTML karakterlerden kaçının',
            'AUTO_LINE_BREAKS' => 'Otomatik satır sonları',
            'AUTO_LINE_BREAKS_HELP' => 'Markdown\'da otomatik satır sonlarını aktif et',
            'AUTO_URL_LINKS' => 'Otomatik URL bağlantıları',
            'AUTO_URL_LINKS_HELP' => 'URL\'leri otomatik olarak HTML linklerine çevirmeyi aktif et',
            'ESCAPE_MARKUP' => 'Biçimlendirmeyi süz(ESCAPE_MARKUP)',
            'ESCAPE_MARKUP_HELP' => 'Escape markup tags into HTML entities',
            'CACHING_HELP' => 'Global ON/OFF switch to enable/disable Grav caching',
            'CACHE_CHECK_METHOD' => 'Cache kontrol methodu',
            'CACHE_CHECK_METHOD_HELP' => 'Select the method that Grav uses to check if page files have been modified.',
            'CACHE_DRIVER' => 'Önbellek sürücüsü',
            'CACHE_DRIVER_HELP' => 'Önbellek sürücüsü seçin. Otomatik Algıla\'yı seçmeniz durumunda Grav sizin için en iyisini bulacaktır',
            'CACHE_PREFIX' => 'Önbellek öneki',
            'CACHE_PREFIX_HELP' => 'An identifier for part of the Grav key.  Don\'t change unless you know what your doing.',
            'CACHE_PREFIX_PLACEHOLDER' => 'Derived from base URL (override by entering random string)',
            'CACHE_PURGE_JOB' => 'Zamanlanmış Temizleme İşini Çalıştır',
            'CACHE_PURGE_JOB_HELP' => 'Zamanlayıcı ile belirli periyotlarla,  önbelleğe alınan dosyaları temizleyebilirsiniz.',
            'CACHE_CLEAR_JOB' => 'Zamanlanmış Temizle İşini Çalıştır',
            'CACHE_CLEAR_JOB_HELP' => 'Zamanlayıcı ile belirli periyotlarla, Grav önbellek dosyalarını temizleyebilirsiniz.',
            'CACHE_JOB_TYPE' => 'Önbellek İş Türü',
            'CACHE_JOB_TYPE_HELP' => '\'Standart\' klasör önbellek dosyalarını silin veya \'Tüm\' klasör önbelleklerini temizleyin.',
            'CACHE_PURGE' => 'Eski Önbelleği Temizle',
            'LIFETIME' => 'Yaşam süresi',
            'LIFETIME_HELP' => 'Sets the cache lifetime in seconds. 0 = infinite',
            'GZIP_COMPRESSION' => 'Gzip sıkıştırması',
            'GZIP_COMPRESSION_HELP' => 'Grav sayfalarında arttırılmış performans için GZip sıkıştırmasını aktif et.',
            'TWIG_TEMPLATING' => 'Twig Şablonu',
            'TWIG_CACHING' => 'Twig önbellekleme',
            'TWIG_CACHING_HELP' => 'Twig önbellekleme mekanizmasını kontrol edin. En iyi performans için bunu etkinleştirin.',
            'TWIG_DEBUG' => 'Twig hata ayıklama',
            'TWIG_DEBUG_HELP' => 'Twig Debugger uzantısını yüklememe seçeneğine izin verir',
            'DETECT_CHANGES' => 'Değişiklikleri tespit et',
            'DETECT_CHANGES_HELP' => 'Twig şablonlarında bir değişiklik algılandığında otomatik olarak yeniden derlenecektir',
            'AUTOESCAPE_VARIABLES' => 'Autoescape variables',
            'AUTOESCAPE_VARIABLES_HELP' => 'Autoescapes all variables.  This will break your site most likely',
            'ASSETS' => 'Statik dosyalar',
            'CSS_PIPELINE' => 'CSS pipeline',
            'CSS_PIPELINE_HELP' => 'The CSS pipeline is the unification of multiple CSS resources into one file',
            'CSS_PIPELINE_INCLUDE_EXTERNALS' => 'Include externals in CSS pipeline',
            'CSS_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'External URLs sometimes have relative file references and shouldn\'t be pipelined',
            'CSS_PIPELINE_BEFORE_EXCLUDES' => 'CSS pipeline render first',
            'CSS_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Render the CSS pipeline before any other CSS references that are not included',
            'CSS_MINIFY' => 'CSS küçültme',
            'CSS_MINIFY_HELP' => 'Minify the CSS during pipelining',
            'CSS_MINIFY_WINDOWS_OVERRIDE' => 'CSS minify işlemini Windows platformlarında geçersiz kıl',
            'CSS_MINIFY_WINDOWS_OVERRIDE_HELP' => 'Windows platformları için minify işlemini geçersiz kıl. Bu özellik ThreadStackSize nedeniyle hatalı çalışıyor',
            'CSS_REWRITE' => 'CSS\'i yeniden yaz',
            'CSS_REWRITE_HELP' => 'Rewrite any CSS relative URLs during pipelining',
            'JAVASCRIPT_PIPELINE' => 'JavaScript Yolu',
            'JAVASCRIPT_PIPELINE_HELP' => 'The JS pipeline is the unification of multiple JS resources into one file',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS' => 'Include externals in JS pipeline',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'External URLs sometimes have relative file references and shouldn\'t be pipelined',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES' => 'JS pipeline render first',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Render the JS pipeline before any other JS references that are not included',
            'JAVASCRIPT_MINIFY' => 'JavaScript küçültme',
            'JAVASCRIPT_MINIFY_HELP' => 'Minify the JS during pipelining',
            'ENABLED_TIMESTAMPS_ON_ASSETS' => 'Statis dosyalarda timestampleri etkinleştir',
            'ENABLED_TIMESTAMPS_ON_ASSETS_HELP' => 'Statik dosyalar için timestamp\'i aktif et',
            'ENABLED_SRI_ON_ASSETS' => 'Varlıklarda SRI aktif et',
            'ENABLED_SRI_ON_ASSETS_HELP' => 'Aktif Varlık SRI',
            'COLLECTIONS' => 'Koleksiyonlar',
            'ERROR_HANDLER' => 'Hata yakalayıcı',
            'DISPLAY_ERRORS' => 'Hataları göster',
            'DISPLAY_ERRORS_HELP' => 'Hataları tam sayfa olarak görüntüle',
            'LOG_ERRORS' => 'Hataları kaydet',
            'LOG_ERRORS_HELP' => 'Hata günlüklerini /logs klasörüne kaydet',
            'LOG_HANDLER' => 'Günlük işleyicisi',
            'LOG_HANDLER_HELP' => 'Log kayıtları nereye',
            'SYSLOG_FACILITY' => 'Syslog tesisi',
            'SYSLOG_FACILITY_HELP' => 'Çıktı için syslog tesisi',
            'DEBUGGER' => 'Hata Ayıklayıcı',
            'DEBUGGER_HELP' => 'Grav hata ayıklayıcısını aktif et ve aşağıdaki ayarları yap',
            'DEBUG_TWIG' => 'Hata Ayıkla - Twig',
            'DEBUG_TWIG_HELP' => 'Twig şablonlarında hata ayıklamayı aktif et',
            'SHUTDOWN_CLOSE_CONNECTION' => 'Shutdown close connection',
            'SHUTDOWN_CLOSE_CONNECTION_HELP' => 'OnShutdown() fonksiyonunu çağırmadan önce bağlantıyı kapatın. Hata ayıklama yanlış çalışabilir',
            'DEFAULT_IMAGE_QUALITY' => 'Varsayılan görüntü kalitesi',
            'DEFAULT_IMAGE_QUALITY_HELP' => 'Görselleri yeniden oluştururken ya da önbelleğe alırken kullanılacak varsayılan kalite (85%)',
            'CACHE_ALL' => 'Tüm görüntülere önbelleğe al',
            'CACHE_ALL_HELP' => 'Medya dosyalarına müdahale edilmemiş olsa bile tüm görselleri Grav\'in önbellek sistemi yardımıyla çalıştır',
            'IMAGES_DEBUG' => 'Görüntü hata ayıklama filigranı',
            'IMAGES_DEBUG_HELP' => 'Show an overlay over images indicating the pixel depth of the image when working with retina for example',
            'IMAGES_LOADING' => 'Fotoğraf Yükleniyor Davranışı',
            'IMAGES_LOADING_HELP' => 'Loading özelliği, bir tarayıcının ekran dışı görüntüleri ve iç çerçevelerin yüklenmesini kullanıcılar yanlarına kaydırıncaya kadar ertelemesine olanak tanır. yükleme üç değeri destekler: otomatik, tembel, istekli',
            'IMAGES_SEOFRIENDLY' => 'SEO-Friendly resim adları',
            'IMAGES_SEOFRIENDLY_HELP' => 'Etkinleştirildiğinde, önce görüntü adı görüntülenir, ardından işlenen işlemleri yansıtmak için daha küçük bir karma değer görüntülenir.',
            'UPLOAD_LIMIT' => 'Dosya yükleme limiti',
            'UPLOAD_LIMIT_HELP' => 'Maksimum dosya yükleme boyutunu girin (bayt)(0 değeri limitsiz olarak kabul edilir)',
            'ENABLE_MEDIA_TIMESTAMP' => 'Medya dosyalarında timestamp özelliğini etkinleştir',
            'ENABLE_MEDIA_TIMESTAMP_HELP' => 'Appends a timestamp based on last modified date to each media item',
            'SESSION' => 'Oturum',
            'SESSION_ENABLED_HELP' => 'Grav içerisinde oturum desteğini etkinleştir',
            'SESSION_NAME_HELP' => 'An identifier used to form the name of the session cookie',
            'SESSION_UNIQUENESS' => 'Benzersiz dize',
            'SESSION_UNIQUENESS_HELP' => 'Grav kök yolunun MD5 karması, yani "GRAV_ROOT" (varsayılan) veya rastgele "security.salt" dizesine dayalı bir.',
            'ABSOLUTE_URLS' => 'Mutlaka URL\'ler',
            'ABSOLUTE_URLS_HELP' => '`base_url` için mutlak ya da ilgili URL\'ler',
            'PARAMETER_SEPARATOR' => 'Parameter separator',
            'PARAMETER_SEPARATOR_HELP' => 'Separator for passed parameters that can be changed for Apache on Windows',
            'TASK_COMPLETED' => 'Görev tamamlandı',
            'EVERYTHING_UP_TO_DATE' => 'Herşey günceş',
            'UPDATES_ARE_AVAILABLE' => 'güncelleştirme(ler) mevcut',
            'IS_AVAILABLE_FOR_UPDATE' => 'güncelleme için uygun',
            'IS_NOW_AVAILABLE' => 'kullanılabilir',
            'CURRENT' => 'Güncel',
            'UPDATE_GRAV_NOW' => 'Grav\'ı şimdi güncelle',
            'GRAV_SYMBOLICALLY_LINKED' => 'Grav sembolik olarak bağlandı. Yükseltmeler kullanılamayacaktır',
            'UPDATING_PLEASE_WAIT' => 'Güncelleniyor... Lütfen indirilirken bekleyin',
            'OF_THIS' => 'of this',
            'OF_YOUR' => 'of your',
            'HAVE_AN_UPDATE_AVAILABLE' => 'kullanılabilir bir güncelleme var',
            'SAVE_AS' => 'Farklı kaydet',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_DESC' => 'Bu sayfayı, alt sayfalarıyla birlikte silmek istediğinize emin misiniz? Eğer sayfa başka dillere çevirilmişse bu çevirilere saklanacaktır ve ayrıca silinmelidir. Aksi halde sayfa klasörü, alt sayfalarıyla birlikte silinecektir. Bu eylem geri alınamaz.',
            'AND' => 've',
            'UPDATE_AVAILABLE' => 'Güncelleme var',
            'METADATA_KEY' => 'Anahtar (örn. \'keywords\')',
            'METADATA_VALUE' => 'Değer (örn. \'blog, grav\')',
            'USERNAME_HELP' => 'Kullanıcı adı 3 ile 16 karakter arasında olmalıdır. Küçük harfler, sayılar, alt çizgiler ve kısa çizgi içerebilir. Büyük harf, boşluk ya da özel karakterlere izin verilmiyor',
            'FULLY_UPDATED' => 'Tamamen Güncellendi',
            'SAVE_LOCATION' => 'Kayıt konumu',
            'PAGE_FILE' => 'Sayfa Şablonu',
            'PAGE_FILE_HELP' => 'Sayfa şablonu dosya adı ve bu sayfa için varsayılan olarak görüntülenecek şablon',
            'NO_USER_ACCOUNTS' => 'Kullanıcı hesabı bulunamadı, önce bir tane ekleyin...',
            'NO_USER_EXISTS' => 'Bu hesap için kayıtlı kullanıcı yok, kaydedilemedi.',
            'REDIRECT_TRAILING_SLASH' => 'Redirect trailing slash',
            'REDIRECT_TRAILING_SLASH_HELP' => 'Perform a 301 redirect rather than transparently handling trailing slash URIs.',
            'DEFAULT_DATE_FORMAT' => 'Sayfa tarih biçimi',
            'DEFAULT_DATE_FORMAT_HELP' => 'Page date format used by Grav. By default, Grav attempts to guess your date format, however you can specifiy a format using PHP\'s date syntax (e.g.: Y-m-d H:i)',
            'DEFAULT_DATE_FORMAT_PLACEHOLDER' => 'Otomatik olarak tahmin et',
            'IGNORE_FILES' => 'Dosyaları yoksay',
            'IGNORE_FILES_HELP' => 'Sayfalar işlenirken yok sayılacak belirli dosyalar',
            'IGNORE_FOLDERS' => 'Klasörleri yoksay',
            'IGNORE_FOLDERS_HELP' => 'Sayfalar işlenirken yok sayılacak belirli klasörler',
            'HIDE_EMPTY_FOLDERS' => 'Boş klasörleri gizle',
            'HIDE_EMPTY_FOLDERS_HELP' => 'Eğer klasör .md dosyası içermiyorsa, navigasyonda gizlenmesi ve aynı zamanda yönlendirilememesi gerekir mi?',
            'HTTP_ACCEPT_LANGUAGE' => 'Dili tarayıcıdan al',
            'HTTP_ACCEPT_LANGUAGE_HELP' => 'You can opt to try to set the language based on `http_accept_language` header tag in the browser',
            'OVERRIDE_LOCALE' => 'Yerel ayarları geçersiz kıl',
            'OVERRIDE_LOCALE_HELP' => 'Geçerli dili temel al ve PHP\'de bulunan yerel ayarı geçersiz kıl',
            'REDIRECT' => 'Sayfa yönlendirmesi',
            'REDIRECT_HELP' => 'Enter a page route or external URL for this page to redirect to. e.g. `/some/route` or `http://somesite.com`',
            'PLUGIN_STATUS' => 'Eklenti durumu',
            'INCLUDE_DEFAULT_LANG' => 'Varsayılan dili dahil et',
            'INCLUDE_DEFAULT_LANG_HELP' => 'This will prepend all URLs in the default language with the default language.  e.g. `/en/blog/my-post`',
            'INCLUDE_DEFAULT_LANG_FILE_EXTENSION' => 'Dosya uzantısına varsayılan dili ekle.',
            'INCLUDE_DEFAULT_LANG_HELP_FILE_EXTENSION' => 'Etkinleştirilirse, varsayılan dili dosya uzantısının başına ekleyecektir (ör. ".en.md`). ".md` dosya uzantısını kullanarak varsayılan dili korumak için devre dışı bırakın.',
            'PAGES_FALLBACK_ONLY' => 'Yalnızca sayfalar',
            'PAGES_FALLBACK_ONLY_HELP' => 'Sayfa içeriğini desteklenen diller arasında bulmak için yalnızca \'geri dönüş\'; varsayılan dil, etkin dil yoksa, bulunan herhangi bir dili görüntülemek içindir',
            'ALLOW_URL_TAXONOMY_FILTERS' => 'URL Bölümlendirme(Taxonomy) Süzgeçleri',
            'ALLOW_URL_TAXONOMY_FILTERS_HELP' => 'Page-based collections allow you to filter via `/taxonomy:value`.',
            'REDIRECT_DEFAULT_CODE' => 'Varsayılan yönlendirme kodu',
            'REDIRECT_DEFAULT_CODE_HELP' => 'Yönlendirmeler için kullanılacak HTTP durum kodu',
            'IGNORE_HIDDEN' => 'Ignore hidden',
            'IGNORE_HIDDEN_HELP' => 'NOKTA ile başlayan tüm dosya ve klasörleri yoksay',
            'WRAPPED_SITE' => 'Wrapped site',
            'WRAPPED_SITE_HELP' => 'For themes/plugins to know if Grav is wrapped by another platform',
            'FALLBACK_TYPES' => 'Allowed fallback types',
            'FALLBACK_TYPES_HELP' => 'Allowed file types that can be found if accessed via Page route. Defaults to any supported media type.',
            'INLINE_TYPES' => 'Inline fallback types',
            'INLINE_TYPES_HELP' => 'A list of file types that should be displayed inline rather than downloaded',
            'APPEND_URL_EXT' => 'URL uzantısı ekle',
            'APPEND_URL_EXT_HELP' => 'Will add a custom extension to the Page\'s URL. Note, this will mean Grav will look for `<template>.<extension>.twig` template',
            'PAGE_MODES' => 'Sayfa Modları',
            'PAGE_TYPES' => 'Sayfa Tipleri',
            'PAGE_TYPES_HELP' => 'Grav\'ın desteklediği sayfa türlerini belirler ve sıra, istek belirsizse hangi türe geri dönüleceğini belirler',
            'ACCESS_LEVELS' => 'Erişim seviyeleri',
            'GROUPS' => 'Gruplar',
            'GROUPS_HELP' => 'List of groups the user is part of',
            'ADMIN_ACCESS' => 'Yönetici Erişimi',
            'SITE_ACCESS' => 'Site Erişimi',
            'INVALID_SECURITY_TOKEN' => 'Geçersiz Güvenlik Girdisi',
            'ACTIVATE' => 'Aktif Et',
            'TWIG_UMASK_FIX' => 'Umask Fix',
            'TWIG_UMASK_FIX_HELP' => 'Twig önbellek dosyaları varsayılan olarak 0755 iznine sahiptir, ayarları 0775 olarak düzeltin',
            'CACHE_PERMS' => 'Önbellek İzinleri',
            'CACHE_PERMS_HELP' => 'Varsayılan önbellek klasörü genellikle 0755 şeklindedir ya da kuruluma bağlı olarak 0775 olmalıdır',
            'REMOVE_SUCCESSFUL' => 'Kaldırma Başarılı',
            'REMOVE_FAILED' => 'Kaldırma Başarısız',
            'HIDE_HOME_IN_URLS' => 'URL\'lerden anasayfayı gizle',
            'HIDE_HOME_IN_URLS_HELP' => 'Will ensure the default routes for any pages under home do not reference home\'s regular route',
            'TWIG_FIRST' => 'Önce Twig\'i İşle',
            'TWIG_FIRST_HELP' => 'If you enabled Twig page processing, then you can configure Twig to process before or after markdown',
            'SESSION_SECURE' => 'Güvenli',
            'SESSION_SECURE_HELP' => 'If true, indicates that communication for this cookie must be over an encrypted transmission. WARNING: Enable this only on sites that run exclusively on HTTPS',
            'SESSION_HTTPONLY' => 'Yalnız HTTP',
            'SESSION_HTTPONLY_HELP' => 'If true, indicates that cookies should be used only over HTTP, and JavaScript modification is not allowed',
            'REVERSE_PROXY' => 'Reverse Proxy',
            'REVERSE_PROXY_HELP' => 'Enable this if you are behind a reverse proxy and you are having trouble with URLs containing incorrect ports',
            'INVALID_FRONTMATTER_COULD_NOT_SAVE' => 'Geçersiz frontmatter, kaydedilmedi',
            'ADD_FOLDER' => 'Klasör Ekle',
            'COPY_PAGE' => 'Copy Page',
            'PROXY_URL' => 'Proxy URL\'si',
            'PROXY_URL_HELP' => 'Proksi sunucusunun HOST ya da IP ve PORT bilgisini gir',
            'NOTHING_TO_SAVE' => 'Kaydedilecek Hiçbirşey Yok',
            'FILE_ERROR_ADD' => 'Dosya eklenirken bir hata meydana geldi',
            'FILE_ERROR_UPLOAD' => 'Dosya yüklenirken bir hata meydana geldi',
            'FILE_UNSUPPORTED' => 'Desteklenmeyen dosya biçimi',
            'ADD_ITEM' => 'Öge ekle',
            'FILE_TOO_LARGE' => 'Bu dosya yüklenebilecek boyuttan daha büyük, PHP ayarlarınıza göre maksimum boyut %s<br> `post_max_size` değerini artırın',
            'INSTALLING' => 'Kuruluyor',
            'LOADING' => 'Yükleniyor..',
            'DEPENDENCIES_NOT_MET_MESSAGE' => 'The following dependencies need to be fulfilled first:',
            'ERROR_INSTALLING_PACKAGES' => 'Paket(ler) kurulurken hata oluştu',
            'INSTALLING_DEPENDENCIES' => 'Gereksinimler kuruluyor...',
            'INSTALLING_PACKAGES' => 'Paket(ler) kuruluyor..',
            'PACKAGES_SUCCESSFULLY_INSTALLED' => 'Paket(ler) başarıyla kuruldu.',
            'READY_TO_INSTALL_PACKAGES' => 'Kuruluma hazır paket(ler)',
            'PACKAGES_NOT_INSTALLED' => 'Yüklü olmayan paketler',
            'PACKAGES_NEED_UPDATE' => 'Paketler zaten yüklü, fakat eski',
            'PACKAGES_SUGGESTED_UPDATE' => 'Paketler zaten yüklü ve en güncel sürümlerinde',
            'REMOVE_THE' => 'Kaldır: %s',
            'CONFIRM_REMOVAL' => 'Bunu silmek istediğinizden emin misiniz: %s?',
            'REMOVED_SUCCESSFULLY' => '%s başarıyla kaldırıldı',
            'ERROR_REMOVING_THE' => '%s kaldırılırken hata',
            'ADDITIONAL_DEPENDENCIES_CAN_BE_REMOVED' => '%s diğer kurulu paketler tarafından kullanılmayan birtakım gereksinimler içeriyor. Kullanmıyorsanız direkt olarak silebilirsiniz.',
            'READY_TO_UPDATE_PACKAGES' => 'Güncellemeye hazır paket(ler)',
            'ERROR_UPDATING_PACKAGES' => 'Paket(ler) günceleştirilirken hata oluştu',
            'UPDATING_PACKAGES' => 'Paket(ler) güncelleştiriliyor..',
            'PACKAGES_SUCCESSFULLY_UPDATED' => 'Paket(ler) başarıyla güncellendi.',
            'UPDATING' => 'Güncelleniyor',
            'GPM_RELEASES' => 'GPM Duyuruları',
            'GPM_RELEASES_HELP' => 'Beta sürümünü veya test sürümlerini yüklemek için \'Test\' seçeneğini belirleyin',
            'GPM_METHOD' => 'Remote Fetch Method',
            'GPM_METHOD_HELP' => 'When set to Auto, Grav will determine if fopen is available and use it, otherwise fall back to cURL. To force the use of one or the other switch the setting.',
            'AUTO' => 'Otomatik',
            'FOPEN' => 'fopen',
            'CURL' => 'cURL',
            'STABLE' => 'Kararlı',
            'TESTING' => 'Test ediliyor',
            'FRONTMATTER_PROCESS_TWIG' => 'Process frontmatter Twig',
            'FRONTMATTER_PROCESS_TWIG_HELP' => 'When enabled you can use Twig config variables in page front matter',
            'FRONTMATTER_IGNORE_FIELDS' => 'Ignore frontmatter fields',
            'FRONTMATTER_IGNORE_FIELDS_HELP' => 'Certain frontmatter fields may contain Twig but should not be processed, such as \'forms\'',
            'FRONTMATTER_IGNORE_FIELDS_PLACEHOLDER' => 'Örn. formlar',
            'PACKAGE_X_INSTALLED_SUCCESSFULLY' => '%s başarıyla yüklendi',
            'ORDERING_DISABLED_BECAUSE_PARENT_SETTING_ORDER' => 'Parent setting order, ordering disabled',
            'ORDERING_DISABLED_BECAUSE_PAGE_NOT_VISIBLE' => 'Sayfa görüntülenebilir değil, sıralama devredışı',
            'ORDERING_DISABLED_BECAUSE_TOO_MANY_SIBLINGS' => 'Ordering via the admin is unsupported because there are more than 200 siblings',
            'ORDERING_DISABLED_BECAUSE_PAGE_NO_PREFIX' => 'Page ordering is disabled for this page because <strong>Folder Numeric Prefix</strong> is not enabled',
            'CANNOT_ADD_MEDIA_FILES_PAGE_NOT_SAVED' => 'NOTE: You cannot add media files until you save the page. Just click \'Save\' on top',
            'CANNOT_ADD_FILES_PAGE_NOT_SAVED' => 'NOTE: Page must be saved before you can upload files to it.',
            'DROP_FILES_HERE_TO_UPLOAD' => 'Drop your files here or <strong>click in this area</strong>',
            'INSERT' => 'Ekle',
            'UNDO' => 'Geri al',
            'REDO' => 'İleri al',
            'HEADERS' => 'Başlıklar',
            'BOLD' => 'Kalın',
            'ITALIC' => 'Eğik',
            'STRIKETHROUGH' => 'Üstü Çizili',
            'SUMMARY_DELIMITER' => 'Özet ayracı',
            'LINK' => 'Bağlantı',
            'IMAGE' => 'Resim',
            'BLOCKQUOTE' => 'Alıntı',
            'UNORDERED_LIST' => 'Sırasız Liste',
            'ORDERED_LIST' => 'Sıralı Liste',
            'EDITOR' => 'Düzenleyici',
            'PREVIEW' => 'Önizleme',
            'FULLSCREEN' => 'Tam Ekran',
            'NON_ROUTABLE' => 'Yönlendirilemeyen',
            'NON_VISIBLE' => 'Görünür Olmayan',
            'NON_PUBLISHED' => 'Yayımlanmamış',
            'CHARACTERS' => 'karakter',
            'PUBLISHING' => 'Yayımlanıyor',
            'MEDIA_TYPES' => 'Ortam Türleri',
            'IMAGE_OPTIONS' => 'Görüntü Seçenekleri',
            'MIME_TYPE' => 'Mime Türü',
            'THUMB' => 'Küçük Resim',
            'TYPE' => 'Tür',
            'FILE_EXTENSION' => 'Dosya Uzantısı',
            'LEGEND' => 'Sayfa Göstergesi',
            'MEMCACHE_SERVER' => 'Memcache sunucusu',
            'MEMCACHE_SERVER_HELP' => 'Memcache sunucu adresi',
            'MEMCACHE_PORT' => 'Memcache bağlantı noktası',
            'MEMCACHE_PORT_HELP' => 'Memcache sunucusu bağlantı noktası',
            'MEMCACHED_SERVER' => 'Memcached sunucusu',
            'MEMCACHED_SERVER_HELP' => 'Memcached sunucu adresi',
            'MEMCACHED_PORT' => 'Memcached bağlantı noktası',
            'MEMCACHED_PORT_HELP' => 'Memcached sunucusu bağlantı noktası',
            'REDIS_SERVER' => 'Redis sunucusu',
            'REDIS_SERVER_HELP' => 'Redis sunucu adresi',
            'REDIS_PORT' => 'Redis bağlantı noktası',
            'REDIS_PORT_HELP' => 'Redis sunucusu bağlantı noktası',
            'REDIS_PASSWORD' => 'Redis password/secret',
            'REDIS_DATABASE' => 'Redis Veritabanı ID',
            'REDIS_DATABASE_HELP' => 'Redis Örnek Veritabanı ID',
            'ALL' => 'Tümü',
            'FROM' => 'Başlangıç',
            'TO' => 'ile',
            'RELEASE_DATE' => 'Yayınlanma Tarihi',
            'SORT_BY' => 'Sıralama Ölçütü',
            'RESOURCE_FILTER' => 'Filtrele...',
            'FORCE_SSL' => 'SSL Kullanmaya Zorla',
            'FORCE_SSL_HELP' => 'Globally force SSL, if enabled when the site is reached through HTTP, Grav sends a redirect to the HTTPS page',
            'NEWS_FEED' => 'Haber Kaynağı',
            'EXTERNAL_URL' => 'Harici Bağlantı',
            'SESSION_SAMESITE' => 'SameSite oturum niteliği',
            'CUSTOM_BASE_URL' => 'Özelleştirilmiş kök URL',
            'CUSTOM_BASE_URL_HELP' => 'Use if you want to rewrite the site domain or use a different subfolder than the one used by Grav. Example: http://localhost',
            'FILEUPLOAD_PREVENT_SELF' => '"%s" sayfalar dışarısında kullanılamaz.',
            'FILEUPLOAD_UNABLE_TO_UPLOAD' => 'Unable to upload file %s: %s',
            'FILEUPLOAD_UNABLE_TO_MOVE' => '%s dosyası "%s" konumuna taşınamadı',
            'DROPZONE_CANCEL_UPLOAD' => 'Yüklemeyi iptal et',
            'DROPZONE_CANCEL_UPLOAD_CONFIRMATION' => 'Bu yüklemeyi iptal etmek istediğinizden emin misiniz?',
            'DROPZONE_DEFAULT_MESSAGE' => 'Dosyalarınızı buraya sürükleyin ya da <strong>bu alana tıklayın</strong>',
            'DROPZONE_FALLBACK_MESSAGE' => 'Tarayıcınız sürükle bırak şeklinde yüklemeleri desteklemiyor.',
            'DROPZONE_FALLBACK_TEXT' => 'Please use the fallback form below to upload your files like in the older days.',
            'DROPZONE_FILE_TOO_BIG' => 'Dosya fazla büyük ({{filesize}}MiB), maksimum dosya boyutu {{maxFilesize}} MiB’dir.',
            'DROPZONE_INVALID_FILE_TYPE' => 'Bu türdeki dosyaları yükleyemezsiniz.',
            'DROPZONE_MAX_FILES_EXCEEDED' => 'Daha fazla dosya yükleyemezsiniz.',
            'DROPZONE_REMOVE_FILE' => 'Dosyayı Kaldır',
            'DROPZONE_RESPONSE_ERROR' => 'Sunucu {{statusCode}} koduyla cevap verdi.',
            'PREMIUM_PRODUCT' => 'Premium',
            'DESTINATION_NOT_SPECIFIED' => 'Hedef belirtilmedi',
            'UPLOAD_ERR_NO_TMP_DIR' => 'Missing a temporary folder',
            'SESSION_SPLIT' => 'Session Split',
            'SESSION_SPLIT_HELP' => 'Independent split sessions between site and other plugins (such as admin)',
            'ERROR_FULL_BACKTRACE' => 'Tam Backtrace Hatası',
            'ERROR_SIMPLE' => 'Basit Hata',
            'ERROR_SYSTEM' => 'Sistem Hatası',
            'IMAGES_AUTO_FIX_ORIENTATION' => 'Fix orientation automatically',
            'IMAGES_AUTO_FIX_ORIENTATION_HELP' => 'Automatically fix the image orientation based on the Exif data',
            'REDIS_SOCKET' => 'Redis socket',
            'REDIS_SOCKET_HELP' => 'The Redis socket',
            'NOT_SET' => 'Not set',
            'PERMISSIONS' => 'İzinler',
            'NEVER_CACHE_TWIG' => 'Twig\'i asla önbelleğe alma',
            'NEVER_CACHE_TWIG_HELP' => 'Sadece içeriği önbelleğe al ve sayfalar için her seferinde Twig çalışsın. twig_first seçeneği yoksayılacaktır.',
            'ALLOW_WEBSERVER_GZIP' => 'Sunucu Gzip sıkıştırmasına izin ver',
            'ALLOW_WEBSERVER_GZIP_HELP' => 'Off by default. When enabled, WebServer-configured Gzip/Deflate compression will work, but http connection will not be closed before onShutDown() event causing slower page loading',
            'OFFLINE_WARNING' => 'GPM ile bağlantı kurulamadı',
            'CLEAR_IMAGES_BY_DEFAULT' => 'Clear image cache by default',
            'CLEAR_IMAGES_BY_DEFAULT_HELP' => 'By default processed images are cleared for all cache clears, this can be disabled',
            'CLI_COMPATIBILITY' => 'CLI Uyumluluğu',
            'CLI_COMPATIBILITY_HELP' => 'Ensures that only non-volatile Cache drivers are used (file, redis, memcache, etc.)',
            'REINSTALL_PLUGIN' => 'Eklentiyi yeniden kur',
            'REINSTALL_THEME' => 'Temayı yeniden kur',
            'REINSTALL_THE' => 'Reinstall the %s',
            'CONFIRM_REINSTALL' => 'Are you sure you want to reinstall this %s?',
            'REINSTALLED_SUCCESSFULLY' => '%s reinstalled successfully',
            'ERROR_REINSTALLING_THE' => 'Error reinstalling the %s',
            'PACKAGE_X_REINSTALLED_SUCCESSFULLY' => 'Package %s reinstalled successfully',
            'REINSTALLATION_FAILED' => 'Yeniden kurulum hatasi',
            'WARNING_REINSTALL_NOT_LATEST_RELEASE' => 'The installed version is not the latest release. By clicking Continue, you\'ll remove the current version and install the latest available release',
            'TOOLS' => 'Araçlar',
            'DIRECT_INSTALL' => 'Yükleme yolu',
            'NO_PACKAGE_NAME' => 'Package name not specified',
            'PACKAGE_EXTRACTION_FAILED' => 'Package extraction failed',
            'NOT_VALID_GRAV_PACKAGE' => 'Not a valid Grav package',
            'NAME_COULD_NOT_BE_DETERMINED' => 'Name could not be determined',
            'CANNOT_OVERWRITE_SYMLINKS' => 'Cannot overwrite symlinks',
            'ZIP_PACKAGE_NOT_FOUND' => 'ZIP package could not be found',
            'GPM_OFFICIAL_ONLY' => 'Official GPM Only',
            'GPM_OFFICIAL_ONLY_HELP' => 'Only allow direct installs from the official GPM repository only.',
            'NO_CHILD_TYPE' => 'No child type for this rawroute',
            'SORTABLE_PAGES' => 'Sortable Pages:',
            'UNSORTABLE_PAGES' => 'Sıralanamayan Sayfalar',
            'ADMIN_SPECIFIC_OVERRIDES' => 'Admin Specific Overrides',
            'ADMIN_CHILDREN_DISPLAY_ORDER' => 'Children Display Order',
            'ADMIN_CHILDREN_DISPLAY_ORDER_HELP' => 'The order that children of this page should be displayed in the \'Pages\' view of Admin plugin',
            'PWD_PLACEHOLDER' => 'complex string at least 8 chars long',
            'PWD_REGEX' => 'Password Regex',
            'PWD_REGEX_HELP' => 'By default: Password must contain at least one number and one uppercase and lowercase letter, and at least 8 or more characters',
            'USERNAME_PLACEHOLDER' => 'lowercase chars only, e.g. \'admin\'',
            'USERNAME_REGEX' => 'Username Regex',
            'USERNAME_REGEX_HELP' => 'By default: Only lowercase chars, digits, dashes, and underscores. 3 - 16 chars',
            'ENABLE_AUTO_METADATA' => 'Auto metadata from Exif',
            'ENABLE_AUTO_METADATA_HELP' => 'Görsel için otomatikman EXIF bilgilerini kullanarak metadata dosyası oluştur',
            '2FA_TITLE' => '2-Adımlı Doğrulama',
            '2FA_INSTRUCTIONS' => '##### 2-Adımlı Doğrulama
Hesabınızda **2FA** etkin durumda. Lütfen giriş yapabilmek için **2FA** uygulamanızı kullanarak **6-digit code** girin.',
            '2FA_REGEN_HINT' => 'Gizli anahtarı yeniden oluşturursanız kimlik doğrulama uygulamasını güncellemeniz gereklidir',
            '2FA_LABEL' => 'Yönetici Erişimi',
            '2FA_FAILED' => 'Invalid 2-Factor Authentication code, please try again...
Geçersiz 2FA doğrulama kodu, lütfen tekrar deneyin...',
            '2FA_ENABLED' => '2FA Etkin',
            '2FA_CODE_INPUT' => '000000',
            '2FA_SECRET' => '2FA Doğrulama Anahtarı',
            '2FA_SECRET_HELP' => 'Bu QR kodunu [Authenticator App] cihazınıza tarayın (https://learn.getgrav.org/admin-panel/2fa#apps). Ayrıca, uygulamanızı yeniden yüklemeniz gerekebilir diye sırrı güvenli bir yerde yedeklemek iyi bir fikirdir. Daha fazla bilgi için [Grav docs] (https://learn.getgrav.org/admin-panel/2fa) adresini ziyaret edin ',
            '2FA_REGENERATE' => 'Yeniden Oluştur',
            'FORCE_LOWERCASE_URLS' => 'URL harflerini küçült',
            'FORCE_LOWERCASE_URLS_HELP' => 'Varsayılan olarak Grav, URL\'de ki tüm harfleri küçültecektir, Bunu kapalı hale getirirseniz URL içinde büyük harfler kullanılabilir.',
            'INTL_ENABLED' => 'Intl modül entegrasyonu',
            'INTL_ENABLED_HELP' => 'Intl PHP modülünü kullanın ve UTF8 tabanlı koleksiyonları sıralamak için harmanlayın',
            'VIEW_SITE_TIP' => 'Siteyi görüntüle',
            'TOOLS_DIRECT_INSTALL_TITLE' => 'Grav Paketlerini Doğrudan Yükle',
            'TOOLS_DIRECT_INSTALL_UPLOAD_TITLE' => 'ZIP Olarak Paket Yükle ve Kur',
            'TOOLS_DIRECT_INSTALL_UPLOAD_DESC' => 'Bu yöntemle geçerli bir<strong>tema</strong> ya da <strong>eklenti</strong>paketini, hatta <strong>Grav</strong> güncelleme paketini ZIP olarak yükleyebilirsin. Bu paketin GPM ile kaydedilmesi gerekmez ve önceki bir sürüme kolayca geri dönmenizi veya test için yüklemenizi sağlar.',
            'TOOLS_DIRECT_INSTALL_URL_TITLE' => 'Uzak URL ile Paket Yükle',
            'TOOLS_DIRECT_INSTALL_URL_DESC' => 'Alternatif bir yöntem olarak, paketin tam ZIP dosyasının bulunduğu uzak adresi belirterek yükleyebilirsin.',
            'TOOLS_DIRECT_INSTALL_UPLOAD_BUTTON' => 'Yükle ve kur',
            'ROUTE_OVERRIDES' => 'Geçersiz Rotalar',
            'ROUTE_DEFAULT' => 'Varsayılan Rota',
            'ROUTE_CANONICAL' => 'Standart Rota',
            'ROUTE_ALIASES' => 'Rota Takma Adları',
            'OPEN_NEW_TAB' => 'Yeni sekmede aç',
            'SESSION_INITIALIZE' => 'Oturumu Başlat',
            'SESSION_INITIALIZE_HELP' => 'Grav\'ı bir oturum başlatmak için yapar. Bu özellik, oturum açma, formlar vb. Gibi herhangi bir kullanıcı etkileşiminin çalışması için gereklidir. Admin Paneli eklentisi bu ayardan etkilenmez.',
            'STRICT_YAML_COMPAT' => 'YAML Uyumluluğu',
            'STRICT_YAML_COMPAT_HELP' => 'Yerel veya 3.4 ayrıştırıcı başarısız olursa Symfony 2.4 YAML ayrıştırıcısına geri döner',
            'STRICT_TWIG_COMPAT' => 'Twig Uyumluluğu',
            'STRICT_TWIG_COMPAT_HELP' => 'Kullanımdan kaldırılan Twig otomatik görünüm ayarını etkinleştirir. Devre dışı bırakıldığında, Twig otomatik çıktının çıktısını alacağı için HTML çıktısını almak için | ham filtre gerekir',
            'SCHEDULER' => 'Zamanlayıcı',
            'SCHEDULER_INSTALL_INSTRUCTIONS' => 'Kurulum Talimatları',
            'SCHEDULER_INSTALLED_READY' => 'Yüklendi ve Hazır',
            'SCHEDULER_CRON_NA' => 'Zamanlayıcı <b>%s</b> kullanıcısı için mümkün değil.',
            'SCHEDULER_NOT_ENABLED' => '<b>%s</b> kullanıcısı için aktif değil.',
            'SCHEDULER_SETUP' => 'Zamanlayıcı Ayarı',
            'SCHEDULER_INSTRUCTIONS' => '<b> Grav Scheduler </b>, özel işler oluşturmanıza ve planlamanıza olanak tanır. Ayrıca, Grav eklentilerinin programlı ve dinamik olarak entegre olması için periyodik olarak çalıştırılacak işleri ekleyebilecekleri bir yöntem sağlar.',
            'SCHEDULER_POST_INSTRUCTIONS' => 'Zamanlayıcının işlevselliğini etkinleştirmek için, <b> Grav Scheduler </b> ’ı sisteminizin crontab dosyasına <b>% s </b> kullanıcısı için eklemelisiniz. Otomatik olarak eklemek için yukarıdaki komutu terminalden çalıştırın. Kaydedildikten sonra durumu görmek için bu sayfayı yenileyin.',
            'SCHEDULER_JOBS' => 'Özel Zamanlanmış Görevler',
            'SCHEDULER_STATUS' => 'Zamanlayıcı Durumu',
            'SCHEDULER_RUNAT' => 'Sonrasında çalıştır',
            'SCHEDULER_OUTPUT' => 'Çıktı-Dosyası',
            'SCHEDULER_OUTPUT_HELP' => 'Çıktı dosyasının dosya yolu (Grav yükleme dizininden)',
            'SCHEDULER_OUTPUT_TYPE' => 'Çıkış Türü',
            'SCHEDULER_OUTPUT_TYPE_HELP' => 'Her çalıştırmada aynı dosyaya ekleyin veya her çalıştırmada dosyanın üzerine yazın',
            'SCHEDULER_EMAIL' => 'E-Posta',
            'SCHEDULER_EMAIL_HELP' => 'Çıktının gönderileceği e-posta. NOT: ayarlanacak çıktı dosyası gerektirir',
            'SCHEDULER_WARNING' => 'Zamanlayıcı, komutları yürütmek için sisteminizin crontab sistemini kullanır. Bunu yalnızca ileri düzey bir kullanıcıysanız ve ne yaptığınızı biliyorsanız kullanmalısınız. Yanlış yapılandırma veya kötüye kullanım, güvenlik açıklarına neden olabilir.',
            'SECURITY' => 'Güvenlik',
            'XSS_SECURITY' => 'İçerik için XSS Güvenliği',
            'XSS_WHITELIST_PERMISSIONS' => 'Beyaz Liste İzinleri',
            'XSS_WHITELIST_PERMISSIONS_HELP' => 'Bu izinlere sahip kullanıcılar, içeriği kaydederken XSS kurallarını atlayacaktır',
            'XSS_ON_EVENTS' => 'Olayları Filtrele',
            'XSS_INVALID_PROTOCOLS' => 'Geçersiz protokolleri filtrele',
            'XSS_INVALID_PROTOCOLS_LIST' => 'Geçersiz protokoller listesi',
            'XSS_MOZ_BINDINGS' => 'Moz ciltlemelerini filtreleme',
            'XSS_HTML_INLINE_STYLES' => 'HTML satır içi stillerini filtreleme',
            'XSS_DANGEROUS_TAGS' => 'Tehlikeli HTML etiketlerini filtreleme',
            'XSS_DANGEROUS_TAGS_LIST' => 'Tehlikeli HTML etiket listesi',
            'XSS_ONSAVE_ISSUE' => 'Kaydetme başarısız oldu: XSS sorunu tespit edildi...',
            'XSS_ISSUE' => '<strong> DİKKAT: </strong> Grav, <strong>% s </strong> \'de potansiyel XSS sorunlarını buldu',
            'UPLOADS_SECURITY' => 'Yükleme Güvenliği',
            'UPLOADS_DANGEROUS_EXTENSIONS' => 'Tehlikeli Eklentiler',
            'UPLOADS_DANGEROUS_EXTENSIONS_HELP' => 'Kabul edilen MIME türleri ne olursa olsun bu uzantıların yüklenmesini engelleyin',
            'REPORTS' => 'Raporlar',
            'LOGS' => 'Günlükler',
            'LOG_VIEWER_FILES' => 'Günlük Görüntüleme Dosyaları',
            'LOG_VIEWER_FILES_HELP' => 'Log dosyalarını Araçlar içerisinde görebilirsiniz. Örneğin \'grav\' = /logs/grav.log',
            'BACKUPS_STORAGE_PURGE_TRIGGER' => 'Yedek Depolama Boşaltma Tetikleyicisi',
            'BACKUPS_MAX_COUNT' => 'En fazla yedek sayısı',
            'BACKUPS_MAX_COUNT_HELP' => '0 sınırsız',
            'BACKUPS_MAX_SPACE' => 'Maksimum Yedekleme Alanı',
            'BACKUPS_MAX_RETENTION_TIME' => 'Maksimum Tutma Süresi',
            'BACKUPS_MAX_RETENTION_TIME_APPEND' => 'günler içinde',
            'BACKUPS_PROFILE_NAME' => 'Yedek Adı',
            'BACKUPS_PROFILE_ROOT_FOLDER' => 'Kök Klasörü',
            'BACKUPS_PROFILE_ROOT_FOLDER_HELP' => 'Mutlak bir yol veya akış olabilir',
            'BACKUPS_PROFILE_EXCLUDE_PATHS' => 'Yolları Hariç Tut',
            'BACKUPS_PROFILE_EXCLUDE_PATHS_HELP' => 'Her satırda bir tane olmak üzere hariç tutulacak mutlak yollar',
            'BACKUPS_PROFILE_EXCLUDE_FILES' => 'Dosyaları Hariç Tut',
            'BACKUPS_PROFILE_EXCLUDE_FILES_HELP' => 'Satır başına bir tane olacak şekilde hariç tutulacak Özel Dosyalar veya Klasörler',
            'BACKUPS_PROFILE_SCHEDULE' => 'Zamanlanmış İşi Etkinleştir',
            'BACKUPS_PROFILE_SCHEDULE_AT' => 'Zamanlanmış İşi Çalıştır',
            'COMMAND' => 'Komut',
            'EXTRA_ARGUMENTS' => 'Ekstra Argümanlar',
            'DEFAULT_LANG' => 'Varsayılan Dili Geçersiz Kıl',
            'DEFAULT_LANG_HELP' => 'Varsayılan, desteklenen ilk dildir. Bu seçenek ayarlanarak bu geçersiz kılınabilir, ancak desteklenen dillerden biri olmalıdır',
            'DEBUGGER_PROVIDER' => 'Hata Ayıklayıcı Sağlayıcı',
            'DEBUGGER_PROVIDER_HELP' => 'Varsayılan PHP Hata Ayıklama Çubuğu\'dur, ancak Clockwork tarayıcı uzantısı daha az müdahaleci bir yaklaşım sağlar',
            'DEBUGGER_DEBUGBAR' => 'PHP Hata Ayıklama Çubuğu',
            'DEBUGGER_CLOCKWORK' => 'Saat MekanizmasıTarayıcı Eklentisi',
            'USER_GROUPS' => 'Kullanıcı Grupları',
            'GROUP_NAME' => 'Grup Adı',
            'DISPLAY_NAME' => 'Görüntülenen İsim',
            'ICON' => 'İkon',
            'ACCESS' => 'Erişim',
            'NO_ACCESS' => 'Erişim Yok',
            'ALLOWED' => 'İzin Verildi',
            'DENIED' => 'Reddedildi',
            'MODULE' => 'Modül',
            'ADD_MODULE' => 'Modül Ekle',
            'PAGE_ACCESS' => 'Sayfa Erişimi',
            'PAGE PERMISSIONS' => 'Sayfa İzinleri',
            'PAGE_VISIBILITY_REQUIRES_ACCESS' => 'Menü Görünümü Yetki Gerektirir',
            'PAGE_AUTHORS_HELP' => 'Sayfa Yazarlarının üyeleri, özel \'Yazarlar\' sayfa grubunda tanımlanan bu sayfaya sahip düzeyinde erişime sahiptir.',
            'PAGE_GROUPS' => 'Sayfa Grupları',
            'PAGE_GROUPS_HELP' => 'Sayfa Gruplarının üyeleri bu sayfaya özel erişime sahiptir.',
            'READ' => 'Okuma',
            'PUBLISH' => 'Yayımla',
            'LIST' => 'Liste',
            'ACCESS_SITE' => 'Site',
            'ACCESS_SITE_LOGIN' => 'Siteye Giriş Yap',
            'ACCESS_ADMIN' => 'Yönetici',
            'ACCESS_ADMIN_LOGIN' => 'Admin Paneline Giriş Yap',
            'ACCESS_ADMIN_SUPER' => 'Süper Kullanıcı',
            'ACCESS_ADMIN_CACHE' => 'Önbelleği Temizle',
            'ACCESS_ADMIN_CONFIGURATION' => 'Yapılandırma',
            'ACCESS_ADMIN_CONFIGURATION_SYSTEM' => 'Sistem Yapılandırması',
            'ACCESS_ADMIN_CONFIGURATION_SITE' => 'Site Yapılandırması',
            'ACCESS_ADMIN_CONFIGURATION_MEDIA' => 'Medya Yapılandırması',
            'ACCESS_ADMIN_CONFIGURATION_INFO' => 'Sunucu Bilgisi',
            'ACCESS_ADMIN_SETTINGS' => 'Ayarlar',
            'ACCESS_ADMIN_PAGES' => 'Sayfaları Yönet',
            'ACCESS_ADMIN_MAINTENANCE' => 'Site Bakım',
            'ACCESS_ADMIN_STATISTICS' => 'Site istatistikleri:',
            'ACCESS_ADMIN_PLUGINS' => 'Eklentileri Yönet',
            'ACCESS_ADMIN_THEMES' => 'Temaları yönet',
            'ACCESS_ADMIN_TOOLS' => 'Araçlara Erişim',
            'ACCESS_ADMIN_USERS' => 'Kullanıcıları Yönet',
            'USERS' => 'Kullanıcılar',
            'ACL' => 'Erişim Kontrolü',
            'FLEX_CACHING' => 'Esnek Önbellekleme',
            'FLEX_INDEX_CACHE_ENABLED' => 'Dizin Önbelleği Etkinleştir',
            'FLEX_INDEX_CACHE_LIFETIME' => 'Dizin Bellek Süresi (saniye)',
            'FLEX_OBJECT_CACHE_ENABLED' => 'Obje Önbelleklemeyi Etkinleştir',
            'FLEX_OBJECT_CACHE_LIFETIME' => 'Obje Bellek Süresi (saniye)',
            'LANGUAGE_TRANSLATIONS' => 'Çeviriler',
            'RESET' => 'Sıfırla',
            'LOGOS' => 'Logolar',
            'COLOR_SCHEME_LABEL' => 'Renk Şemaları',
            'WEB_FONTS_LABEL' => 'Web Yazı Tipleri',
            'HEADER_FONT_LABEL' => 'Başlık yazı tipi',
            'CONFIGURATION' => 'Yapılandırma',
            'ADMIN_CACHING' => 'Admin sayfasında önbelleği etkinleştir',
            'ADMIN_CACHING_HELP' => 'Caching in the admin can be controlled independently from the front-end site',
            'CONTENT_PADDING' => 'Content padding',
            'CONTENT_PADDING_HELP' => 'Enable/Disable content padding around content area to provide more space',
            'TIMEOUT' => 'Zamanaşımı',
            'TIMEOUT_HELP' => 'Oturum zaman aşımını saniye olarak ayarlar',
            'DASHBOARD' => 'Yönetim Paneli',
            'NOTIFICATIONS' => 'Bildirimler'
        ]
    ]
];
