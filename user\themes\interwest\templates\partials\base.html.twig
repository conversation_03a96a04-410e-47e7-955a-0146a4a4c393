<!DOCTYPE html>
<html lang="{{ grav.language.getActive() ?: grav.config.site.default_lang }}">
    <head>
    {% block head %}
        <meta charset="utf-8" />
        <meta name="theme-color" content="#328fb6" />
        <meta name="robots" content="INDEX,FOLLOW" />
        {# base.html.twig – canonical ↔ alternate hreflang loop #}
        {% for key in langswitcher.languages %}

          {# 1) URL prefix for *this* language #}
          {% set url_prefix   = key == 'en' ? '/en' : '' %}
          {# 2) prefix of the *current* page language, so we can strip it out #}
          {% set current_pref = langswitcher.current == 'en' ? '/en' : '' %}
          {# 3) build a small “mapping” only if current_pref is non-empty #}
          {% set strip_map    = current_pref ? { (current_pref) : '' } : {} %}
        
          {% if key == langswitcher.current %}
            {# ─── CANONICAL ─── #}
            {% if page.home %}
              {% set href = 'https://www.plomberie5etoiles.com' ~ url_prefix ~ '/' %}
            {% else %}
              {% set clean_path = page.url|replace(strip_map) %}
              {% set href       = 'https://www.plomberie5etoiles.com' ~ url_prefix ~ clean_path %}
            {% endif %}
            <link rel="canonical" hreflang="{{ key }}" href="{{ href }}" />
        
          {% else %}
            {# ─── ALTERNATE ─── #}
            {% set raw_trans = (langswitcher.translated_pages[key] is defined)
                                 ? langswitcher.translated_pages[key].url|trim
                                 : '' %}
            {% set trans_path = raw_trans|replace(strip_map) %}
            {% set href       = 'https://www.plomberie5etoiles.com' ~ url_prefix ~ trans_path %}
            <link rel="alternate" hreflang="{{ key }}" href="{{ href }}" />
          {% endif %}
        
        {% endfor %}




		{# Set the Global Analytics ID for each language (See Tag Manager > Variables > Analytics ID) ss #}
		<meta id="ga-id" content="{% if langswitcher.current == 'fr' %}UA-49434800-1{% else %}UA-34656084-1{% endif %}"/>

        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {% include 'partials/metadata.html.twig' %}

        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        
        <link href="https://fonts.googleapis.com/css?family=Raleway:400,600,800&display=swap" rel="stylesheet">
		<link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
		<link rel="manifest" href="/site.webmanifest">
		<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
		<meta name="msapplication-TileColor" content="#2d89ef">
		<meta name="theme-color" content="#328fb6">

		{% if langswitcher.current == 'fr' %}
		<meta name="google-site-verification" content="6Q0CwM9C4ZwiumbWepbzjcrxrk3T58QQbASttt-4Vlk" />
    <meta name="msvalidate.01" content="26DAACDAB480EC5708316FC8148AA5A4" />

		{% else %}
		<meta name="google-site-verification" content="LSPdl2ApX23tLRcP14OflCe1SHCQ9JjilC6f122vTCU" />
    <meta name="msvalidate.01" content="51E037F123AD6D5A4555CBF6CB2171E1" />
	    {% endif %}

        {% block stylesheets %}
            {#{% do assets.add('theme://css/font-awesome.min.css',99) %}#}
            {% do assets.addCss('theme://css/font-awesome.min.css', { 'priority': 99, 'pipeline': false }) %}
            {% do assets.add('theme://css-compiled/bootstrap.css',98) %}
            {% do assets.add('theme://css-compiled/bootstrap-reboot.css',97) %}
            {% do assets.add('theme://css-compiled/bootstrap-grid.css',96) %}
            {% do assets.add('theme://css-compiled/custom.css',95) %}
            {% do assets.add('https://unpkg.com/aos@2.3.1/dist/aos.css',94) %}
        {% endblock %}

   {#     {% block javascripts %}#}
   {#         {% do assets.add('jquery', 115) %}#}
   {#         {% do assets.add('theme://js/modernizr.custom.71422.js', 100) %}#}
			{#{% do assets.add('https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js', 70) %}#}
			{#{% do assets.add('https://unpkg.com/aos@2.3.1/dist/aos.js', 98) %}#}

   {#         {% if browser.getBrowser == 'msie' and browser.getVersion >= 8 and browser.getVersion <= 9 %}#}
   {#             {% do assets.add('https://oss.maxcdn.com/respond/1.4.2/respond.min.js') %}#}
   {#             {% do assets.add('theme://js/html5shiv-printshiv.min.js') %}#}
   {#         {% endif %}#}
   {#     {% endblock %}#}
        
        {% block javascripts %}
           {# Load jQuery first and exclude it from the pipeline #}
            {% do assets.addJs('https://code.jquery.com/jquery-3.6.0.min.js', { 'group': 'head', 'priority': 100, 'pipeline': false }) %}
        
            {# Load other JS scripts, these can stay in the pipeline #}
            {% do assets.addJs('theme://js/modernizr.custom.71422.js', { 'group': 'bottom', 'priority': 90 }) %}
            {% do assets.addJs('https://unpkg.com/aos@2.3.1/dist/aos.js', { 'group': 'bottom', 'priority': 80 }) %}
            {% do assets.addJs('https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js', { 'group': 'head', 'priority': 70,'pipeline': false  }) %}
        
            {% if browser.getBrowser == 'msie' and browser.getVersion >= 8 and browser.getVersion <= 9 %}
                {% do assets.add('https://oss.maxcdn.com/respond/1.4.2/respond.min.js', 60) %}
                {% do assets.add('theme://js/html5shiv-printshiv.min.js', 50) %}
            {% endif %}
        {% endblock %}



        {% block assets deferred %}
            {{ assets.css()|raw }}
            {{ assets.js()|raw }}
        {% endblock %}

        {% block microdata %}
            {% include 'partials/microdata.html.twig' %}
        {% endblock %}

		<!-- Google Tag Manager -->
		<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-PPZ8MWP');</script>
		<!-- End Google Tag Manager -->


    {% endblock head%}
    
{% if not defined('grecaptcha_script_loaded') %}
    {% set grecaptcha_script_loaded = true %}
    <script src="https://www.google.com/recaptcha/api.js?render=6LeRLqUdAAAAAMmiqejEfhyrcV0lcAFIVXTq2oQ6&theme=light" async defer></script>
{% endif %}




    </head>

    <body class="fixed-nav {% block body_classes %}{{ page.header.body_classes }}{% endblock %}">
		<!-- Google Tag Manager (noscript) -->
		<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PPZ8MWP"
		height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
		<!-- End Google Tag Manager (noscript) ss -->

        {% block header_navigation %}
            {% include 'partials/navigation.html.twig' %}
        {% endblock %}


        {% block content %}{% endblock %}


	{% block footer %}
            {% include 'partials/footer.html.twig' %}
	{% endblock %}

    {% block bottom %}
        {{ assets.js('bottom')|raw }}
    {% endblock %}
    </body>
    <style type="text/css">
    	.grecaptcha-badge{
	    	opacity:0 !important;
	    	visibility:hidden !important;
    	}
    </style>

	{% if langswitcher.current == 'fr' %}

		<!--Start of Tawk.to Script-->
		<script type="text/javascript">
		var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
		(function(){
		var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
		s1.async=true;
		s1.src='https://embed.tawk.to/5f11c084a45e787d128ba210/default';
		s1.charset='UTF-8';
		s1.setAttribute('crossorigin','*');
		s0.parentNode.insertBefore(s1,s0);
		})();
		</script>
<!--End of Tawk.to Script-->

	{% else %}
		<!--Start of Tawk.to Script-->
		<script type="text/javascript">
		var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
		(function(){
		var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
		s1.async=true;
		s1.src='https://embed.tawk.to/5f11cccf7258dc118bee71b8/default';
		s1.charset='UTF-8';
		s1.setAttribute('crossorigin','*');
		s0.parentNode.insertBefore(s1,s0);
		})();
		</script>
<!--End of Tawk.to Script sla-->
<!--ns-->
	{% endif %}
<script>
   (function($){
    $(document).ready(function () {
        $('.close-button .close').on('click', function () {
            $('.navbar-collapse').removeClass('show');
        });
    });
})(jQuery);

</script>
</html>