<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/admin/languages/zh-cn.yaml',
    'modified' => 1730089692,
    'size' => 61156,
    'data' => [
        'PLUGIN_ADMIN' => [
            'ADMIN_NOSCRIPT_MSG' => '请在您的浏览器中启用 JavaScript。',
            'ADMIN_BETA_MSG' => '这是一个 Beta 版本！若在生产环境中使用，请自行承担责任。',
            'ADMIN_REPORT_ISSUE' => '遇到问题？请提交到 GitHub。',
            'EMAIL_FOOTER' => '<a href="https://getgrav.org">由 Grav 驱动</a> - 先进的纯文件 CMS',
            'LOGIN_BTN' => '登录',
            'LOGIN_BTN_FORGOT' => '忘记密码',
            'LOGIN_BTN_RESET' => '重置密码',
            'LOGIN_BTN_SEND_INSTRUCTIONS' => '发送重置指引',
            'LOGIN_BTN_CLEAR' => '清除表单',
            'LOGIN_BTN_CREATE_USER' => '创建用户',
            'LOGIN_LOGGED_IN' => '您已成功登录',
            'LOGIN_FAILED' => '登录失败',
            'LOGGED_OUT' => '您已退出登录',
            'RESET_NEW_PASSWORD' => '请输入一个新密码 &hellip;',
            'RESET_LINK_EXPIRED' => '重置链接已过期，请再试一次',
            'RESET_PASSWORD_RESET' => '密码已重置',
            'RESET_INVALID_LINK' => '密码重置链接已使用过，请再试一次',
            'FORGOT_INSTRUCTIONS_SENT_VIA_EMAIL' => '重置密码指引已经发送到你的邮箱',
            'FORGOT_FAILED_TO_EMAIL' => '重置密码指引发送失败，请稍后再试',
            'FORGOT_CANNOT_RESET_EMAIL_NO_EMAIL' => '因没有预留电子邮件地址，无法为 %s 重置密码',
            'FORGOT_USERNAME_DOES_NOT_EXIST' => '用户 <b>%s</b> 不存在',
            'FORGOT_EMAIL_NOT_CONFIGURED' => '无法重置密码。本站不能发送电子邮件',
            'FORGOT_EMAIL_SUBJECT' => '%s 的密码重置请求',
            'FORGOT_EMAIL_BODY' => '<h1>密码重置</h1><p>亲爱的 %1$s，</p><p>一个重置您的密码的请求在 <b>%4$s</b> 生成。</p><p><br /><a href="%2$s" class="btn-primary">点击此处重置密码</a><br /><br /></p><p>或复制以下链接到浏览器地址栏：</p> <p>%2$s</p><p><br />祝好,<br /><br />%3$s</p>',
            'MANAGE_PAGES' => '管理页面',
            'PAGES' => '页面',
            'PLUGINS' => '插件',
            'PLUGIN' => '插件',
            'THEMES' => '主题',
            'LOGOUT' => '登出',
            'BACK' => '返回',
            'NEXT' => '下一页',
            'PREVIOUS' => '上一页',
            'ADD_PAGE' => '添加页面',
            'MOVE' => '移动',
            'DELETE' => '删除',
            'UNSET' => '未设定',
            'VIEW' => '查看',
            'SAVE' => '保存',
            'NORMAL' => '标准',
            'EXPERT' => '专家',
            'EXPAND_ALL' => '全部展开',
            'COLLAPSE_ALL' => '全部折叠',
            'ERROR' => '错误',
            'CLOSE' => '关闭',
            'CANCEL' => '取消',
            'CONTINUE' => '继续',
            'CONFIRM' => '确认',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_TITLE' => '需要确认',
            'MODAL_CHANGED_DETECTED_TITLE' => '检测到更改',
            'MODAL_CHANGED_DETECTED_DESC' => '您有未保存的更改。 您确定要在不保存的情况下离开吗？',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_TITLE' => '需要确认',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_DESC' => '您确定要删除这个文件吗？此操作无法撤消。',
            'MODAL_UPDATE_GRAV_CONFIRMATION_REQUIRED_DESC' => '您将升级 Grav 到可用的最新版本。您想要继续吗？',
            'ADD_FILTERS' => '添加筛选器',
            'SEARCH_PAGES' => '搜索页面',
            'VERSION' => '版本',
            'WAS_MADE_WITH' => '制作',
            'BY' => '由',
            'UPDATE_THEME' => '更新主题',
            'UPDATE_PLUGIN' => '更新插件',
            'OF_THIS_THEME_IS_NOW_AVAILABLE' => '这一主题现在可用',
            'OF_THIS_PLUGIN_IS_NOW_AVAILABLE' => '这个插件现在可用',
            'AUTHOR' => '作者',
            'HOMEPAGE' => '首页',
            'DEMO' => '演示',
            'BUG_TRACKER' => 'Bug 追踪',
            'KEYWORDS' => '关键字',
            'LICENSE' => '许可证',
            'DESCRIPTION' => '说明',
            'README' => '自述文档',
            'DOCS' => '文档',
            'REMOVE_THEME' => '删除主题',
            'INSTALL_THEME' => '安装主题',
            'THEME' => '主题',
            'BACK_TO_THEMES' => '返回主题列表',
            'BACK_TO_PLUGINS' => '返回插件列表',
            'CHECK_FOR_UPDATES' => '检查更新',
            'ADD' => '增加',
            'CLEAR_CACHE' => '清除缓存',
            'CLEAR_CACHE_ALL_CACHE' => '所有缓存',
            'CLEAR_CACHE_ASSETS_ONLY' => '仅资源',
            'CLEAR_CACHE_IMAGES_ONLY' => '仅图片',
            'CLEAR_CACHE_CACHE_ONLY' => '仅缓存',
            'CLEAR_CACHE_TMP_ONLY' => '仅临时',
            'UPDATES_AVAILABLE' => '有可用的更新',
            'DAYS' => '天',
            'UPDATE' => '更新',
            'BACKUP' => '备份',
            'BACKUPS' => '备份',
            'BACKUP_NOW' => '立即备份',
            'BACKUPS_STATS' => '备份统计',
            'BACKUPS_HISTORY' => '备份历史',
            'BACKUPS_PURGE_CONFIG' => '备份清除配置',
            'BACKUPS_PROFILES' => '备份配置文件',
            'BACKUPS_COUNT' => '备份数量',
            'BACKUPS_PROFILES_COUNT' => '备份配置文件数量',
            'BACKUPS_TOTAL_SIZE' => '使用的空间',
            'BACKUPS_NEWEST' => '最新的备份',
            'BACKUPS_OLDEST' => '最早的备份',
            'BACKUPS_PURGE' => '清除',
            'BACKUPS_NOT_GENERATED' => '尚未生成备份…',
            'BACKUPS_PURGE_NUMBER' => '使用 %s 的 %s 备份插槽',
            'BACKUPS_PURGE_TIME' => '备份剩余 %s 天',
            'BACKUPS_PURGE_SPACE' => '已使用 %s，共 %s',
            'BACKUP_DELETED' => '备份已成功删除！',
            'BACKUP_NOT_FOUND' => '找不到备份',
            'BACKUP_DATE' => '备份日期',
            'STATISTICS' => '统计数据',
            'VIEWS_STATISTICS' => '页面查看统计',
            'TODAY' => '今天',
            'WEEK' => '星期',
            'MONTH' => '月',
            'LATEST_PAGE_UPDATES' => '最近更新',
            'MAINTENANCE' => '维护',
            'UPDATED' => '已更新',
            'MON' => '星期一',
            'TUE' => '星期二',
            'WED' => '星期三',
            'THU' => '星期四',
            'FRI' => '星期五',
            'SAT' => '星期六',
            'SUN' => '星期天',
            'COPY' => '复制',
            'EDIT' => '编辑',
            'CREATE' => '制作',
            'GRAV_ADMIN' => 'Grav 管理',
            'GRAV_OFFICIAL_PLUGIN' => 'Grav 官方插件',
            'GRAV_OFFICIAL_THEME' => 'Grav 官方主题',
            'PLUGIN_SYMBOLICALLY_LINKED' => '这个插件由软连接连接，无法检测到更新。',
            'THEME_SYMBOLICALLY_LINKED' => '这个主题由软连接连接，无法检测到更新。',
            'REMOVE_PLUGIN' => '删除插件',
            'INSTALL_PLUGIN' => '安装插件',
            'AVAILABLE' => '可选',
            'INSTALLED' => '已安装',
            'INSTALL' => '安装',
            'ACTIVE_THEME' => '当前主题',
            'SWITCHING_TO' => '切换到',
            'SWITCHING_TO_DESCRIPTION' => '切换到不同的主题时，无法保证所有页面排版均得到支持，在载入这些页面时可能会出现错误。',
            'SWITCHING_TO_CONFIRMATION' => '您想要继续并切换主题吗',
            'CREATE_NEW_USER' => '创建新用户',
            'REMOVE_USER' => '删除用户',
            'ACCESS_DENIED' => '访问被拒绝',
            'ACCOUNT_NOT_ADMIN' => '您的帐户没有管理员权限',
            'PHP_INFO' => 'PHP 信息',
            'INSTALLER' => '安装器',
            'AVAILABLE_THEMES' => '可用 的主题',
            'AVAILABLE_PLUGINS' => '可用的插件',
            'INSTALLED_THEMES' => '已安装的主题',
            'INSTALLED_PLUGINS' => '已安装的插件',
            'BROWSE_ERROR_LOGS' => '浏览错误日志',
            'SITE' => '站点',
            'INFO' => '信息',
            'SYSTEM' => '系统',
            'USER' => '用户',
            'ADD_ACCOUNT' => '添加帐户',
            'SWITCH_LANGUAGE' => '切换语言',
            'SUCCESSFULLY_ENABLED_PLUGIN' => '成功启用插件',
            'SUCCESSFULLY_DISABLED_PLUGIN' => '成功禁用插件',
            'SUCCESSFULLY_CHANGED_THEME' => '成功更改默认主题',
            'INSTALLATION_FAILED' => '安装失败',
            'INSTALLATION_SUCCESSFUL' => '安装成功',
            'UNINSTALL_FAILED' => '卸载失败',
            'UNINSTALL_SUCCESSFUL' => '卸载成功',
            'SUCCESSFULLY_SAVED' => '保存成功',
            'SUCCESSFULLY_COPIED' => '复制成功',
            'REORDERING_WAS_SUCCESSFUL' => '重新排序成功',
            'SUCCESSFULLY_DELETED' => '删除成功',
            'SUCCESSFULLY_SWITCHED_LANGUAGE' => '切换语言成功',
            'INSUFFICIENT_PERMISSIONS_FOR_TASK' => '您没有足够权限执行这个任务',
            'CACHE_CLEARED' => '缓存已清除',
            'METHOD' => '方法',
            'ERROR_CLEARING_CACHE' => '清除缓存出错',
            'AN_ERROR_OCCURRED' => '发生了一个错误',
            'YOUR_BACKUP_IS_READY_FOR_DOWNLOAD' => '您的备份已可下载',
            'DOWNLOAD_BACKUP' => '下载备份',
            'PAGES_FILTERED' => '页面已过滤',
            'NO_PAGE_FOUND' => '没有找到页面',
            'INVALID_PARAMETERS' => '无效的参数',
            'NO_FILES_SENT' => '没有文件被发送',
            'EXCEEDED_FILESIZE_LIMIT' => '超出 PHP 配置文件大小限制',
            'EXCEEDED_POSTMAX_LIMIT' => '超出 PHP 配置参数 post_max_size',
            'UNKNOWN_ERRORS' => '未知错误',
            'EXCEEDED_GRAV_FILESIZE_LIMIT' => '超出系统配置文件大小限制',
            'UNSUPPORTED_FILE_TYPE' => '不支持的文件类型',
            'FAILED_TO_MOVE_UPLOADED_FILE' => '移动上传文件失败',
            'FILE_UPLOADED_SUCCESSFULLY' => '文件已成功上传',
            'FILE_DELETED' => '文件已删除',
            'FILE_COULD_NOT_BE_DELETED' => '无法删除文件',
            'FILE_NOT_FOUND' => '找不到文件',
            'NO_FILE_FOUND' => '未找到文件',
            'FIELD_REORDER_SUCCESSFUL' => '为字段%s 更新媒体订单',
            'FIELD_REORDER_FAILED' => '存储字段 \'%s \' 的媒体订单时出错',
            'GRAV_WAS_SUCCESSFULLY_UPDATED_TO' => 'Grav 已成功更新到',
            'GRAV_UPDATE_FAILED' => 'Grav 更新失败',
            'EVERYTHING_UPDATED' => '一切都是最新版本',
            'UPDATES_FAILED' => '更新失败',
            'AVATAR_BY' => '头像来自',
            'AVATAR_UPLOAD_OWN' => '或上传你自己的...',
            'LAST_BACKUP' => '最后一次备份',
            'FULL_NAME' => '全名',
            'USERNAME' => '用户名',
            'EMAIL' => '邮箱地址',
            'USERNAME_EMAIL' => '用户名或电子邮件',
            'PASSWORD' => '密码',
            'PASSWORD_CONFIRM' => '确认密码',
            'TITLE' => '标题',
            'ACCOUNT' => '帐户',
            'EMAIL_VALIDATION_MESSAGE' => '必须是一个有效的电子邮件地址',
            'PASSWORD_VALIDATION_MESSAGE' => '密码必须包含至少一个数字和一个大写字母和小写字母，必须有 8 个或更多字符。',
            'LANGUAGE' => '语言',
            'LANGUAGE_HELP' => '设置偏好语言',
            'LANGUAGE_DEBUG' => '调试语言',
            'LANGUAGE_DEBUG_HELP' => '启用正在使用 |t Twig 过滤器的语言调试，添加一个可以被风格化的间距，用来帮助诊断问题。',
            'MEDIA' => '媒体',
            'DEFAULTS' => '默认值',
            'SITE_TITLE' => '网站标题',
            'SITE_TITLE_PLACEHOLDER' => '网站长标题',
            'SITE_TITLE_HELP' => '网站的默认标题，通常在主题中使用',
            'SITE_DEFAULT_LANG' => '默认语言',
            'SITE_DEFAULT_LANG_PLACEHOLDER' => '主题的<HTML>标记中要使用的默认语言',
            'SITE_DEFAULT_LANG_HELP' => '主题的<HTML>标记中要使用的默认语言',
            'DEFAULT_AUTHOR' => '默认作者',
            'DEFAULT_AUTHOR_HELP' => '默认作者名称，通常在主题或页面内容中使用',
            'DEFAULT_EMAIL' => '默认电子邮件',
            'DEFAULT_EMAIL_HELP' => '在主题和页面中默认引用的电子邮件',
            'TAXONOMY_TYPES' => '分类类型',
            'TAXONOMY_TYPES_HELP' => '如果你想在页面中使用分类类型，请在这里定义它们',
            'PAGE_SUMMARY' => '页面摘要',
            'ENABLED' => '已启用',
            'ENABLED_HELP' => '启用页面摘要 （摘要返回内容与页面内容相同）',
            'YES' => '是',
            'NO' => '否',
            'SUMMARY_SIZE' => '摘要大小',
            'SUMMARY_SIZE_HELP' => '用作内容摘要的页面字符数',
            'FORMAT' => '格式',
            'FORMAT_HELP' => '短：根据第一个分隔符或根据大小；长：忽略摘要分隔符',
            'SHORT' => '短',
            'LONG' => '长',
            'DELIMITER' => '分隔符',
            'DELIMITER_HELP' => '摘要分隔符 （默认为 \'===\'）',
            'METADATA' => '元数据',
            'METADATA_HELP' => '除非被页面覆盖, 否则将在每个页面上显示的默认元数据值',
            'NAME' => '名称',
            'CONTENT' => '内容',
            'SIZE' => '尺寸',
            'ACTION' => '行动',
            'REDIRECTS_AND_ROUTES' => '重定向和路由',
            'CUSTOM_REDIRECTS' => '自定义重定向',
            'CUSTOM_REDIRECTS_HELP' => '路由跳转到其他页面，标准正则替换有效',
            'CUSTOM_REDIRECTS_PLACEHOLDER_KEY' => '/your/alias',
            'CUSTOM_REDIRECTS_PLACEHOLDER_VALUE' => '/your/redirect',
            'CUSTOM_ROUTES' => '自定义路由',
            'CUSTOM_ROUTES_HELP' => '跳转到其他页面的路由，可以使用标准的正则表达式。',
            'CUSTOM_ROUTES_PLACEHOLDER_KEY' => '/your/alias',
            'CUSTOM_ROUTES_PLACEHOLDER_VALUE' => '/your/route',
            'FILE_STREAMS' => '文件流',
            'DEFAULT' => '默认',
            'PAGE_MEDIA' => '页面媒体',
            'OPTIONS' => '选项',
            'PUBLISHED' => '已发布',
            'PUBLISHED_HELP' => '默认情况下，页面将会发布。除非您设置了发布为false ，或publish_date设置了未来某时刻，或unpublish_date设置了过去某时刻',
            'DATE' => '日期',
            'DATE_HELP' => 'date变量允许您专门设置与此页面相关联的时间。',
            'PUBLISHED_DATE' => '发布时间',
            'PUBLISHED_DATE_HELP' => '可提供一个时间自动触发发布。',
            'UNPUBLISHED_DATE' => '取消发布时间',
            'UNPUBLISHED_DATE_HELP' => '可提供一个时间自动取消发布。',
            'ROBOTS' => '搜索引擎抓取规则Robots',
            'TAXONOMIES' => '分类法',
            'TAXONOMY' => '分类',
            'ADVANCED' => '高级选项',
            'SETTINGS' => '设置',
            'FOLDER_NUMERIC_PREFIX' => '文件夹数字前缀',
            'FOLDER_NUMERIC_PREFIX_HELP' => '手动排序和隐藏的数字前缀',
            'FOLDER_NAME' => '文件夹名称',
            'FOLDER_NAME_HELP' => '将存储此页的文件夹名称',
            'PARENT' => '上级页面',
            'DEFAULT_OPTION_ROOT' => '-根目录-',
            'DEFAULT_OPTION_SELECT' => '-请选择-',
            'DISPLAY_TEMPLATE' => '显示模板',
            'DISPLAY_TEMPLATE_HELP' => '转换此页为何种模板',
            'ORDERING' => '排序',
            'PAGE_ORDER' => '页面顺序',
            'OVERRIDES' => '覆盖',
            'MENU' => '菜单',
            'MENU_HELP' => '在菜单中使用的字符串。 如果不设置，标题将被使用。',
            'SLUG' => '标语',
            'SLUG_HELP' => 'Slug变量允许您专门设置 URL 的一部分',
            'SLUG_VALIDATE_MESSAGE' => 'Slug 必须包含只有小写字母数字字符和短划线',
            'PROCESS' => '处理',
            'PROCESS_HELP' => '控制如何处理页。单页设置优先于全局设置',
            'DEFAULT_CHILD_TYPE' => '默认子类型',
            'USE_GLOBAL' => '使用全局',
            'ROUTABLE' => '可路由的',
            'ROUTABLE_HELP' => '如果此页面可以从 URL 访问到',
            'CACHING' => '缓存',
            'VISIBLE' => '可见',
            'VISIBLE_HELP' => '决定一个页面是否在导航栏可见',
            'DISABLED' => '已禁用',
            'ITEMS' => '项目',
            'ORDER_BY' => '按顺序',
            'ORDER' => '顺序',
            'FOLDER' => '文件夹',
            'ASCENDING' => '升序',
            'DESCENDING' => '降序',
            'PAGE_TITLE' => '页面标题',
            'PAGE_TITLE_HELP' => '页面标题',
            'PAGE' => '页面',
            'FRONTMATTER' => 'Frontmatter',
            'FILENAME' => '文件名',
            'PARENT_PAGE' => '上级页面',
            'HOME_PAGE' => '首页',
            'HOME_PAGE_HELP' => '用作默认首页的页面',
            'DEFAULT_THEME' => '默认主题',
            'DEFAULT_THEME_HELP' => '设置 Grav 的默认主题（默认为 Antimatter）',
            'TIMEZONE' => '时区',
            'TIMEZONE_HELP' => '覆盖服务器默认时区',
            'SHORT_DATE_FORMAT' => '短日期格式',
            'SHORT_DATE_FORMAT_HELP' => '设定主题可使用的短日期格式',
            'LONG_DATE_FORMAT' => '长日期格式',
            'LONG_DATE_FORMAT_HELP' => '设定主题可使用的长日期格式',
            'DEFAULT_ORDERING' => '默认排序',
            'DEFAULT_ORDERING_HELP' => '除非例外，在列表中的页面将以此顺序呈现',
            'DEFAULT_ORDERING_DEFAULT' => '默认值 - 基于文件夹名称',
            'DEFAULT_ORDERING_FOLDER' => '文件夹 - 基于无前缀的文件夹名',
            'DEFAULT_ORDERING_TITLE' => '标题 - 基于头部信息中的标题',
            'DEFAULT_ORDERING_DATE' => '日期 - 基于头部信息中的日期',
            'DEFAULT_ORDER_DIRECTION' => '默认排序方向',
            'DEFAULT_ORDER_DIRECTION_HELP' => '列表中的页面方向',
            'DEFAULT_PAGE_COUNT' => '默认页面计数',
            'DEFAULT_PAGE_COUNT_HELP' => '默认的列表最大页面数',
            'DATE_BASED_PUBLISHING' => '基于日期发布',
            'DATE_BASED_PUBLISHING_HELP' => '根据日期自动发布或撤下文章',
            'EVENTS' => '事件',
            'EVENTS_HELP' => '启用或禁用特定事件。禁用可能导致插件崩溃。',
            'REDIRECT_DEFAULT_ROUTE' => '重定向默认路由',
            'REDIRECT_DEFAULT_ROUTE_HELP' => '自动重定向到页面默认路由',
            'LANGUAGES' => '语言',
            'SUPPORTED' => '已支持',
            'SUPPORTED_HELP' => '以逗号分隔，两个字母的语言代号（如 \'en,fr,de\'）',
            'SUPPORTED_PLACEHOLDER' => '例如: en, fr',
            'TRANSLATIONS_FALLBACK' => '多语言备选',
            'TRANSLATIONS_FALLBACK_HELP' => '在当前语言不存在时回退到支持的语言',
            'ACTIVE_LANGUAGE_IN_SESSION' => '在 Session 中设置语言',
            'ACTIVE_LANGUAGE_IN_SESSION_HELP' => '在 Session 中保存当前语言信息',
            'HTTP_HEADERS' => 'HTTP 标头',
            'EXPIRES' => '到期',
            'EXPIRES_HELP' => '设置过期标头。值以秒为单位。',
            'CACHE_CONTROL' => 'HTTP 缓存控制',
            'CACHE_CONTROL_HELP' => '设置为一个有效的缓存控制值，例如 `no-cache, no-store, must-revalidate`',
            'CACHE_CONTROL_PLACEHOLDER' => '例如: public, max-age=31536000',
            'LAST_MODIFIED' => '最后修改',
            'LAST_MODIFIED_HELP' => '设置最后修改标头有助于优化代理和浏览器缓存',
            'ETAG' => 'ETag',
            'ETAG_HELP' => '设置 etag 标头, 以帮助确定页面何时被修改',
            'VARY_ACCEPT_ENCODING' => '动态接受编码',
            'VARY_ACCEPT_ENCODING_HELP' => '设置 Vary: Accept Encoding 标头以有利于代理和 CDN 缓存',
            'MARKDOWN' => 'Markdown',
            'MARKDOWN_EXTRA' => 'Markdown extra',
            'MARKDOWN_EXTRA_HELP' => '启用 Markdown Extra 的默认支持 - https://michelf.ca/projects/php-markdown/extra/',
            'MARKDOWN_EXTRA_ESCAPE_FENCES' => '在 markdown extra 添加 HTML 元素',
            'MARKDOWN_EXTRA_ESCAPE_FENCES_HELP' => '在 markdown extra 添加 HTML 元素',
            'AUTO_LINE_BREAKS' => '自动换行',
            'AUTO_LINE_BREAKS_HELP' => '在 Markdown 中启用自动换行',
            'AUTO_URL_LINKS' => '自动 URL 链接',
            'AUTO_URL_LINKS_HELP' => '启用自动转换 URL 为 HTML 超链接',
            'ESCAPE_MARKUP' => '转义标记',
            'ESCAPE_MARKUP_HELP' => '将标记转义为 HTML 实体',
            'CACHING_HELP' => '全局 Grav 缓存开关',
            'CACHE_CHECK_METHOD' => '缓存检查方法',
            'CACHE_CHECK_METHOD_HELP' => '选择 Grav 用于检测页面是否有变动的方法',
            'CACHE_DRIVER' => '缓存驱动',
            'CACHE_DRIVER_HELP' => '选择 Grav 应该使用何种缓存驱动。“自动检测”会尝试为您找到最好的选择。',
            'CACHE_PREFIX' => '缓存前缀',
            'CACHE_PREFIX_HELP' => '属于 Grav key 一部分的标识符。除非您知道在做什么，否则不要修改。',
            'CACHE_PREFIX_PLACEHOLDER' => '衍生自基础URL(输入随机字符串覆盖)',
            'CACHE_PURGE_JOB' => '运行计划清除作业',
            'CACHE_PURGE_JOB_HELP' => '开启定时程序功能，您可以使用此作业定期清除旧的Doctrine文件缓存文件夹',
            'CACHE_CLEAR_JOB' => '运行计划清除任务',
            'CACHE_CLEAR_JOB_HELP' => '使用计划程序, 您可以定期清除 Grav 的缓存',
            'CACHE_JOB_TYPE' => '缓存作业类型',
            'CACHE_JOB_TYPE_HELP' => '要么清除 \'standard\' 文件夹缓存，要么使用 \'all\' 文件夹',
            'CACHE_PURGE' => '清除旧缓存',
            'LIFETIME' => '生命周期',
            'LIFETIME_HELP' => '以秒为单位设置缓存生命周期。0 为无限。',
            'GZIP_COMPRESSION' => 'Gzip 压缩',
            'GZIP_COMPRESSION_HELP' => '为 Grav 页面启用 GZip 压缩以改善性能',
            'TWIG_TEMPLATING' => 'Twig 模板',
            'TWIG_CACHING' => 'Twig 缓存',
            'TWIG_CACHING_HELP' => '控制 Twig 的缓存机制。设置为启用以获得最佳性能。',
            'TWIG_DEBUG' => 'Twig 调试',
            'TWIG_DEBUG_HELP' => '允许不加载 Twig 调试器扩展',
            'DETECT_CHANGES' => '检测更改',
            'DETECT_CHANGES_HELP' => '如果 Twig 检测到任何 Twig 模板的改变，会自动重新编译 Twig 缓存。',
            'AUTOESCAPE_VARIABLES' => '自动转义变量',
            'AUTOESCAPE_VARIABLES_HELP' => '自动转义所有变量。这很可能会让您的网站崩溃',
            'ASSETS' => '资源',
            'CSS_ASSETS' => 'CSS 资源',
            'CSS_PIPELINE' => 'CSS 管道',
            'CSS_PIPELINE_HELP' => 'CSS 管道为多个 CSS 资源合并成的一个文件',
            'CSS_PIPELINE_INCLUDE_EXTERNALS' => '在 CSS 管道中包含外部资源',
            'CSS_PIPELINE_INCLUDE_EXTERNALS_HELP' => '外部的URLs 有时有相对文件引用不应该管道化',
            'CSS_PIPELINE_BEFORE_EXCLUDES' => '先呈现 CSS 管道',
            'CSS_PIPELINE_BEFORE_EXCLUDES_HELP' => '在其他 CSS 参考未被包含时先呈现 CSS 管道',
            'CSS_MINIFY' => 'CSS 压缩',
            'CSS_MINIFY_HELP' => '在管道中压缩 CSS',
            'CSS_MINIFY_WINDOWS_OVERRIDE' => 'CSS 压缩 Windows 重写',
            'CSS_MINIFY_WINDOWS_OVERRIDE_HELP' => '对Windows平台进行调整。根据 ThreadStackSize，默认情况下为 False',
            'CSS_REWRITE' => 'CSS 重写',
            'CSS_REWRITE_HELP' => '管道化时重写任何 CSS 相对 URL',
            'JS_ASSETS' => 'JavaScript 资源',
            'JAVASCRIPT_PIPELINE' => 'JavaScript 管道化',
            'JAVASCRIPT_PIPELINE_HELP' => 'JavaScript管道化是统一多个JavaScript资源文件成为一个文件',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS' => '包括外部在JavaScript管道化',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS_HELP' => '外部 URL 有时有相对的文件引用，不应该被管道化',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES' => '优先渲染 JavaScript 管道',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES_HELP' => '在未被包含的其他 JavaScript 引用之前渲染 JavaScript管道',
            'JS_MODULE_ASSETS' => 'JavaScript 模块资源',
            'JAVASCRIPT_MODULE_PIPELINE' => 'JavaScript 模块管道',
            'JAVASCRIPT_MODULE_PIPELINE_HELP' => 'JavaScript 模块管道可统一多个 JavaScript 资源文件成为一个文件',
            'JAVASCRIPT_MODULE_PIPELINE_INCLUDE_EXTERNALS' => '在 JS 模块管道中包含外部元素',
            'JAVASCRIPT_MODULE_PIPELINE_INCLUDE_EXTERNALS_HELP' => '外部 URL 有时有相对的文件引用，不应管道化',
            'JAVASCRIPT_MODULE_PIPELINE_BEFORE_EXCLUDES' => '优先渲染 JS 模块管道',
            'JAVASCRIPT_MODULE_PIPELINE_BEFORE_EXCLUDES_HELP' => '在没有包含的其他 JS 引用之前渲染 JS 管道',
            'GENERAL_CONFIG' => '常规资源配置',
            'JAVASCRIPT_MINIFY' => 'JavaScript 压缩',
            'JAVASCRIPT_MINIFY_HELP' => '在管道化同时压缩 JavaScript',
            'ENABLED_TIMESTAMPS_ON_ASSETS' => '在资源上启用时间戳',
            'ENABLED_TIMESTAMPS_ON_ASSETS_HELP' => '启用在资源上的时间戳',
            'ENABLED_SRI_ON_ASSETS' => '在资源上启用 SRI',
            'ENABLED_SRI_ON_ASSETS_HELP' => '启用资源 SRI',
            'COLLECTIONS' => '集合',
            'ERROR_HANDLER' => '错误处理',
            'DISPLAY_ERRORS' => '显示错误',
            'DISPLAY_ERRORS_HELP' => '显示全回溯风格错误页面',
            'LOG_ERRORS' => '记录错误',
            'LOG_ERRORS_HELP' => '记录错误到 /logs 文件夹',
            'LOG_HANDLER' => '日志处理',
            'LOG_HANDLER_HELP' => '日志输出位置',
            'SYSLOG_FACILITY' => 'Syslog 特性',
            'SYSLOG_FACILITY_HELP' => 'Syslog 输出特性',
            'SYSLOG_TAG' => 'Syslog 标签',
            'SYSLOG_TAG_HELP' => 'Syslog 输出标签',
            'DEBUGGER' => '调试器',
            'DEBUGGER_HELP' => '启用Grav调试器和其设置',
            'DEBUG_TWIG' => '调试Twig',
            'DEBUG_TWIG_HELP' => '启用调试的Twig模版',
            'SHUTDOWN_CLOSE_CONNECTION' => '关机关闭连接',
            'SHUTDOWN_CLOSE_CONNECTION_HELP' => '关闭连接之前调用 onShutdown()。false 为调试',
            'DEFAULT_IMAGE_QUALITY' => '默认图像质量',
            'DEFAULT_IMAGE_QUALITY_HELP' => '采样或缓存图像时使用的默认图像质量 (85%)',
            'CACHE_ALL' => '缓存所有图片',
            'CACHE_ALL_HELP' => '通过Grav的缓存系统运行的所有图像，即使他们没有媒体操作',
            'IMAGES_DEBUG' => '图像调试水印',
            'IMAGES_DEBUG_HELP' => '显示overlay图像标示图像的像素数深度，例如当运行retina时',
            'IMAGES_LOADING' => '图像加载行为',
            'IMAGES_LOADING_HELP' => '加载属性允许浏览器推迟加载不在屏幕中的图像和iframes，直到用户滚动到它们的附近。加载支持三个值: auto, lazy, eager',
            'IMAGES_DECODING' => '图像解码行为',
            'IMAGES_DECODING_HELP' => '解码属性允许浏览器推迟解码不在屏幕中的图像和iframes，直到用户滚动到它们的附近。解码支持三个值: auto, sync, async',
            'IMAGES_SEOFRIENDLY' => 'SEO加速图片名',
            'IMAGES_SEOFRIENDLY_HELP' => '启用后, 首先显示图像名, 然后显示一个小哈希值, 以反映处理进度',
            'UPLOAD_LIMIT' => '文件上传限制',
            'UPLOAD_LIMIT_HELP' => '设置最大上传大小以位为单位(0 是无限的)',
            'ENABLE_MEDIA_TIMESTAMP' => '在媒体上启用时间戳',
            'ENABLE_MEDIA_TIMESTAMP_HELP' => '追加时间戳基于每个媒体项的最后修改日期',
            'SESSION' => 'Session',
            'SESSION_ENABLED_HELP' => '在 Grav 内启用 Session 支持',
            'SESSION_NAME_HELP' => '用来形成 Session Cookie 的名称标识符',
            'SESSION_UNIQUENESS' => '唯一字符串',
            'SESSION_UNIQUENESS_HELP' => 'Grav 根路径的 MD5 哈希值，如 `GRAV_ROOT` (默认值) 或基于随机 `security. salt` 字符串的哈希。',
            'ABSOLUTE_URLS' => '绝对网址',
            'ABSOLUTE_URLS_HELP' => '绝对或相对网址为 \'base_url\'',
            'PARAMETER_SEPARATOR' => '参数分隔符',
            'PARAMETER_SEPARATOR_HELP' => '传递参数的分隔符可以在 Windows上的Apache更改',
            'TASK_COMPLETED' => '任务已完成',
            'EVERYTHING_UP_TO_DATE' => '所有内容都已更新到最新',
            'UPDATES_ARE_AVAILABLE' => '有可用的更新',
            'IS_AVAILABLE_FOR_UPDATE' => '可进行更新',
            'IS_NOW_AVAILABLE' => '现在可用',
            'CURRENT' => '当前',
            'UPDATE_GRAV_NOW' => '立即更新 Grav',
            'GRAV_SYMBOLICALLY_LINKED' => 'Grav 由软连接连接。更新将不可用',
            'UPDATING_PLEASE_WAIT' => '更新……请稍候，正在下载',
            'OF_THIS' => '这',
            'OF_YOUR' => '您的',
            'HAVE_AN_UPDATE_AVAILABLE' => '有一个可用的更新',
            'SAVE_AS' => '另存为',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_DESC' => '是否确实要删除此页面及其所有子页面？如果页面被翻译成其他语言，这些译本将被保留而需要您分别单独删除。否则页面文件夹及其子页均会被删除。此操作无法撤消。',
            'AND' => '和',
            'UPDATE_AVAILABLE' => '有可用的更新',
            'METADATA_KEY' => '关键字 （例如 \'Keywords\'）',
            'METADATA_VALUE' => '值 （例如“博客”、“Grav”）',
            'USERNAME_HELP' => '用户名长度应为 3 到 16 个字符，允许使用小写字母、 数字、 下划线和连字符。不允许使用大写字母、 空格和特殊字符。',
            'FULLY_UPDATED' => '已完全更新',
            'SAVE_LOCATION' => '保存位置',
            'PAGE_FILE' => '页面模板',
            'PAGE_FILE_HELP' => '页面模版文件名称，并缺省为此页面的显示模版',
            'NO_USER_ACCOUNTS' => '没有用户帐户，请先创建一个......',
            'NO_USER_EXISTS' => '此帐户不存在本地用户, 无法保存...',
            'REDIRECT_TRAILING_SLASH' => '重定向结尾的斜杠',
            'REDIRECT_TRAILING_SLASH_HELP' => '执行 301 重定向而不是透明处理有结尾斜杠的 URI。',
            'DEFAULT_DATE_FORMAT' => '页面日期格式',
            'DEFAULT_DATE_FORMAT_HELP' => 'Grav 使用的页面日期格式。默认情况下，Grav 会尝试猜测您的日期格式，然而您可以用 PHP 的日期语法指定一个格式（例如 Y-m-d H:i）',
            'DEFAULT_DATE_FORMAT_PLACEHOLDER' => '自动猜测',
            'IGNORE_FILES' => '忽略文件',
            'IGNORE_FILES_HELP' => '处理页面时的需要忽略的特定文件',
            'IGNORE_FOLDERS' => '忽略文件夹',
            'IGNORE_FOLDERS_HELP' => '处理页面时的需要忽略的特定文件夹',
            'HIDE_EMPTY_FOLDERS' => '隐藏空文件夹',
            'HIDE_EMPTY_FOLDERS_HELP' => '如果目录中没有 .md 文件，将在导航中隐藏，就像“不可路由”一样',
            'HTTP_ACCEPT_LANGUAGE' => '根据浏览器设置语言',
            'HTTP_ACCEPT_LANGUAGE_HELP' => '您可以选择尝试基于浏览器中 `http_accept_language` 标头设置语言',
            'OVERRIDE_LOCALE' => '覆盖区域设置',
            'OVERRIDE_LOCALE_HELP' => '基于当前语言覆盖 PHP 区域设置',
            'REDIRECT' => '页面重定向',
            'REDIRECT_HELP' => '输入一个供这个页面重定向的页面路径或外部 URL，例如“/some/route”或“http://somesite.com”',
            'PLUGIN_STATUS' => '插件状态',
            'INCLUDE_DEFAULT_LANG' => '包括默认语言',
            'INCLUDE_DEFAULT_LANG_HELP' => '这会将默认语言添加到所有 URL 的头部。例如 `/en/blog/my-post`',
            'INCLUDE_DEFAULT_LANG_FILE_EXTENSION' => '在文件扩展名中包含默认语言',
            'INCLUDE_DEFAULT_LANG_HELP_FILE_EXTENSION' => '如果启用，它会在文件扩展名前加上默认语言(例如 `.en.md`)。禁用它以保持默认语言用 `.md` 作为文件扩展名。',
            'PAGES_FALLBACK_ONLY' => '仅备选页面',
            'PAGES_FALLBACK_ONLY_HELP' => '仅 “备选” 才能通过支持的语言找到页面内容，默认行为就是显示任何在活动语言缺失时找到的语言',
            'ALLOW_URL_TAXONOMY_FILTERS' => 'URL 分类过滤器',
            'ALLOW_URL_TAXONOMY_FILTERS_HELP' => '允许您通过 `/taxonomy:value` 筛选的基于页面的集合',
            'REDIRECT_DEFAULT_CODE' => '默认重定向状态码',
            'REDIRECT_DEFAULT_CODE_HELP' => '重定向的 HTTP 状态代码',
            'IGNORE_HIDDEN' => '忽略隐藏',
            'IGNORE_HIDDEN_HELP' => '忽略名称以一个点开头的所有文件和文件夹',
            'WRAPPED_SITE' => '嵌入的网站',
            'WRAPPED_SITE_HELP' => '为了让主题和插件知道 Grav 是否被另一个平台嵌入',
            'FALLBACK_TYPES' => '允许的备选类型',
            'FALLBACK_TYPES_HELP' => '通过页面路径访问允许发现的文件类型。默认为支持的媒体类型。',
            'INLINE_TYPES' => '内联备选类型',
            'INLINE_TYPES_HELP' => '应内联显示而不是下载的文件类型列表',
            'APPEND_URL_EXT' => '附加 URL 扩展名',
            'APPEND_URL_EXT_HELP' => '将添加定制后缀到页面的URL. 注意，这意味着Grav需要查找 `<template>.<extension>.twig` 的模板',
            'PAGE_MODES' => '页面模式',
            'PAGE_TYPES' => '页面类型',
            'PAGE_TYPES_HELP' => '确定 Grav 支持的页面类型. 如果请求不明确确定, 顺序返回到哪个类型',
            'ACCESS_LEVELS' => '访问级别',
            'GROUPS' => '组',
            'GROUPS_HELP' => '列表中的用户组的一部分',
            'ADMIN_ACCESS' => '管理访问权限',
            'SITE_ACCESS' => '网站访问权限',
            'INVALID_SECURITY_TOKEN' => '无效的安全令牌',
            'ACTIVATE' => '激活',
            'TWIG_UMASK_FIX' => 'Umask 修复',
            'TWIG_UMASK_FIX_HELP' => '缺省Twig创建缓存文件以 0755, 修改开关到 0775',
            'CACHE_PERMS' => '缓存的权限',
            'CACHE_PERMS_HELP' => '默认缓存文件夹权限。取决于安装设置，通常为 0755 或 0775',
            'REMOVE_SUCCESSFUL' => '删除成功',
            'REMOVE_FAILED' => '删除失败',
            'HIDE_HOME_IN_URLS' => '在 URL 中隐藏主页路径',
            'HIDE_HOME_IN_URLS_HELP' => '举例：若 /blog 被设置为主页，那当开打这个选项时，/blog/page 将可以通过 /page 访问（即 /blog 这段路径将被隐藏）',
            'TWIG_FIRST' => '优先处理Twig',
            'TWIG_FIRST_HELP' => '如果您启用 Twig 页面处理，您可以在 markdown 之前或之后配置 Twig 处理',
            'SESSION_SECURE' => '仅使用 HTTPS 传输 Cookie',
            'SESSION_SECURE_HELP' => '如果为 true，则指示此 cookie 的通信必须在加密传输。警告︰ 启用此功能只有在完全在 HTTPS 运行的网站上',
            'SESSION_HTTPONLY' => '仅使用 HTTP 传输 Cookie',
            'SESSION_HTTPONLY_HELP' => '如果 true，表示 cookies 只能通过 HTTP 使用，并且不允许 JavaScript 修改',
            'REVERSE_PROXY' => '反向代理',
            'REVERSE_PROXY_HELP' => '如果你在反向代理之后，并且你的URL包含不正确的端口，启用此项',
            'INVALID_FRONTMATTER_COULD_NOT_SAVE' => '无效 frontmatter，不能保存',
            'ADD_FOLDER' => '添加文件夹',
            'COPY_PAGE' => '复制页面',
            'PROXY_URL' => '代理 URL',
            'PROXY_URL_HELP' => '输入代理服务器主机或 IP 和端口',
            'PROXY_CERT' => '代理证书路径',
            'PROXY_CERT_HELP' => '包含代理证书pem文件的文件夹的本地路径',
            'NOTHING_TO_SAVE' => '没有要保存的数据',
            'FILE_ERROR_ADD' => '试图添加文件时出错',
            'FILE_ERROR_UPLOAD' => '尝试上载文件时出错',
            'FILE_UNSUPPORTED' => '不支持的文件类型',
            'ADD_ITEM' => '添加项目',
            'FILE_TOO_LARGE' => '文件太大，无法上传，最大允许 %s 根据您的 PHP 设置 <br>。增加你 \'post_max_size\' PHP 设置',
            'INSTALLING' => '正在安装',
            'LOADING' => '正在加载…',
            'DEPENDENCIES_NOT_MET_MESSAGE' => '需要先满足以下依赖：',
            'ERROR_INSTALLING_PACKAGES' => '安装程序包时出错',
            'INSTALLING_DEPENDENCIES' => '安装依赖项...',
            'INSTALLING_PACKAGES' => '安装程序包...',
            'PACKAGES_SUCCESSFULLY_INSTALLED' => '成功安装的程序包。',
            'READY_TO_INSTALL_PACKAGES' => '准备安装程序包',
            'PACKAGES_NOT_INSTALLED' => '软件包未安装',
            'PACKAGES_NEED_UPDATE' => '软件包已经安装，但太旧了',
            'PACKAGES_SUGGESTED_UPDATE' => '软件包已安装，版本没有问题。但将会更新以保持更新',
            'REMOVE_THE' => '删除 %s',
            'CONFIRM_REMOVAL' => '是否确实要删除此 %s？',
            'REMOVED_SUCCESSFULLY' => '%s 已成功删除',
            'ERROR_REMOVING_THE' => '删除 %s 时出错',
            'ADDITIONAL_DEPENDENCIES_CAN_BE_REMOVED' => '%s 所需以下的依存关系，不需要其他已安装的软件包。如果您不使用它们，您可以直接从这里删除它们。',
            'READY_TO_UPDATE_PACKAGES' => '准备更新程序包',
            'ERROR_UPDATING_PACKAGES' => '更新数据包时出现错误',
            'UPDATING_PACKAGES' => '正在更新软件包',
            'PACKAGES_SUCCESSFULLY_UPDATED' => '软件包成功更新。',
            'UPDATING' => '更新中',
            'GPM_SECTION' => 'GPM 部分',
            'GPM_RELEASES' => 'GPM 发行版',
            'GPM_RELEASES_HELP' => '选择\'Testing\'来安装Beta版或测试版本',
            'GPM_METHOD' => '远程读取方法',
            'GPM_METHOD_HELP' => '当设置为自动，Grav将确定 fopen 可用和使用它，否则回退到cURL。要强制使用一个或其他切换设置。',
            'HTTP_SECTION' => 'HTTP 部分',
            'SSL_ENABLE_PROXY' => '启用代理服务器',
            'SSL_VERIFY_PEER' => '远程验证节点',
            'SSL_VERIFY_PEER_HELP' => '有些可能无法验证 SSL 证书',
            'SSL_VERIFY_HOST' => '远程验证主机',
            'SSL_VERIFY_HOST_HELP' => '有些可能无法验证 SSL 证书',
            'HTTP_CONNECTIONS' => 'HTTP 连接',
            'HTTP_CONNECTIONS_HELP' => '多路请求中并发的 HTTP 连接数',
            'MISC_SECTION' => '杂项部分',
            'AUTO' => '自动',
            'FOPEN' => 'fopen',
            'CURL' => 'cURL',
            'STABLE' => '稳定',
            'TESTING' => '测试中',
            'FRONTMATTER_PROCESS_TWIG' => '处理 frontmatter Twig',
            'FRONTMATTER_PROCESS_TWIG_HELP' => '当启用时你可以在页中frontmatter使用Twig设定变量',
            'FRONTMATTER_IGNORE_FIELDS' => '忽略frontmatter域',
            'FRONTMATTER_IGNORE_FIELDS_HELP' => '某些frontmatter域可能包含Twig，但不应被处理，如 \'表单\'',
            'FRONTMATTER_IGNORE_FIELDS_PLACEHOLDER' => '例如: forms',
            'PACKAGE_X_INSTALLED_SUCCESSFULLY' => '成功安装包 %s',
            'ORDERING_DISABLED_BECAUSE_PARENT_SETTING_ORDER' => '父设置排序，排序失效',
            'ORDERING_DISABLED_BECAUSE_PAGE_NOT_VISIBLE' => '不可见的页面，排序失效',
            'ORDERING_DISABLED_BECAUSE_TOO_MANY_SIBLINGS' => '通过管理员排序是不受支持的，因为多过200 多个兄弟节点',
            'ORDERING_DISABLED_BECAUSE_PAGE_NO_PREFIX' => '此页的页面排序被禁用，因为 <strong>文件夹数字前缀</strong> 不启用',
            'CANNOT_ADD_MEDIA_FILES_PAGE_NOT_SAVED' => '注： 您不能添加媒体文件，直到您保存网页。只需单击保存在上面',
            'CANNOT_ADD_FILES_PAGE_NOT_SAVED' => '注意： 网页必须保存之前你可以将文件上载到它。',
            'DROP_FILES_HERE_TO_UPLOAD' => '将要上传的文件拖放到这里或直接<strong>单击此区域上传</strong>',
            'INSERT' => '插入',
            'UNDO' => '取消',
            'REDO' => '重做',
            'HEADERS' => '头信息',
            'BOLD' => '粗体',
            'ITALIC' => '斜体',
            'STRIKETHROUGH' => '删除线',
            'SUMMARY_DELIMITER' => '摘要分隔符',
            'LINK' => '链接',
            'IMAGE' => '图片',
            'BLOCKQUOTE' => '块引用',
            'UNORDERED_LIST' => '无序列表',
            'ORDERED_LIST' => '有序列表',
            'EDITOR' => '编辑器',
            'PREVIEW' => '预览',
            'FULLSCREEN' => '全屏',
            'NON_ROUTABLE' => '非路由',
            'NON_VISIBLE' => '非可见',
            'NON_PUBLISHED' => '非发布',
            'CHARACTERS' => '字符',
            'PUBLISHING' => '发布',
            'MEDIA_TYPES' => '媒体类型',
            'IMAGE_OPTIONS' => '图像选项',
            'MIME_TYPE' => 'Mime 类型',
            'THUMB' => '缩略图',
            'TYPE' => '类型',
            'FILE_EXTENSION' => '文件扩展名',
            'LEGEND' => '页面图例',
            'MEMCACHE_SERVER' => 'Memcache 服务器',
            'MEMCACHE_SERVER_HELP' => 'Memcache 服务器地址',
            'MEMCACHE_PORT' => 'Memcache 端口',
            'MEMCACHE_PORT_HELP' => 'Memcache 服务器端口',
            'MEMCACHED_SERVER' => 'Memcached 服务器',
            'MEMCACHED_SERVER_HELP' => 'Memcached 服务器地址',
            'MEMCACHED_PORT' => 'Memcached 端口',
            'MEMCACHED_PORT_HELP' => 'Memcached 服务器端口',
            'REDIS_SERVER' => 'Redis 服务器',
            'REDIS_SERVER_HELP' => 'Redis 服务器地址',
            'REDIS_PORT' => 'Redis 端口',
            'REDIS_PORT_HELP' => 'Redis 服务器端口',
            'REDIS_PASSWORD' => 'Redis 密码/密文',
            'REDIS_DATABASE' => 'Redis 数据库 ID',
            'REDIS_DATABASE_HELP' => 'Redis 实例数据库 ID',
            'ALL' => '所有',
            'FROM' => '从',
            'TO' => '到',
            'RELEASE_DATE' => '发行日期',
            'SORT_BY' => '排序方式',
            'RESOURCE_FILTER' => '筛选器...',
            'FORCE_SSL' => '强制使用 SSL',
            'FORCE_SSL_HELP' => '全局强制 SSL，如果通过 HTTP访问站点时启用， Grav 将重定向到 HTTPS 页面',
            'NEWS_FEED' => '新闻源',
            'EXTERNAL_URL' => '外部 URL',
            'SESSION_SAMESITE' => '会话 SameSite 属性',
            'SESSION_SAMESITE_HELP' => 'Lax|Strict|None。更多信息见 https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/sameSite。',
            'CUSTOM_BASE_URL' => '自定义的基 URL',
            'CUSTOM_BASE_URL_HELP' => '如果你想要重写网站域或使用的Grav的不同子文件夹，使用。示例： http://localhost',
            'FILEUPLOAD_PREVENT_SELF' => '不能使用"%s"在页面之外。',
            'FILEUPLOAD_UNABLE_TO_UPLOAD' => '无法上载文件 %s: %s',
            'FILEUPLOAD_UNABLE_TO_MOVE' => '无法将移到"%s"的文件 %s',
            'DROPZONE_CANCEL_UPLOAD' => '取消上传',
            'DROPZONE_CANCEL_UPLOAD_CONFIRMATION' => '你确定你想要取消这个上传？',
            'DROPZONE_DEFAULT_MESSAGE' => '将要上传的文件拖放到这里或直接<strong>单击此区域上传</strong>',
            'DROPZONE_FALLBACK_MESSAGE' => '您的浏览器并不支持拖放文件上传。',
            'DROPZONE_FALLBACK_TEXT' => '请使用下面的回退表单上传你的文件',
            'DROPZONE_FILE_TOO_BIG' => '文件太大 ({{filesize}}MiB)。最大文件大小： {{maxFilesize}}MiB。',
            'DROPZONE_INVALID_FILE_TYPE' => '你不能上传此类型的文件。',
            'DROPZONE_MAX_FILES_EXCEEDED' => '您可以上传任何更多的文件。',
            'DROPZONE_REMOVE_FILE' => '删除文件',
            'DROPZONE_RESPONSE_ERROR' => '服务器响应 {{statusCode}} 代码。',
            'PREMIUM_PRODUCT' => '高级',
            'DESTINATION_NOT_SPECIFIED' => '未指定目标',
            'UPLOAD_ERR_NO_TMP_DIR' => '缺少临时文件夹',
            'SESSION_SPLIT' => 'Session 拆分',
            'SESSION_SPLIT_HELP' => '在网站和其他插件程序（如管理面板）之间独立拆分 Session',
            'ERROR_FULL_BACKTRACE' => '完整的回溯错误',
            'ERROR_SIMPLE' => '简单错误',
            'ERROR_SYSTEM' => '系统错误',
            'IMAGES_AUTO_FIX_ORIENTATION' => '自动修复方向',
            'IMAGES_AUTO_FIX_ORIENTATION_HELP' => '基于 Exif 数据自动修复图像方向',
            'REDIS_SOCKET' => 'Redis 套接字',
            'REDIS_SOCKET_HELP' => 'Redis 套接字',
            'NOT_SET' => '未设置',
            'PERMISSIONS' => '权限',
            'NEVER_CACHE_TWIG' => '从不缓存Twig',
            'NEVER_CACHE_TWIG_HELP' => '只缓存内容并每次处理Twig。将忽略 twig_first 设置。',
            'ALLOW_WEBSERVER_GZIP' => '允许 Web 服务器 Gzip',
            'ALLOW_WEBSERVER_GZIP_HELP' => '默认是关闭的。当启用时，web 服务器配置 Gzip/Deflate 压缩会工作，但 http 连接不会造成页面加载速度较慢的 onShutDown() 事件之前被关闭',
            'OFFLINE_WARNING' => '不能建立到 GPM 的连接',
            'CLEAR_IMAGES_BY_DEFAULT' => '在默认情况下清除图像缓存',
            'CLEAR_IMAGES_BY_DEFAULT_HELP' => '默认情况下，所有的缓存清理会清除掉已经处理理过的图像，也能将其禁用',
            'CLI_COMPATIBILITY' => 'CLI 兼容性',
            'CLI_COMPATIBILITY_HELP' => '确保使用唯一的非易失性缓存驱动程序（文件，Redis，memcache，等等）。',
            'REINSTALL_PLUGIN' => '重新安装插件',
            'REINSTALL_THEME' => '重新安装主题',
            'REINSTALL_THE' => '重新安装 %s',
            'CONFIRM_REINSTALL' => '你确定你想要重新安装此 %s？',
            'REINSTALLED_SUCCESSFULLY' => '%s 成功地重新安装',
            'ERROR_REINSTALLING_THE' => '重新安装 %s 的错误',
            'PACKAGE_X_REINSTALLED_SUCCESSFULLY' => '软件包 %s 成功重新安装',
            'REINSTALLATION_FAILED' => '重新安装失败',
            'WARNING_REINSTALL_NOT_LATEST_RELEASE' => '已安装的版本不是最新的版本。通过单击继续，您会删除当前的版本并安装最新的可用版本',
            'TOOLS' => '工具',
            'DIRECT_INSTALL' => '直接安装',
            'NO_PACKAGE_NAME' => '未指定的包名称',
            'PACKAGE_EXTRACTION_FAILED' => '包提取失败',
            'NOT_VALID_GRAV_PACKAGE' => '不是有效的Grav包',
            'NAME_COULD_NOT_BE_DETERMINED' => '名称不能确定',
            'CANNOT_OVERWRITE_SYMLINKS' => '不能覆盖符号链接',
            'ZIP_PACKAGE_NOT_FOUND' => '找不到 ZIP 包',
            'GPM_OFFICIAL_ONLY' => '仅官方 GPM',
            'GPM_OFFICIAL_ONLY_HELP' => '仅允许直接从官方 GPM 仓库安装。',
            'NO_CHILD_TYPE' => '此 rawroute 没有子类型',
            'SORTABLE_PAGES' => '可排序的页面：',
            'UNSORTABLE_PAGES' => '页面不可排序',
            'ADMIN_SPECIFIC_OVERRIDES' => '管理特定优先规则',
            'ADMIN_CHILDREN_DISPLAY_ORDER' => '子页面的显示顺序',
            'ADMIN_CHILDREN_DISPLAY_ORDER_HELP' => '此页的子页面在页面视图中的显示顺序',
            'PWD_PLACEHOLDER' => '复杂的字符串至少 8 字符长',
            'PWD_REGEX' => '密码的正则表达式',
            'PWD_REGEX_HELP' => '默认情况下： 密码必须包含至少一个数字和一个大写和小写字母和至少 8 个或更多字符',
            'USERNAME_PLACEHOLDER' => '限小写字符, 如‘admin’',
            'USERNAME_REGEX' => '用户名正则表达式',
            'USERNAME_REGEX_HELP' => '默认情况下： 只有小写字符、 数字、 短划线和下划线。3-16 个字符',
            'ENABLE_AUTO_METADATA' => '从 Exif 自动获取元数据',
            'ENABLE_AUTO_METADATA_HELP' => '通过 Exif 信息自动生成图像元数据文件',
            '2FA_TITLE' => '双因素身份验证',
            '2FA_INSTRUCTIONS' => '#####双因素身份验证
您已在此账户启用**双因素身份验证**。建议您使用**双因素身份验证** 应用程序，输入当前**6位代码** 以便完成登录。',
            '2FA_REGEN_HINT' => '重设密码将重新登陆',
            '2FA_LABEL' => '管理访问权限',
            '2FA_FAILED' => '无效的双因素身份验证代码, 请重试…',
            '2FA_ENABLED' => '启用双因素身份验证',
            '2FA_CODE_INPUT' => '000000',
            '2FA_SECRET' => '双因素身份验证口令',
            '2FA_SECRET_HELP' => '将此QR码扫描到您的[Authenticator App]（https://learn.getgrav.org/admin-panel/2fa#apps）。此外，将秘密备份在安全位置是个好主意，以防需要重新安装应用程序。查看[ Grav文档]（https://learn.getgrav.org/admin-panel/2fa）以获取更多信息 ',
            '2FA_REGENERATE' => '重新生成',
            'YUBIKEY_ID' => 'Yubikey ID',
            'YUBIKEY_OTP_INPUT' => 'YubiKey OTP',
            'YUBIKEY_HELP' => '将您的 YubiKey 插入您的计算机，然后单击按钮生成一个 OTP。 前12个字符是您的客户端ID，将被保存。',
            'FORCE_LOWERCASE_URLS' => '强制小写 url',
            'FORCE_LOWERCASE_URLS_HELP' => '默认情况下为小写，设置为false时可以使用大写字母',
            'INTL_ENABLED' => 'Intl模块集成',
            'INTL_ENABLED_HELP' => '使用intl php模块并以UTF-8作为编码基础',
            'VIEW_SITE_TIP' => '查看站点',
            'TOOLS_DIRECT_INSTALL_TITLE' => '直接安装 Grav 软件包',
            'TOOLS_DIRECT_INSTALL_UPLOAD_TITLE' => '通过直接ZIP上传安装软件包',
            'TOOLS_DIRECT_INSTALL_UPLOAD_DESC' => '您可以通过此方法轻松安装有效的 Grav  <strong>主题</strong>，<strong>插件</strong>，甚至是<strong> Grav </strong>更新 Zip 包。这个软件包不需要通过 GPM 注册，并允许您轻松回滚到之前的版本或安装进行测试。',
            'TOOLS_DIRECT_INSTALL_URL_TITLE' => '通过远程链接来安装软件包',
            'TOOLS_DIRECT_INSTALL_URL_DESC' => '或者，你也可以参考zip压缩包的完整路径 来远程安装',
            'TOOLS_DIRECT_INSTALL_UPLOAD_BUTTON' => '上传并安装',
            'ROUTE_OVERRIDES' => '路由覆盖',
            'ROUTE_DEFAULT' => '默认路由',
            'ROUTE_CANONICAL' => 'Canonical 路由',
            'ROUTE_ALIASES' => '路由别名',
            'OPEN_NEW_TAB' => '在新标签页中打开',
            'SESSION_INITIALIZE' => '执行 Session 初始化',
            'SESSION_INITIALIZE_HELP' => '允许 Grav 开启 Session。此功能是使任何用户交互正常工作所必需的, 例如登录、窗体等。插件“管理面板”不受此设置的影响。',
            'STRICT_YAML_COMPAT' => 'YAML 兼容',
            'STRICT_YAML_COMPAT_HELP' => '如果原生的或 3.4 版本解析器执行失败，退回 Symfonty 2.4 YAML 解析器',
            'STRICT_TWIG_COMPAT' => 'Twig 兼容',
            'STRICT_TWIG_COMPAT_HELP' => '启用已弃用的 Twig 自动转义设置。 禁用时, 需要 `|raw` 过滤器才能输出 HTML，因为 Twig 将自动转义输出',
            'SCHEDULER' => '调度器',
            'SCHEDULER_INSTALL_INSTRUCTIONS' => '安装说明',
            'SCHEDULER_INSTALLED_READY' => '已安装并准备就绪',
            'SCHEDULER_CRON_NA' => '该用户不可用 Cron : <b>%s</b>',
            'SCHEDULER_NOT_ENABLED' => '未对用户启用： <b>%s</b>',
            'SCHEDULER_SETUP' => '调度器设置',
            'SCHEDULER_INSTRUCTIONS' => '<b>Grav 调度器</b> 允许您创建和计划自定义作业。 它还为 Grav 插件提供了一种以编程方式集成和动态添加定期运行的作业的方法。',
            'SCHEDULER_POST_INSTRUCTIONS' => '要启用调度器的功能， 您必须为 <b>%s</b> 用户将 <b>Grav 调度器</b> 添加到系统的 crontab文件中。 从终端上方运行命令自动添加。保存后刷新此页面以查看状态。',
            'SCHEDULER_JOBS' => '自定义调度任务',
            'SCHEDULER_STATUS' => '调度状态',
            'SCHEDULER_RUNAT' => '运行于',
            'SCHEDULER_RUNAT_HELP' => 'Cron 格式化的 \'at\' 语法。注意：所有时间都是 UTC！',
            'SCHEDULER_OUTPUT' => '输出文件',
            'SCHEDULER_OUTPUT_HELP' => '输出文件的路径名 (Grav 安装的根目录)',
            'SCHEDULER_OUTPUT_TYPE' => '输出类型',
            'SCHEDULER_OUTPUT_TYPE_HELP' => '每次运行时追加到同一文件, 或在每次运行时覆盖该文件',
            'SCHEDULER_EMAIL' => '电子邮件',
            'SCHEDULER_EMAIL_HELP' => '电子邮件发送到输出到。注: 需要设置输出文件',
            'SCHEDULER_WARNING' => '调度程序使用您系统的 crontab 来执行命令。 只有当您是专家用户并知道您正在做什么时，您才应该使用这个。配置错误或滥用可能导致安全漏洞。',
            'SECURITY' => '安全',
            'XSS_SECURITY' => '内容 XSS 安全性',
            'XSS_WHITELIST_PERMISSIONS' => 'XSS 白名单权限',
            'XSS_WHITELIST_PERMISSIONS_HELP' => '有以下权限的用户在保存内容时将跳过 XSS 规则',
            'XSS_ON_EVENTS' => '事件触发时过滤',
            'XSS_INVALID_PROTOCOLS' => '过滤非法的协议',
            'XSS_INVALID_PROTOCOLS_LIST' => '无效的协议列表',
            'XSS_MOZ_BINDINGS' => '过滤 Moz bindings',
            'XSS_HTML_INLINE_STYLES' => '过滤 HTML 内联样式',
            'XSS_DANGEROUS_TAGS' => '过滤危险的 HTML 标签',
            'XSS_DANGEROUS_TAGS_LIST' => '危险的 HTML 标签列表',
            'XSS_ONSAVE_ISSUE' => '保存失败：检测到 XSS 问题...',
            'XSS_ISSUE' => '<strong>警告：</strong> 在 <strong>%s</strong> 发现了潜在的 XSS 问题',
            'UPLOADS_SECURITY' => '上传安全性',
            'UPLOADS_DANGEROUS_EXTENSIONS' => '危险的扩展名',
            'UPLOADS_DANGEROUS_EXTENSIONS_HELP' => '禁止上传这些扩展名，忽略接受的 MIME 类型',
            'REPORTS' => '报告',
            'LOGS' => '日志',
            'LOG_VIEWER_FILES' => '日志查看器文件',
            'LOG_VIEWER_FILES_HELP' => '在 "工具-日志" 中将能够查看 /logs/目录中的文件。例如 \'grav\' = /logs/grav.log',
            'BACKUPS_STORAGE_PURGE_TRIGGER' => '备份存储清除触发器',
            'BACKUPS_MAX_COUNT' => '最大备份数量',
            'BACKUPS_MAX_COUNT_HELP' => '0 = 无限',
            'BACKUPS_MAX_SPACE' => '最大备份空间',
            'BACKUPS_MAX_RETENTION_TIME' => '保留时间最大值',
            'BACKUPS_MAX_RETENTION_TIME_APPEND' => '单位: 天',
            'BACKUPS_PROFILE_NAME' => '备份名称',
            'BACKUPS_PROFILE_ROOT_FOLDER' => '根文件夹',
            'BACKUPS_PROFILE_ROOT_FOLDER_HELP' => '可以是绝对路径或流',
            'BACKUPS_PROFILE_EXCLUDE_PATHS' => '排除路径',
            'BACKUPS_PROFILE_EXCLUDE_PATHS_HELP' => '要排除的绝对路径, 每行一个',
            'BACKUPS_PROFILE_EXCLUDE_FILES' => '排除文件',
            'BACKUPS_PROFILE_EXCLUDE_FILES_HELP' => '要排除的特定文件或文件夹, 每行一个',
            'BACKUPS_PROFILE_SCHEDULE' => '启用计划任务',
            'BACKUPS_PROFILE_SCHEDULE_AT' => '运行计划的任务',
            'COMMAND' => '命令',
            'EXTRA_ARGUMENTS' => '额外参数：',
            'DEFAULT_LANG' => '覆盖默认语言',
            'DEFAULT_LANG_HELP' => '默认是第一个支持的语言。可以通过设置此选项来覆盖，但它必须是支持的语言之一。',
            'DEBUGGER_PROVIDER' => '调试器提供者',
            'DEBUGGER_PROVIDER_HELP' => '默认是 PHP 调试栏，但 Clockwork 浏览器扩展提供了侵入性较低的方法',
            'DEBUGGER_DEBUGBAR' => 'PHP 调试栏',
            'DEBUGGER_CLOCKWORK' => 'Clockwork 浏览器扩展',
            'PAGE_ROUTE_NOT_FOUND' => '找不到页面路由',
            'PAGE_ROUTE_FOUND' => '已找到页面路由',
            'NO_ROUTE_PROVIDED' => '未提供路由',
            'CONTENT_LANGUAGE_FALLBACKS' => '内容语言回退',
            'CONTENT_LANGUAGE_FALLBACKS_HELP' => '默认情况下，如果内容没有被翻译，Grav将以默认语言显示内容。使用此设置来覆盖每个语言基础。',
            'CONTENT_LANGUAGE_FALLBACK' => '备用语言',
            'CONTENT_LANGUAGE_FALLBACK_HELP' => '请输入语言代码列表。请注意，如果您省略了默认语言代码，将不会生效。',
            'CONTENT_FALLBACK_LANGUAGE_HELP' => '指定您想要自定义的语言代码。',
            'EXPERIMENTAL' => '实验性功能',
            'PAGES_TYPE' => '前端页面类型',
            'PAGES_TYPE_HELP' => '此选项在前端启用弹性对象页面。管理界面的弹性页面需要弹性对象(Flex Objects)插件。',
            'ACCOUNTS_TYPE' => '帐号类型',
            'ACCOUNTS_TYPE_HELP' => '存储用户帐户的弹性对象系统',
            'ACCOUNTS_STORAGE' => '帐号存储',
            'ACCOUNTS_STORAGE_HELP' => '用于弹性对象帐户类型的存储机制。“文件”是传统的方法，将帐户存储在 YAML 文件中的单个文件夹中。 “文件夹”则为每个帐户创建一个新文件夹。',
            'FLEX' => '弹性对象(实验性)',
            'REGULAR' => '常规',
            'FILE' => '文件',
            'SANITIZE_SVG' => 'SVG 消毒',
            'SANITIZE_SVG_HELP' => '从 SVG 中移除任何 XSS 代码',
            'ACCOUNTS' => '帐号',
            'USER_ACCOUNTS' => '用户账号',
            'USER_GROUPS' => '用户组',
            'GROUP_NAME' => '组名称',
            'DISPLAY_NAME' => '显示名称',
            'ICON' => '图标',
            'ACCESS' => '访问权限',
            'NO_ACCESS' => '无访问权限',
            'SUPER_USER' => '超级用户',
            'ALLOWED' => '已允许',
            'DENIED' => '已禁止',
            'MODULE' => '模块',
            'NON_MODULE' => '无模块',
            'ADD_MODULE' => '添加模块',
            'MODULE_SETUP' => '模块设置',
            'MODULE_TEMPLATE' => '模块模板',
            'ADD_MODULE_CONTENT' => '添加模块内容',
            'CHANGELOG' => '更新日志',
            'PAGE_ACCESS' => '页面访问',
            'PAGE PERMISSIONS' => '页面权限',
            'PAGE_ACCESS_HELP' => '具有以下访问权限的用户可以访问页面。',
            'PAGE_VISIBILITY_REQUIRES_ACCESS' => '菜单可见性要求访问',
            'PAGE_VISIBILITY_REQUIRES_ACCESS_HELP' => '如果仅当用户可以访问它们时，才在菜单中显示页面，则设置为“是”。',
            'PAGE_INHERIT_PERMISSIONS' => '继承权限',
            'PAGE_INHERIT_PERMISSIONS_HELP' => '从父页面继承ACL。',
            'PAGE_AUTHORS' => '页面作者',
            'PAGE_AUTHORS_HELP' => '对于定义在指定作者页面分组的页面，页面作者成员拥有所有者级别访问权限。',
            'PAGE_GROUPS' => '页面分组',
            'PAGE_GROUPS_HELP' => '页面组成员有特别访问此页面的权限。',
            'READ' => '读取',
            'PUBLISH' => '发布',
            'LIST' => '列表',
            'ACCESS_SITE' => '站点',
            'ACCESS_SITE_LOGIN' => '登录到站点',
            'ACCESS_ADMIN' => '管理',
            'ACCESS_ADMIN_LOGIN' => '登录到管理',
            'ACCESS_ADMIN_SUPER' => '超级用户',
            'ACCESS_ADMIN_CACHE' => '清除缓存',
            'ACCESS_ADMIN_CONFIGURATION' => '配置',
            'ACCESS_ADMIN_CONFIGURATION_SYSTEM' => '管理系统配置',
            'ACCESS_ADMIN_CONFIGURATION_SITE' => '管理站点配置',
            'ACCESS_ADMIN_CONFIGURATION_MEDIA' => '管理媒体配置',
            'ACCESS_ADMIN_CONFIGURATION_INFO' => '查看服务器信息',
            'ACCESS_ADMIN_SETTINGS' => '设置',
            'ACCESS_ADMIN_PAGES' => '管理页面',
            'ACCESS_ADMIN_MAINTENANCE' => '站点维护',
            'ACCESS_ADMIN_STATISTICS' => '站点统计',
            'ACCESS_ADMIN_PLUGINS' => '管理插件',
            'ACCESS_ADMIN_THEMES' => '管理主题',
            'ACCESS_ADMIN_TOOLS' => '访问工具',
            'ACCESS_ADMIN_USERS' => '管理用户',
            'USERS' => '用户',
            'ACL' => '访问控制',
            'FLEX_CACHING' => '弹性缓存',
            'FLEX_INDEX_CACHE_ENABLED' => '启用索引缓存',
            'FLEX_INDEX_CACHE_LIFETIME' => '索引缓存生命周期(秒)',
            'FLEX_OBJECT_CACHE_ENABLED' => '启用对象缓存',
            'FLEX_OBJECT_CACHE_LIFETIME' => '对象缓存生命周期(秒)',
            'FLEX_RENDER_CACHE_ENABLED' => '启用渲染缓存',
            'FLEX_RENDER_CACHE_LIFETIME' => '渲染缓存生命周期(秒)',
            'DEBUGGER_CENSORED' => '传感器敏感性数据',
            'DEBUGGER_CENSORED_HELP' => '仅对 Clockwork 提供者：如果启用，则显示可能敏感的信息 (POST 参数、cookies、files、配置以及日志消息中大多数数组/对象数据)',
            'LANGUAGE_TRANSLATIONS' => '翻译',
            'LANGUAGE_TRANSLATIONS_HELP' => '如果为 false，则使用翻译键而不是翻译字符串。 此功能可以用于修复翻译错误或找到硬编码的英文字符串。',
            'STRICT_BLUEPRINT_COMPAT' => '蓝图兼容性',
            'STRICT_BLUEPRINT_COMPAT_HELP' => '启用对蓝图的后向兼容的严格支持。 如果关闭，新行为会使表单验证失败，如果在蓝图中没有定义额外的数据。',
            'RESET' => '重置',
            'LOGOS' => '标志',
            'PRESETS' => '预设',
            'COLOR_SCHEME_LABEL' => '配色方案',
            'COLOR_SCHEME_HELP' => '从预定义组合列表中选择一个配色方案，或添加您自己的样式',
            'COLOR_SCHEME_NAME' => '自定义配色方案名称',
            'COLOR_SCHEME_NAME_HELP' => '给您的自定义主题命名以便导出和分享',
            'COLOR_SCHEME_NAME_PLACEHOLDER' => '蓝色之子',
            'PRIMARY_ACCENT_LABEL' => '主色调',
            'PRIMARY_ACCENT_HELP' => '选择主色调的配色方案应使用的颜色集。',
            'SECONDARY_ACCENT_LABEL' => '次要色调',
            'SECONDARY_ACCENT_HELP' => '选择次要色调的配色方案应使用的颜色集。',
            'TERTIARY_ACCENT_LABEL' => '第三色调',
            'TERTIARY_ACCENT_HELP' => '选择第三色调的配色方案应使用的颜色集。',
            'WEB_FONTS_LABEL' => 'Web 字体',
            'WEB_FONTS_HELP' => '使用自定义 Web 字体',
            'HEADER_FONT_LABEL' => '标题字体',
            'HEADER_FONT_HELP' => '用于标题、侧边导航和部分标题的字体',
            'BODY_FONT_LABEL' => '正文字体',
            'BODY_FONT_HELP' => '主题正文中使用的主字体',
            'CUSTOM_CSS_LABEL' => '自定义 CSS',
            'CUSTOM_CSS_PLACEHOLDER' => '将您自定义的 CSS 放到这里...',
            'CUSTOM_CSS_HELP' => '自定义将添加到每个管理页面的 CSS',
            'CUSTOM_FOOTER' => '自定义页脚',
            'CUSTOM_FOOTER_HELP' => '您可以在这里使用 HTML 和/或 Markdown 语法',
            'CUSTOM_FOOTER_PLACEHOLDER' => '输入 HTML/Markdown 覆盖默认页脚',
            'LOGIN_SCREEN_CUSTOM_LOGO_LABEL' => '登录自定义标志',
            'TOP_LEFT_CUSTOM_LOGO_LABEL' => '主要自定义标志',
            'LOAD_PRESET' => '加载预设',
            'RECOMPILE' => '重新编译',
            'EXPORT' => '导出',
            'QUICKTRAY_RECOMPILE' => '快速托盘重新编译图标',
            'QUICKTRAY_RECOMPILE_HELP' => '将重新编译预设的 SCSS，以使得任何更改或新插件生效',
            'CODEMIRROR' => 'CodeMirror 编辑器',
            'CODEMIRROR_THEME' => '编辑器主题',
            'CODEMIRROR_THEME_DESC' => '**注意:** 访问 [CodeMirror 主题演示](https://codemirror.net/demo/theme.html?target=_blank) 查看这些操作。**_Paper_** 是默认的Grav 主题。',
            'CODEMIRROR_FONTSIZE' => '编辑器字体大小',
            'CODEMIRROR_FONTSIZE_SM' => '小字体',
            'CODEMIRROR_FONTSIZE_MD' => '中等字体',
            'CODEMIRROR_FONTSIZE_LG' => '大字体',
            'CODEMIRROR_MD_FONT' => 'Markdown 编辑器字体',
            'CODEMIRROR_MD_FONT_SANS' => 'Sans Font',
            'CODEMIRROR_MD_FONT_MONO' => '单色/等宽字体',
            'CUSTOM_PRESETS' => '自定义预设',
            'CUSTOM_PRESETS_HELP' => '拖放主题 .yaml 文件到此处，或者您可以创建一个预设数组，以文本为基础的键值',
            'CUSTOM_PRESETS_PLACEHOLDER' => '将您的预设置放在这里',
            'GENERAL' => '普通',
            'CONTENT_EDITOR' => '内容编辑器',
            'CONTENT_EDITOR_HELP' => '编辑内容可以优先使用自定义编辑器',
            'BAD_FILENAME' => '文件名错误',
            'SHOW_SENSITIVE' => '显示敏感数据',
            'SHOW_SENSITIVE_HELP' => '仅对 Clockwork 提供者：显示可能敏感的信息 (POST 参数、cookies、files、配置以及日志消息中大多数数组/对象数据)',
            'VALID_LINK_ATTRIBUTES' => '有效链接属性',
            'VALID_LINK_ATTRIBUTES_HELP' => '将自动添加到媒体 HTML 元素的属性',
            'CONFIGURATION' => '设置',
            'CUSTOMIZATION' => '自定义',
            'EXTRAS' => '附加',
            'BASICS' => '基本',
            'ADMIN_CACHING' => '启用缓存管理',
            'ADMIN_CACHING_HELP' => '管理面板中的缓存可以独立于前端站点进行控制',
            'ADMIN_PATH' => '管理员路径',
            'ADMIN_PATH_PLACEHOLDER' => '管理员默认路由 (相对于基础)',
            'ADMIN_PATH_HELP' => '如果你想要更改管理员的URL，你可以在此提供一个路径',
            'LOGO_TEXT' => '标志的文本',
            'LOGO_TEXT_HELP' => '取代默认 Grav 标志的文本',
            'CONTENT_PADDING' => '内容的内边距',
            'CONTENT_PADDING_HELP' => '启用/禁用在内容区域周围的内边距，以提供更多的空间',
            'BODY_CLASSES' => 'Body 的 classes',
            'BODY_CLASSES_HELP' => '添加自定义 body classes ，以空格分隔',
            'SIDEBAR_ACTIVATION' => '侧边栏激活',
            'SIDEBAR_ACTIVATION_HELP' => '控制侧边栏如何激活',
            'SIDEBAR_HOVER_DELAY' => '悬停延迟',
            'SIDEBAR_HOVER_DELAY_APPEND' => '毫秒',
            'SIDEBAR_ACTIVATION_TAB' => '标签页',
            'SIDEBAR_ACTIVATION_HOVER' => '悬停',
            'SIDEBAR_SIZE' => '侧边栏大小',
            'SIDEBAR_SIZE_HELP' => '控制侧边栏宽度',
            'SIDEBAR_SIZE_AUTO' => '自动宽度',
            'SIDEBAR_SIZE_SMALL' => '小宽度',
            'EDIT_MODE' => '编辑模式',
            'EDIT_MODE_HELP' => '自动使用蓝图，如果没有找到此蓝图，将使用“专家”模式。',
            'FRONTEND_PREVIEW_TARGET' => '预览页面目标',
            'FRONTEND_PREVIEW_TARGET_INLINE' => '内嵌在管理界面',
            'FRONTEND_PREVIEW_TARGET_NEW' => '新标签页',
            'FRONTEND_PREVIEW_TARGET_CURRENT' => '当前标签页',
            'PARENT_DROPDOWN' => '上级下拉列表',
            'PARENT_DROPDOWN_BOTH' => '显示名称和文件夹',
            'PARENT_DROPDOWN_FOLDER' => '显示文件夹',
            'PARENT_DROPDOWN_FULLPATH' => '显示完整路径',
            'PARENTS_LEVELS' => '上级级别',
            'PARENTS_LEVELS_HELP' => '上级选择列表中显示的级别数量',
            'MODULAR_PARENTS' => '模块化的父级',
            'MODULAR_PARENTS_HELP' => '在父选择列表中显示模块化页面',
            'SHOW_GITHUB_LINK' => '显示 GitHub 链接',
            'SHOW_GITHUB_LINK_HELP' => '显示“发现问题了吗？请在 GitHub 上报告”的消息。',
            'PAGES_LIST_DISPLAY_FIELD' => '页面列表显示字段',
            'PAGES_LIST_DISPLAY_FIELD_HELP' => '如果存在，在页面列表中使用该字段。默认为标题。',
            'AUTO_UPDATES' => '自动检查更新',
            'AUTO_UPDATES_HELP' => '当更新可用时，在管理面板中显示一个信息消息。',
            'TIMEOUT' => '超时',
            'TIMEOUT_HELP' => '设置 Session 超时，以秒为单位',
            'HIDE_PAGE_TYPES' => '在管理界面中隐藏页面类型',
            'HIDE_MODULAR_PAGE_TYPES' => '在管理界面中隐藏模块化页面类型',
            'DASHBOARD' => '仪表板',
            'WIDGETS_DISPLAY' => '小部件显示状态',
            'NOTIFICATIONS' => '通知',
            'FEED_NOTIFICATIONS' => '新闻源通知',
            'FEED_NOTIFICATIONS_HELP' => '显示基于订阅源的通知',
            'DASHBOARD_NOTIFICATIONS' => '仪表板通知',
            'DASHBOARD_NOTIFICATIONS_HELP' => '显示基于仪表板的通知',
            'PLUGINS_NOTIFICATIONS' => '插件通知',
            'PLUGINS_NOTIFICATIONS_HELP' => '显示针对插件的通知',
            'THEMES_NOTIFICATIONS' => '主题通知',
            'THEMES_NOTIFICATIONS_HELP' => '显示针对主题的通知',
            'LOGO_BG_HELP' => '标志背景',
            'LOGO_LINK_HELP' => '标志的链接',
            'NAV_BG_HELP' => '导航栏背景',
            'NAV_TEXT_HELP' => '导航文本',
            'NAV_LINK_HELP' => '导航链接',
            'NAV_SELECTED_BG_HELP' => '导航选中时背景',
            'NAV_SELECTED_LINK_HELP' => '导航选中的链接',
            'NAV_HOVER_BG_HELP' => '导航鼠标悬停背景',
            'NAV_HOVER_LINK_HELP' => '导航鼠标悬停链接',
            'TOOLBAR_BG_HELP' => '工具栏背景',
            'TOOLBAR_TEXT_HELP' => '工具栏文本',
            'PAGE_BG_HELP' => '页面背景',
            'PAGE_TEXT_HELP' => '页面文本',
            'PAGE_LINK_HELP' => '页面链接',
            'CONTENT_BG_HELP' => '内容背景',
            'CONTENT_TEXT_HELP' => '内容文本',
            'CONTENT_LINK_HELP' => '内容链接',
            'CONTENT_LINK2_HELP' => '内容链接 2',
            'CONTENT_HEADER_HELP' => '内容标题',
            'CONTENT_TABS_BG_HELP' => '内容标签页背景',
            'CONTENT_TABS_TEXT_HELP' => '内容标签页文本',
            'CONTENT_HIGHLIGHT_HELP' => '内容高亮',
            'BUTTON_BG_HELP' => '按钮背景',
            'BUTTON_TEXT_HELP' => '按钮文本',
            'NOTICE_BG_HELP' => '通知背景',
            'NOTICE_TEXT_HELP' => '通知文本',
            'UPDATES_BG_HELP' => '更新背景',
            'UPDATES_TEXT_HELP' => '更新文本',
            'CRITICAL_BG_HELP' => '危急背景',
            'CRITICAL_TEXT_HELP' => '危急文本',
            'BUTTON_COLORS' => '按钮颜色',
            'CONTENT_COLORS' => '内容颜色',
            'TABS_COLORS' => '标签页颜色',
            'CRITICAL_COLORS' => '危急颜色',
            'LOGO_COLORS' => '标志颜色',
            'NAV_COLORS' => '导航栏颜色',
            'NOTICE_COLORS' => '通知颜色',
            'PAGE_COLORS' => '页面颜色',
            'TOOLBAR_COLORS' => '工具栏颜色',
            'UPDATE_COLORS' => '更新颜色',
            'POPULARITY' => '受欢迎度',
            'VISITOR_TRACKING' => '访客跟踪',
            'VISITOR_TRACKING_HELP' => '启用访客统计数据收集功能',
            'DAYS_OF_STATS' => '统计天数',
            'DAYS_OF_STATS_HELP' => '保留特定天数的统计资料，然后丢弃。',
            'IGNORE_URLS' => '忽略',
            'IGNORE_URLS_HELP' => '要忽略的 URL',
            'DAILY_HISTORY' => '每日历史',
            'MONTHLY_HISTORY' => '每月历史',
            'VISITORS_HISTORY' => '访客历史',
            'MEDIA_RESIZE' => '页面媒体图像调整器',
            'PAGEMEDIA_RESIZER' => '> 以下设置适用于通过页面媒体上传的图像。如果超过输入中设置的限制, 调整宽度/高度将自动缩小大小, 并按比例调整图像的大小。分辨率最小值和最大值定义上传图像的大小范围。将字段设置为 0, 以防止任何操作。',
            'RESIZE_WIDTH' => '调整宽度',
            'RESIZE_WIDTH_HELP' => '将过宽的图像缩放到设定的值',
            'RESIZE_HEIGHT' => '调整高度',
            'RESIZE_HEIGHT_HELP' => '将过高的图像缩放到设定的值',
            'RES_MIN_WIDTH' => '分辨率最小宽度',
            'RES_MIN_WIDTH_HELP' => '允许添加图像的最小宽度',
            'RES_MIN_HEIGHT' => '分辨率最小高度',
            'RES_MIN_HEIGHT_HELP' => '允许添加图像的最小高度',
            'RES_MAX_WIDTH' => '分辨率最大宽度',
            'RES_MAX_WIDTH_HELP' => '允许添加图像的最大宽度',
            'RES_MAX_HEIGHT' => '分辨率最大高度',
            'RES_MAX_HEIGHT_HELP' => '允许添加图像的最大高度',
            'RESIZE_QUALITY' => '调整质量',
            'RESIZE_QUALITY_HELP' => '调整图像大小时使用的质量。取值介于 0 到 1 之间。',
            'PIXELS' => '像素',
            'ACCESS_ADMIN_CONFIGURATION_SECURITY' => '管理安全配置',
            'SESSION_DOMAIN' => 'Session 域',
            'SESSION_DOMAIN_HELP' => '仅在您重写站点域名时使用，例如在 Docker 容器中。',
            'SESSION_PATH' => 'Session 路径',
            'SESSION_PATH_HELP' => '仅在您重写站点路径时使用，例如在 Docker 容器中。',
            'REDIRECT_OPTION_NO_REDIRECT' => '没有重定向',
            'REDIRECT_OPTION_DEFAULT_REDIRECT' => '使用默认重定向代码',
            'REDIRECT_OPTION_301' => '301 - 永久移动',
            'REDIRECT_OPTION_302' => '302 - 临时移动',
            'REDIRECT_OPTION_303' => '303 - 查看其他',
            'IMAGES_CLS_TITLE' => '累积布局偏移 (CLS)',
            'IMAGES_CLS_AUTO_SIZES' => '启用自动尺寸',
            'IMAGES_CLS_AUTO_SIZES_HELP' => '自动将 \'宽度\' 和 \'高度\' 属性添加到图片以应对 CLS',
            'IMAGES_CLS_ASPECT_RATIO' => '启用宽高比',
            'IMAGES_CLS_ASPECT_RATIO_HELP' => '通过 \'style\' 属性应用的可选的 CSS 变量，可以在 CSS 中用于自定义样式',
            'IMAGES_CLS_RETINA_SCALE' => 'Retina 缩放倍数',
            'IMAGES_CLS_RETINA_SCALE_HELP' => '将计算出的尺寸除以缩放率，以较小像素大小显示较高分辨率图像，从而更好地处理 HiDPI 分辨率',
            'AUTOREGENERATE_FOLDER_SLUG' => '基于页面标题自动生成',
            'ENABLE' => '启用',
            'PLUGINS_MUST_BE_ENABLED' => '必须启用插件才能配置',
            'ACTIVATION_REQUIRED' => '配置需要先激活',
            'SESSION_SECURE_HTTPS' => '安全 (HTTPS)',
            'SESSION_SECURE_HTTPS_HELP' => '在 HTTPS 上设置会话安全，但不是 HTTP。如果您在安全设置上设置为 true，将不会产生任何效果。 如果您的网站在 HTTP 和 HTTP 之间跳转，设置为 false。',
            'AVATAR' => '头像生成器',
            'AVATAR_HELP' => 'Multiavatar 是本地生成的头像。Gravatar 是一个外部服务，它使用您的电子邮件地址来远程拉取一个预配置的头像。',
            'AVATAR_HASH' => '注意：可选头像自定义“哈希”字符串',
            'IMAGES_TITLE' => '图像',
            'LEGACY_MEDIA_MUTATION' => '旧媒体操纵兼容性',
            'LEGACY_MEDIA_MUTATION_HELP' => '只有当图像操作在Grav 更新后中断时才启用此设置。',
            'BACKWARD_COMPATIBILITY' => '向后兼容'
        ]
    ]
];
