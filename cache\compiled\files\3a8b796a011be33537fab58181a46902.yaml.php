<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/system/config/media.yaml',
    'modified' => 1715789844,
    'size' => 4436,
    'data' => [
        'types' => [
            'defaults' => [
                'type' => 'file',
                'thumb' => 'media/thumb.png',
                'mime' => 'application/octet-stream',
                'image' => [
                    'filters' => [
                        'default' => [
                            0 => 'enableProgressive'
                        ]
                    ]
                ]
            ],
            'jpg' => [
                'type' => 'image',
                'thumb' => 'media/thumb-jpg.png',
                'mime' => 'image/jpeg'
            ],
            'jpe' => [
                'type' => 'image',
                'thumb' => 'media/thumb-jpg.png',
                'mime' => 'image/jpeg'
            ],
            'jpeg' => [
                'type' => 'image',
                'thumb' => 'media/thumb-jpg.png',
                'mime' => 'image/jpeg'
            ],
            'png' => [
                'type' => 'image',
                'thumb' => 'media/thumb-png.png',
                'mime' => 'image/png'
            ],
            'webp' => [
                'type' => 'image',
                'thumb' => 'media/thumb-webp.png',
                'mime' => 'image/webp'
            ],
            'avif' => [
                'type' => 'image',
                'thumb' => 'media/thumb.png',
                'mime' => 'image/avif'
            ],
            'gif' => [
                'type' => 'animated',
                'thumb' => 'media/thumb-gif.png',
                'mime' => 'image/gif'
            ],
            'svg' => [
                'type' => 'vector',
                'thumb' => 'media/thumb-svg.png',
                'mime' => 'image/svg+xml'
            ],
            'mp4' => [
                'type' => 'video',
                'thumb' => 'media/thumb-mp4.png',
                'mime' => 'video/mp4'
            ],
            'mov' => [
                'type' => 'video',
                'thumb' => 'media/thumb-mov.png',
                'mime' => 'video/quicktime'
            ],
            'm4v' => [
                'type' => 'video',
                'thumb' => 'media/thumb-m4v.png',
                'mime' => 'video/x-m4v'
            ],
            'swf' => [
                'type' => 'video',
                'thumb' => 'media/thumb-swf.png',
                'mime' => 'video/x-flv'
            ],
            'flv' => [
                'type' => 'video',
                'thumb' => 'media/thumb-flv.png',
                'mime' => 'video/x-flv'
            ],
            'webm' => [
                'type' => 'video',
                'thumb' => 'media/thumb-webm.png',
                'mime' => 'video/webm'
            ],
            'ogv' => [
                'type' => 'video',
                'thumb' => 'media/thumb-ogg.png',
                'mime' => 'video/ogg'
            ],
            'mp3' => [
                'type' => 'audio',
                'thumb' => 'media/thumb-mp3.png',
                'mime' => 'audio/mp3'
            ],
            'ogg' => [
                'type' => 'audio',
                'thumb' => 'media/thumb-ogg.png',
                'mime' => 'audio/ogg'
            ],
            'wma' => [
                'type' => 'audio',
                'thumb' => 'media/thumb-wma.png',
                'mime' => 'audio/wma'
            ],
            'm4a' => [
                'type' => 'audio',
                'thumb' => 'media/thumb-m4a.png',
                'mime' => 'audio/m4a'
            ],
            'wav' => [
                'type' => 'audio',
                'thumb' => 'media/thumb-wav.png',
                'mime' => 'audio/wav'
            ],
            'aiff' => [
                'type' => 'audio',
                'thumb' => 'media/thumb-aif.png',
                'mime' => 'audio/aiff'
            ],
            'aif' => [
                'type' => 'audio',
                'thumb' => 'media/thumb-aif.png',
                'mime' => 'audio/aiff'
            ],
            'txt' => [
                'type' => 'file',
                'thumb' => 'media/thumb-txt.png',
                'mime' => 'text/plain'
            ],
            'xml' => [
                'type' => 'file',
                'thumb' => 'media/thumb-xml.png',
                'mime' => 'application/xml'
            ],
            'doc' => [
                'type' => 'file',
                'thumb' => 'media/thumb-doc.png',
                'mime' => 'application/msword'
            ],
            'docx' => [
                'type' => 'file',
                'thumb' => 'media/thumb-docx.png',
                'mime' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ],
            'xls' => [
                'type' => 'file',
                'thumb' => 'media/thumb-xls.png',
                'mime' => 'application/vnd.ms-excel'
            ],
            'xlsx' => [
                'type' => 'file',
                'thumb' => 'media/thumb-xlsx.png',
                'mime' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            ],
            'ppt' => [
                'type' => 'file',
                'thumb' => 'media/thumb-ppt.png',
                'mime' => 'application/vnd.ms-powerpoint'
            ],
            'pptx' => [
                'type' => 'file',
                'thumb' => 'media/thumb-pptx.png',
                'mime' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            ],
            'pps' => [
                'type' => 'file',
                'thumb' => 'media/thumb-pps.png',
                'mime' => 'application/vnd.ms-powerpoint'
            ],
            'rtf' => [
                'type' => 'file',
                'thumb' => 'media/thumb-rtf.png',
                'mime' => 'application/rtf'
            ],
            'bmp' => [
                'type' => 'file',
                'thumb' => 'media/thumb-bmp.png',
                'mime' => 'image/bmp'
            ],
            'tiff' => [
                'type' => 'file',
                'thumb' => 'media/thumb-tiff.png',
                'mime' => 'image/tiff'
            ],
            'mpeg' => [
                'type' => 'file',
                'thumb' => 'media/thumb-mpg.png',
                'mime' => 'video/mpeg'
            ],
            'mpg' => [
                'type' => 'file',
                'thumb' => 'media/thumb-mpg.png',
                'mime' => 'video/mpeg'
            ],
            'mpe' => [
                'type' => 'file',
                'thumb' => 'media/thumb-mpe.png',
                'mime' => 'video/mpeg'
            ],
            'avi' => [
                'type' => 'file',
                'thumb' => 'media/thumb-avi.png',
                'mime' => 'video/msvideo'
            ],
            'wmv' => [
                'type' => 'file',
                'thumb' => 'media/thumb-wmv.png',
                'mime' => 'video/x-ms-wmv'
            ],
            'html' => [
                'type' => 'file',
                'thumb' => 'media/thumb-html.png',
                'mime' => 'text/html'
            ],
            'htm' => [
                'type' => 'file',
                'thumb' => 'media/thumb-html.png',
                'mime' => 'text/html'
            ],
            'ics' => [
                'type' => 'iCal',
                'thumb' => 'media/thumb-ics.png',
                'mime' => 'text/calendar'
            ],
            'pdf' => [
                'type' => 'file',
                'thumb' => 'media/thumb-pdf.png',
                'mime' => 'application/pdf'
            ],
            'ai' => [
                'type' => 'file',
                'thumb' => 'media/thumb-ai.png',
                'mime' => 'image/ai'
            ],
            'psd' => [
                'type' => 'file',
                'thumb' => 'media/thumb-psd.png',
                'mime' => 'image/psd'
            ],
            'zip' => [
                'type' => 'file',
                'thumb' => 'media/thumb-zip.png',
                'mime' => 'application/zip'
            ],
            '7z' => [
                'type' => 'file',
                'thumb' => 'media/thumb-7z.png',
                'mime' => 'application/x-7z-compressed'
            ],
            'gz' => [
                'type' => 'file',
                'thumb' => 'media/thumb-gz.png',
                'mime' => 'application/x-gzip'
            ],
            'tar' => [
                'type' => 'file',
                'thumb' => 'media/thumb-tar.png',
                'mime' => 'application/x-tar'
            ],
            'css' => [
                'type' => 'file',
                'thumb' => 'media/thumb-css.png',
                'mime' => 'text/css'
            ],
            'js' => [
                'type' => 'file',
                'thumb' => 'media/thumb-js.png',
                'mime' => 'text/javascript'
            ],
            'json' => [
                'type' => 'file',
                'thumb' => 'media/thumb-json.png',
                'mime' => 'application/json'
            ],
            'vcf' => [
                'type' => 'file',
                'thumb' => 'media/thumb-vcf.png',
                'mime' => 'text/x-vcard'
            ]
        ]
    ]
];
