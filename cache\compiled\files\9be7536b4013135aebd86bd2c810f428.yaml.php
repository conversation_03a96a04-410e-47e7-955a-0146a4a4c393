<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/twilio-sms/blueprints.yaml',
    'modified' => 1747167222,
    'size' => 2024,
    'data' => [
        'name' => 'Twilio SMS',
        'version' => '1.0.0',
        'description' => 'Send SMS notifications via Twilio when emergency service requests are submitted',
        'icon' => 'bell',
        'author' => [
            'name' => '<PERSON><PERSON><PERSON>',
            'email' => '<EMAIL>'
        ],
        'homepage' => 'https://drain5etoiles.com',
        'keywords' => 'grav, plugin, sms, twilio, notifications',
        'bugs' => 'https://github.com/getgrav/grav-plugin-twilio-sms/issues',
        'license' => 'MIT',
        'dependencies' => [
            0 => [
                'name' => 'grav',
                'version' => '>=1.6.0'
            ],
            1 => [
                'name' => 'form',
                'version' => '>=4.0.0'
            ]
        ],
        'form' => [
            'validation' => 'loose',
            'fields' => [
                'enabled' => [
                    'type' => 'toggle',
                    'label' => 'Plugin Status',
                    'highlight' => 1,
                    'default' => 0,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ]
                ],
                'account_sid' => [
                    'type' => 'text',
                    'label' => 'Twilio Account SID',
                    'help' => 'Your Twilio Account SID from the Twilio dashboard',
                    'placeholder' => 'ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                ],
                'auth_token' => [
                    'type' => 'text',
                    'label' => 'Twilio Auth Token',
                    'help' => 'Your Twilio Auth Token from the Twilio dashboard',
                    'placeholder' => 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
                ],
                'twilio_number' => [
                    'type' => 'text',
                    'label' => 'Twilio Phone Number',
                    'help' => 'Your Twilio phone number in E.164 format (e.g., +***********)',
                    'placeholder' => '+***********'
                ],
                'recipient_numbers' => [
                    'type' => 'list',
                    'label' => 'Recipient Phone Numbers',
                    'help' => 'List of phone numbers to receive emergency notifications (in E.164 format)',
                    'placeholder_key' => 'Phone Number',
                    'placeholder_value' => '+***********'
                ],
                'emergency_message_template' => [
                    'type' => 'textarea',
                    'label' => 'Emergency Message Template',
                    'help' => 'Template for emergency notification messages (use variables like {{name}}, {{phone}}, etc.)',
                    'placeholder' => 'EMERGENCY SERVICE REQUEST

Customer: {{name}}
Phone: {{phone}}
Address: {{address}}
Message: {{message}}

Please contact the customer as soon as possible.',
                    'default' => 'EMERGENCY SERVICE REQUEST

Customer: {{name}}
Phone: {{phone}}
Address: {{address}}
Message: {{message}}

Please contact the customer as soon as possible.'
                ]
            ]
        ]
    ]
];
