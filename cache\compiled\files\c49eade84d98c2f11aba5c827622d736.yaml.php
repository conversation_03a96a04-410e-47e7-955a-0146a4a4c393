<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/admin/themes/grav/templates/forms/fields/iconpicker/icons.yaml',
    'modified' => **********,
    'size' => 121378,
    'data' => [
        'icons' => [
            0 => [
                'name' => 'Glass',
                'id' => 'glass',
                'unicode' => 'f000',
                'created' => 1.0,
                'filter' => [
                    0 => 'martini',
                    1 => 'drink',
                    2 => 'bar',
                    3 => 'alcohol',
                    4 => 'liquor'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            1 => [
                'name' => 'Music',
                'id' => 'music',
                'unicode' => 'f001',
                'created' => 1.0,
                'filter' => [
                    0 => 'note',
                    1 => 'sound'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            2 => [
                'name' => 'Search',
                'id' => 'search',
                'unicode' => 'f002',
                'created' => 1.0,
                'filter' => [
                    0 => 'magnify',
                    1 => 'zoom',
                    2 => 'enlarge',
                    3 => 'bigger'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            3 => [
                'name' => 'Envelope Outlined',
                'id' => 'envelope-o',
                'unicode' => 'f003',
                'created' => 1.0,
                'filter' => [
                    0 => 'email',
                    1 => 'e-mail',
                    2 => 'letter',
                    3 => 'support',
                    4 => 'mail',
                    5 => 'message',
                    6 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            4 => [
                'name' => 'Heart',
                'id' => 'heart',
                'unicode' => 'f004',
                'created' => 1.0,
                'filter' => [
                    0 => 'love',
                    1 => 'like',
                    2 => 'favorite'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Medical Icons'
                ]
            ],
            5 => [
                'name' => 'Star',
                'id' => 'star',
                'unicode' => 'f005',
                'created' => 1.0,
                'filter' => [
                    0 => 'award',
                    1 => 'achievement',
                    2 => 'night',
                    3 => 'rating',
                    4 => 'score',
                    5 => 'favorite'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            6 => [
                'name' => 'Star Outlined',
                'id' => 'star-o',
                'unicode' => 'f006',
                'created' => 1.0,
                'filter' => [
                    0 => 'award',
                    1 => 'achievement',
                    2 => 'night',
                    3 => 'rating',
                    4 => 'score',
                    5 => 'favorite'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            7 => [
                'name' => 'User',
                'id' => 'user',
                'unicode' => 'f007',
                'created' => 1.0,
                'filter' => [
                    0 => 'person',
                    1 => 'man',
                    2 => 'head',
                    3 => 'profile'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            8 => [
                'name' => 'Film',
                'id' => 'film',
                'unicode' => 'f008',
                'created' => 1.0,
                'filter' => [
                    0 => 'movie'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            9 => [
                'name' => 'th-large',
                'id' => 'th-large',
                'unicode' => 'f009',
                'created' => 1.0,
                'filter' => [
                    0 => 'blocks',
                    1 => 'squares',
                    2 => 'boxes',
                    3 => 'grid'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            10 => [
                'name' => 'th',
                'id' => 'th',
                'unicode' => 'f00a',
                'created' => 1.0,
                'filter' => [
                    0 => 'blocks',
                    1 => 'squares',
                    2 => 'boxes',
                    3 => 'grid'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            11 => [
                'name' => 'th-list',
                'id' => 'th-list',
                'unicode' => 'f00b',
                'created' => 1.0,
                'filter' => [
                    0 => 'ul',
                    1 => 'ol',
                    2 => 'checklist',
                    3 => 'finished',
                    4 => 'completed',
                    5 => 'done',
                    6 => 'todo'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            12 => [
                'name' => 'Check',
                'id' => 'check',
                'unicode' => 'f00c',
                'created' => 1.0,
                'filter' => [
                    0 => 'checkmark',
                    1 => 'done',
                    2 => 'todo',
                    3 => 'agree',
                    4 => 'accept',
                    5 => 'confirm',
                    6 => 'tick',
                    7 => 'ok'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            13 => [
                'name' => 'Times',
                'id' => 'times',
                'unicode' => 'f00d',
                'created' => 1.0,
                'aliases' => [
                    0 => 'remove',
                    1 => 'close'
                ],
                'filter' => [
                    0 => 'close',
                    1 => 'exit',
                    2 => 'x',
                    3 => 'cross'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            14 => [
                'name' => 'Search Plus',
                'id' => 'search-plus',
                'unicode' => 'f00e',
                'created' => 1.0,
                'filter' => [
                    0 => 'magnify',
                    1 => 'zoom',
                    2 => 'enlarge',
                    3 => 'bigger'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            15 => [
                'name' => 'Search Minus',
                'id' => 'search-minus',
                'unicode' => 'f010',
                'created' => 1.0,
                'filter' => [
                    0 => 'magnify',
                    1 => 'minify',
                    2 => 'zoom',
                    3 => 'smaller'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            16 => [
                'name' => 'Power Off',
                'id' => 'power-off',
                'unicode' => 'f011',
                'created' => 1.0,
                'filter' => [
                    0 => 'on'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            17 => [
                'name' => 'signal',
                'id' => 'signal',
                'unicode' => 'f012',
                'created' => 1.0,
                'filter' => [
                    0 => 'graph',
                    1 => 'bars'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            18 => [
                'name' => 'cog',
                'id' => 'cog',
                'unicode' => 'f013',
                'created' => 1.0,
                'filter' => [
                    0 => 'settings'
                ],
                'aliases' => [
                    0 => 'gear'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Spinner Icons'
                ]
            ],
            19 => [
                'name' => 'Trash Outlined',
                'id' => 'trash-o',
                'unicode' => 'f014',
                'created' => 1.0,
                'filter' => [
                    0 => 'garbage',
                    1 => 'delete',
                    2 => 'remove',
                    3 => 'trash',
                    4 => 'hide'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            20 => [
                'name' => 'home',
                'id' => 'home',
                'unicode' => 'f015',
                'created' => 1.0,
                'filter' => [
                    0 => 'main',
                    1 => 'house'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            21 => [
                'name' => 'File Outlined',
                'id' => 'file-o',
                'unicode' => 'f016',
                'created' => 1.0,
                'filter' => [
                    0 => 'new',
                    1 => 'page',
                    2 => 'pdf',
                    3 => 'document'
                ],
                'categories' => [
                    0 => 'Text Editor Icons',
                    1 => 'File Type Icons'
                ]
            ],
            22 => [
                'name' => 'Clock Outlined',
                'id' => 'clock-o',
                'unicode' => 'f017',
                'created' => 1.0,
                'filter' => [
                    0 => 'watch',
                    1 => 'timer',
                    2 => 'late',
                    3 => 'timestamp'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            23 => [
                'name' => 'road',
                'id' => 'road',
                'unicode' => 'f018',
                'created' => 1.0,
                'filter' => [
                    0 => 'street'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            24 => [
                'name' => 'Download',
                'id' => 'download',
                'unicode' => 'f019',
                'created' => 1.0,
                'filter' => [
                    0 => 'import'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            25 => [
                'name' => 'Arrow Circle Outlined Down',
                'id' => 'arrow-circle-o-down',
                'unicode' => 'f01a',
                'created' => 1.0,
                'filter' => [
                    0 => 'download'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            26 => [
                'name' => 'Arrow Circle Outlined Up',
                'id' => 'arrow-circle-o-up',
                'unicode' => 'f01b',
                'created' => 1.0,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            27 => [
                'name' => 'inbox',
                'id' => 'inbox',
                'unicode' => 'f01c',
                'created' => 1.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            28 => [
                'name' => 'Play Circle Outlined',
                'id' => 'play-circle-o',
                'unicode' => 'f01d',
                'created' => 1.0,
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            29 => [
                'name' => 'Repeat',
                'id' => 'repeat',
                'unicode' => 'f01e',
                'created' => 1.0,
                'filter' => [
                    0 => 'redo',
                    1 => 'forward'
                ],
                'aliases' => [
                    0 => 'rotate-right'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            30 => [
                'name' => 'refresh',
                'id' => 'refresh',
                'unicode' => 'f021',
                'created' => 1.0,
                'filter' => [
                    0 => 'reload',
                    1 => 'sync'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Spinner Icons'
                ]
            ],
            31 => [
                'name' => 'list-alt',
                'id' => 'list-alt',
                'unicode' => 'f022',
                'created' => 1.0,
                'filter' => [
                    0 => 'ul',
                    1 => 'ol',
                    2 => 'checklist',
                    3 => 'finished',
                    4 => 'completed',
                    5 => 'done',
                    6 => 'todo'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            32 => [
                'name' => 'lock',
                'id' => 'lock',
                'unicode' => 'f023',
                'created' => 1.0,
                'filter' => [
                    0 => 'protect',
                    1 => 'admin',
                    2 => 'security'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            33 => [
                'name' => 'flag',
                'id' => 'flag',
                'unicode' => 'f024',
                'created' => 1.0,
                'filter' => [
                    0 => 'report',
                    1 => 'notification',
                    2 => 'notify'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            34 => [
                'name' => 'headphones',
                'id' => 'headphones',
                'unicode' => 'f025',
                'created' => 1.0,
                'filter' => [
                    0 => 'sound',
                    1 => 'listen',
                    2 => 'music',
                    3 => 'audio'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            35 => [
                'name' => 'volume-off',
                'id' => 'volume-off',
                'unicode' => 'f026',
                'created' => 1.0,
                'filter' => [
                    0 => 'audio',
                    1 => 'mute',
                    2 => 'sound',
                    3 => 'music'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            36 => [
                'name' => 'volume-down',
                'id' => 'volume-down',
                'unicode' => 'f027',
                'created' => 1.0,
                'filter' => [
                    0 => 'audio',
                    1 => 'lower',
                    2 => 'quieter',
                    3 => 'sound',
                    4 => 'music'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            37 => [
                'name' => 'volume-up',
                'id' => 'volume-up',
                'unicode' => 'f028',
                'created' => 1.0,
                'filter' => [
                    0 => 'audio',
                    1 => 'higher',
                    2 => 'louder',
                    3 => 'sound',
                    4 => 'music'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            38 => [
                'name' => 'qrcode',
                'id' => 'qrcode',
                'unicode' => 'f029',
                'created' => 1.0,
                'filter' => [
                    0 => 'scan'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            39 => [
                'name' => 'barcode',
                'id' => 'barcode',
                'unicode' => 'f02a',
                'created' => 1.0,
                'filter' => [
                    0 => 'scan'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            40 => [
                'name' => 'tag',
                'id' => 'tag',
                'unicode' => 'f02b',
                'created' => 1.0,
                'filter' => [
                    0 => 'label'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            41 => [
                'name' => 'tags',
                'id' => 'tags',
                'unicode' => 'f02c',
                'created' => 1.0,
                'filter' => [
                    0 => 'labels'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            42 => [
                'name' => 'book',
                'id' => 'book',
                'unicode' => 'f02d',
                'created' => 1.0,
                'filter' => [
                    0 => 'read',
                    1 => 'documentation'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            43 => [
                'name' => 'bookmark',
                'id' => 'bookmark',
                'unicode' => 'f02e',
                'created' => 1.0,
                'filter' => [
                    0 => 'save'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            44 => [
                'name' => 'print',
                'id' => 'print',
                'unicode' => 'f02f',
                'created' => 1.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            45 => [
                'name' => 'camera',
                'id' => 'camera',
                'unicode' => 'f030',
                'created' => 1.0,
                'filter' => [
                    0 => 'photo',
                    1 => 'picture',
                    2 => 'record'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            46 => [
                'name' => 'font',
                'id' => 'font',
                'unicode' => 'f031',
                'created' => 1.0,
                'filter' => [
                    0 => 'text'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            47 => [
                'name' => 'bold',
                'id' => 'bold',
                'unicode' => 'f032',
                'created' => 1.0,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            48 => [
                'name' => 'italic',
                'id' => 'italic',
                'unicode' => 'f033',
                'created' => 1.0,
                'filter' => [
                    0 => 'italics'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            49 => [
                'name' => 'text-height',
                'id' => 'text-height',
                'unicode' => 'f034',
                'created' => 1.0,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            50 => [
                'name' => 'text-width',
                'id' => 'text-width',
                'unicode' => 'f035',
                'created' => 1.0,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            51 => [
                'name' => 'align-left',
                'id' => 'align-left',
                'unicode' => 'f036',
                'created' => 1.0,
                'filter' => [
                    0 => 'text'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            52 => [
                'name' => 'align-center',
                'id' => 'align-center',
                'unicode' => 'f037',
                'created' => 1.0,
                'filter' => [
                    0 => 'middle',
                    1 => 'text'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            53 => [
                'name' => 'align-right',
                'id' => 'align-right',
                'unicode' => 'f038',
                'created' => 1.0,
                'filter' => [
                    0 => 'text'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            54 => [
                'name' => 'align-justify',
                'id' => 'align-justify',
                'unicode' => 'f039',
                'created' => 1.0,
                'filter' => [
                    0 => 'text'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            55 => [
                'name' => 'list',
                'id' => 'list',
                'unicode' => 'f03a',
                'created' => 1.0,
                'filter' => [
                    0 => 'ul',
                    1 => 'ol',
                    2 => 'checklist',
                    3 => 'finished',
                    4 => 'completed',
                    5 => 'done',
                    6 => 'todo'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            56 => [
                'name' => 'Outdent',
                'id' => 'outdent',
                'unicode' => 'f03b',
                'created' => 1.0,
                'aliases' => [
                    0 => 'dedent'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            57 => [
                'name' => 'Indent',
                'id' => 'indent',
                'unicode' => 'f03c',
                'created' => 1.0,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            58 => [
                'name' => 'Video Camera',
                'id' => 'video-camera',
                'unicode' => 'f03d',
                'created' => 1.0,
                'filter' => [
                    0 => 'film',
                    1 => 'movie',
                    2 => 'record'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            59 => [
                'name' => 'Picture Outlined',
                'id' => 'picture-o',
                'unicode' => 'f03e',
                'created' => 1.0,
                'aliases' => [
                    0 => 'photo',
                    1 => 'image'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            60 => [
                'name' => 'pencil',
                'id' => 'pencil',
                'unicode' => 'f040',
                'created' => 1.0,
                'filter' => [
                    0 => 'write',
                    1 => 'edit',
                    2 => 'update'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            61 => [
                'name' => 'map-marker',
                'id' => 'map-marker',
                'unicode' => 'f041',
                'created' => 1.0,
                'filter' => [
                    0 => 'map',
                    1 => 'pin',
                    2 => 'location',
                    3 => 'coordinates',
                    4 => 'localize',
                    5 => 'address',
                    6 => 'travel',
                    7 => 'where',
                    8 => 'place'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            62 => [
                'name' => 'adjust',
                'id' => 'adjust',
                'unicode' => 'f042',
                'created' => 1.0,
                'filter' => [
                    0 => 'contrast'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            63 => [
                'name' => 'tint',
                'id' => 'tint',
                'unicode' => 'f043',
                'created' => 1.0,
                'filter' => [
                    0 => 'raindrop',
                    1 => 'waterdrop',
                    2 => 'drop',
                    3 => 'droplet'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            64 => [
                'name' => 'Pencil Square Outlined',
                'id' => 'pencil-square-o',
                'unicode' => 'f044',
                'created' => 1.0,
                'filter' => [
                    0 => 'write',
                    1 => 'edit',
                    2 => 'update'
                ],
                'aliases' => [
                    0 => 'edit'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            65 => [
                'name' => 'Share Square Outlined',
                'id' => 'share-square-o',
                'unicode' => 'f045',
                'created' => 1.0,
                'filter' => [
                    0 => 'social',
                    1 => 'send',
                    2 => 'arrow'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            66 => [
                'name' => 'Check Square Outlined',
                'id' => 'check-square-o',
                'unicode' => 'f046',
                'created' => 1.0,
                'filter' => [
                    0 => 'todo',
                    1 => 'done',
                    2 => 'agree',
                    3 => 'accept',
                    4 => 'confirm',
                    5 => 'ok'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            67 => [
                'name' => 'Arrows',
                'id' => 'arrows',
                'unicode' => 'f047',
                'created' => 1.0,
                'filter' => [
                    0 => 'move',
                    1 => 'reorder',
                    2 => 'resize'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            68 => [
                'name' => 'step-backward',
                'id' => 'step-backward',
                'unicode' => 'f048',
                'created' => 1.0,
                'filter' => [
                    0 => 'rewind',
                    1 => 'previous',
                    2 => 'beginning',
                    3 => 'start',
                    4 => 'first'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            69 => [
                'name' => 'fast-backward',
                'id' => 'fast-backward',
                'unicode' => 'f049',
                'created' => 1.0,
                'filter' => [
                    0 => 'rewind',
                    1 => 'previous',
                    2 => 'beginning',
                    3 => 'start',
                    4 => 'first'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            70 => [
                'name' => 'backward',
                'id' => 'backward',
                'unicode' => 'f04a',
                'created' => 1.0,
                'filter' => [
                    0 => 'rewind',
                    1 => 'previous'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            71 => [
                'name' => 'play',
                'id' => 'play',
                'unicode' => 'f04b',
                'created' => 1.0,
                'filter' => [
                    0 => 'start',
                    1 => 'playing',
                    2 => 'music',
                    3 => 'sound'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            72 => [
                'name' => 'pause',
                'id' => 'pause',
                'unicode' => 'f04c',
                'created' => 1.0,
                'filter' => [
                    0 => 'wait'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            73 => [
                'name' => 'stop',
                'id' => 'stop',
                'unicode' => 'f04d',
                'created' => 1.0,
                'filter' => [
                    0 => 'block',
                    1 => 'box',
                    2 => 'square'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            74 => [
                'name' => 'forward',
                'id' => 'forward',
                'unicode' => 'f04e',
                'created' => 1.0,
                'filter' => [
                    0 => 'forward',
                    1 => 'next'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            75 => [
                'name' => 'fast-forward',
                'id' => 'fast-forward',
                'unicode' => 'f050',
                'created' => 1.0,
                'filter' => [
                    0 => 'next',
                    1 => 'end',
                    2 => 'last'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            76 => [
                'name' => 'step-forward',
                'id' => 'step-forward',
                'unicode' => 'f051',
                'created' => 1.0,
                'filter' => [
                    0 => 'next',
                    1 => 'end',
                    2 => 'last'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            77 => [
                'name' => 'eject',
                'id' => 'eject',
                'unicode' => 'f052',
                'created' => 1.0,
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            78 => [
                'name' => 'chevron-left',
                'id' => 'chevron-left',
                'unicode' => 'f053',
                'created' => 1.0,
                'filter' => [
                    0 => 'bracket',
                    1 => 'previous',
                    2 => 'back'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            79 => [
                'name' => 'chevron-right',
                'id' => 'chevron-right',
                'unicode' => 'f054',
                'created' => 1.0,
                'filter' => [
                    0 => 'bracket',
                    1 => 'next',
                    2 => 'forward'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            80 => [
                'name' => 'Plus Circle',
                'id' => 'plus-circle',
                'unicode' => 'f055',
                'created' => 1.0,
                'filter' => [
                    0 => 'add',
                    1 => 'new',
                    2 => 'create',
                    3 => 'expand'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            81 => [
                'name' => 'Minus Circle',
                'id' => 'minus-circle',
                'unicode' => 'f056',
                'created' => 1.0,
                'filter' => [
                    0 => 'delete',
                    1 => 'remove',
                    2 => 'trash',
                    3 => 'hide'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            82 => [
                'name' => 'Times Circle',
                'id' => 'times-circle',
                'unicode' => 'f057',
                'created' => 1.0,
                'filter' => [
                    0 => 'close',
                    1 => 'exit',
                    2 => 'x'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            83 => [
                'name' => 'Check Circle',
                'id' => 'check-circle',
                'unicode' => 'f058',
                'created' => 1.0,
                'filter' => [
                    0 => 'todo',
                    1 => 'done',
                    2 => 'agree',
                    3 => 'accept',
                    4 => 'confirm',
                    5 => 'ok'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            84 => [
                'name' => 'Question Circle',
                'id' => 'question-circle',
                'unicode' => 'f059',
                'filter' => [
                    0 => 'help',
                    1 => 'information',
                    2 => 'unknown',
                    3 => 'support'
                ],
                'created' => 1.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            85 => [
                'name' => 'Info Circle',
                'id' => 'info-circle',
                'unicode' => 'f05a',
                'created' => 1.0,
                'filter' => [
                    0 => 'help',
                    1 => 'information',
                    2 => 'more',
                    3 => 'details'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            86 => [
                'name' => 'Crosshairs',
                'id' => 'crosshairs',
                'unicode' => 'f05b',
                'created' => 1.0,
                'filter' => [
                    0 => 'picker'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            87 => [
                'name' => 'Times Circle Outlined',
                'id' => 'times-circle-o',
                'unicode' => 'f05c',
                'created' => 1.0,
                'filter' => [
                    0 => 'close',
                    1 => 'exit',
                    2 => 'x'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            88 => [
                'name' => 'Check Circle Outlined',
                'id' => 'check-circle-o',
                'unicode' => 'f05d',
                'created' => 1.0,
                'filter' => [
                    0 => 'todo',
                    1 => 'done',
                    2 => 'agree',
                    3 => 'accept',
                    4 => 'confirm',
                    5 => 'ok'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            89 => [
                'name' => 'ban',
                'id' => 'ban',
                'unicode' => 'f05e',
                'created' => 1.0,
                'filter' => [
                    0 => 'delete',
                    1 => 'remove',
                    2 => 'trash',
                    3 => 'hide',
                    4 => 'block',
                    5 => 'stop',
                    6 => 'abort',
                    7 => 'cancel'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            90 => [
                'name' => 'arrow-left',
                'id' => 'arrow-left',
                'unicode' => 'f060',
                'created' => 1.0,
                'filter' => [
                    0 => 'previous',
                    1 => 'back'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            91 => [
                'name' => 'arrow-right',
                'id' => 'arrow-right',
                'unicode' => 'f061',
                'created' => 1.0,
                'filter' => [
                    0 => 'next',
                    1 => 'forward'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            92 => [
                'name' => 'arrow-up',
                'id' => 'arrow-up',
                'unicode' => 'f062',
                'created' => 1.0,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            93 => [
                'name' => 'arrow-down',
                'id' => 'arrow-down',
                'unicode' => 'f063',
                'created' => 1.0,
                'filter' => [
                    0 => 'download'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            94 => [
                'name' => 'Share',
                'id' => 'share',
                'unicode' => 'f064',
                'created' => 1.0,
                'aliases' => [
                    0 => 'mail-forward'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            95 => [
                'name' => 'Expand',
                'id' => 'expand',
                'unicode' => 'f065',
                'created' => 1.0,
                'filter' => [
                    0 => 'enlarge',
                    1 => 'bigger',
                    2 => 'resize'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            96 => [
                'name' => 'Compress',
                'id' => 'compress',
                'unicode' => 'f066',
                'created' => 1.0,
                'filter' => [
                    0 => 'collapse',
                    1 => 'combine',
                    2 => 'contract',
                    3 => 'merge',
                    4 => 'smaller'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            97 => [
                'name' => 'plus',
                'id' => 'plus',
                'unicode' => 'f067',
                'created' => 1.0,
                'filter' => [
                    0 => 'add',
                    1 => 'new',
                    2 => 'create',
                    3 => 'expand'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            98 => [
                'name' => 'minus',
                'id' => 'minus',
                'unicode' => 'f068',
                'created' => 1.0,
                'filter' => [
                    0 => 'hide',
                    1 => 'minify',
                    2 => 'delete',
                    3 => 'remove',
                    4 => 'trash',
                    5 => 'hide',
                    6 => 'collapse'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            99 => [
                'name' => 'asterisk',
                'id' => 'asterisk',
                'unicode' => 'f069',
                'created' => 1.0,
                'filter' => [
                    0 => 'details'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            100 => [
                'name' => 'Exclamation Circle',
                'id' => 'exclamation-circle',
                'unicode' => 'f06a',
                'created' => 1.0,
                'filter' => [
                    0 => 'warning',
                    1 => 'error',
                    2 => 'problem',
                    3 => 'notification',
                    4 => 'alert'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            101 => [
                'name' => 'gift',
                'id' => 'gift',
                'unicode' => 'f06b',
                'created' => 1.0,
                'filter' => [
                    0 => 'present'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            102 => [
                'name' => 'leaf',
                'id' => 'leaf',
                'unicode' => 'f06c',
                'created' => 1.0,
                'filter' => [
                    0 => 'eco',
                    1 => 'nature',
                    2 => 'plant'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            103 => [
                'name' => 'fire',
                'id' => 'fire',
                'unicode' => 'f06d',
                'created' => 1.0,
                'filter' => [
                    0 => 'flame',
                    1 => 'hot',
                    2 => 'popular'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            104 => [
                'name' => 'Eye',
                'id' => 'eye',
                'unicode' => 'f06e',
                'created' => 1.0,
                'filter' => [
                    0 => 'show',
                    1 => 'visible',
                    2 => 'views'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            105 => [
                'name' => 'Eye Slash',
                'id' => 'eye-slash',
                'unicode' => 'f070',
                'created' => 1.0,
                'filter' => [
                    0 => 'toggle',
                    1 => 'show',
                    2 => 'hide',
                    3 => 'visible',
                    4 => 'visiblity',
                    5 => 'views'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            106 => [
                'name' => 'Exclamation Triangle',
                'id' => 'exclamation-triangle',
                'unicode' => 'f071',
                'created' => 1.0,
                'filter' => [
                    0 => 'warning',
                    1 => 'error',
                    2 => 'problem',
                    3 => 'notification',
                    4 => 'alert'
                ],
                'aliases' => [
                    0 => 'warning'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            107 => [
                'name' => 'plane',
                'id' => 'plane',
                'unicode' => 'f072',
                'created' => 1.0,
                'filter' => [
                    0 => 'travel',
                    1 => 'trip',
                    2 => 'location',
                    3 => 'destination',
                    4 => 'airplane',
                    5 => 'fly',
                    6 => 'mode'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            108 => [
                'name' => 'calendar',
                'id' => 'calendar',
                'unicode' => 'f073',
                'created' => 1.0,
                'filter' => [
                    0 => 'date',
                    1 => 'time',
                    2 => 'when',
                    3 => 'event'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            109 => [
                'name' => 'random',
                'id' => 'random',
                'unicode' => 'f074',
                'created' => 1.0,
                'filter' => [
                    0 => 'sort',
                    1 => 'shuffle'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Video Player Icons'
                ]
            ],
            110 => [
                'name' => 'comment',
                'id' => 'comment',
                'unicode' => 'f075',
                'created' => 1.0,
                'filter' => [
                    0 => 'speech',
                    1 => 'notification',
                    2 => 'note',
                    3 => 'chat',
                    4 => 'bubble',
                    5 => 'feedback',
                    6 => 'message',
                    7 => 'texting',
                    8 => 'sms',
                    9 => 'conversation'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            111 => [
                'name' => 'magnet',
                'id' => 'magnet',
                'unicode' => 'f076',
                'created' => 1.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            112 => [
                'name' => 'chevron-up',
                'id' => 'chevron-up',
                'unicode' => 'f077',
                'created' => 1.0,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            113 => [
                'name' => 'chevron-down',
                'id' => 'chevron-down',
                'unicode' => 'f078',
                'created' => 1.0,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            114 => [
                'name' => 'retweet',
                'id' => 'retweet',
                'unicode' => 'f079',
                'created' => 1.0,
                'filter' => [
                    0 => 'refresh',
                    1 => 'reload',
                    2 => 'share'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            115 => [
                'name' => 'shopping-cart',
                'id' => 'shopping-cart',
                'unicode' => 'f07a',
                'created' => 1.0,
                'filter' => [
                    0 => 'checkout',
                    1 => 'buy',
                    2 => 'purchase',
                    3 => 'payment'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            116 => [
                'name' => 'Folder',
                'id' => 'folder',
                'unicode' => 'f07b',
                'created' => 1.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            117 => [
                'name' => 'Folder Open',
                'id' => 'folder-open',
                'unicode' => 'f07c',
                'created' => 1.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            118 => [
                'name' => 'Arrows Vertical',
                'id' => 'arrows-v',
                'unicode' => 'f07d',
                'created' => 1.0,
                'filter' => [
                    0 => 'resize'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            119 => [
                'name' => 'Arrows Horizontal',
                'id' => 'arrows-h',
                'unicode' => 'f07e',
                'created' => 1.0,
                'filter' => [
                    0 => 'resize'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            120 => [
                'name' => 'Bar Chart',
                'id' => 'bar-chart',
                'unicode' => 'f080',
                'created' => 1.0,
                'aliases' => [
                    0 => 'bar-chart-o'
                ],
                'filter' => [
                    0 => 'graph',
                    1 => 'analytics'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Chart Icons'
                ]
            ],
            121 => [
                'name' => 'Twitter Square',
                'id' => 'twitter-square',
                'unicode' => 'f081',
                'created' => 1.0,
                'filter' => [
                    0 => 'tweet',
                    1 => 'social network'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            122 => [
                'name' => 'Facebook Square',
                'id' => 'facebook-square',
                'unicode' => 'f082',
                'created' => 1.0,
                'filter' => [
                    0 => 'social network'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            123 => [
                'name' => 'camera-retro',
                'id' => 'camera-retro',
                'unicode' => 'f083',
                'created' => 1.0,
                'filter' => [
                    0 => 'photo',
                    1 => 'picture',
                    2 => 'record'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            124 => [
                'name' => 'key',
                'id' => 'key',
                'unicode' => 'f084',
                'created' => 1.0,
                'filter' => [
                    0 => 'unlock',
                    1 => 'password'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            125 => [
                'name' => 'cogs',
                'id' => 'cogs',
                'unicode' => 'f085',
                'created' => 1.0,
                'aliases' => [
                    0 => 'gears'
                ],
                'filter' => [
                    0 => 'settings'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            126 => [
                'name' => 'comments',
                'id' => 'comments',
                'unicode' => 'f086',
                'created' => 1.0,
                'filter' => [
                    0 => 'speech',
                    1 => 'notification',
                    2 => 'note',
                    3 => 'chat',
                    4 => 'bubble',
                    5 => 'feedback',
                    6 => 'message',
                    7 => 'texting',
                    8 => 'sms',
                    9 => 'conversation'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            127 => [
                'name' => 'Thumbs Up Outlined',
                'id' => 'thumbs-o-up',
                'unicode' => 'f087',
                'created' => 1.0,
                'filter' => [
                    0 => 'like',
                    1 => 'approve',
                    2 => 'favorite',
                    3 => 'agree',
                    4 => 'hand'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            128 => [
                'name' => 'Thumbs Down Outlined',
                'id' => 'thumbs-o-down',
                'unicode' => 'f088',
                'created' => 1.0,
                'filter' => [
                    0 => 'dislike',
                    1 => 'disapprove',
                    2 => 'disagree',
                    3 => 'hand'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            129 => [
                'name' => 'star-half',
                'id' => 'star-half',
                'unicode' => 'f089',
                'created' => 1.0,
                'filter' => [
                    0 => 'award',
                    1 => 'achievement',
                    2 => 'rating',
                    3 => 'score'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            130 => [
                'name' => 'Heart Outlined',
                'id' => 'heart-o',
                'unicode' => 'f08a',
                'created' => 1.0,
                'filter' => [
                    0 => 'love',
                    1 => 'like',
                    2 => 'favorite'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Medical Icons'
                ]
            ],
            131 => [
                'name' => 'Sign Out',
                'id' => 'sign-out',
                'unicode' => 'f08b',
                'created' => 1.0,
                'filter' => [
                    0 => 'log out',
                    1 => 'logout',
                    2 => 'leave',
                    3 => 'exit',
                    4 => 'arrow'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            132 => [
                'name' => 'LinkedIn Square',
                'id' => 'linkedin-square',
                'unicode' => 'f08c',
                'created' => 1.0,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            133 => [
                'name' => 'Thumb Tack',
                'id' => 'thumb-tack',
                'unicode' => 'f08d',
                'created' => 1.0,
                'filter' => [
                    0 => 'marker',
                    1 => 'pin',
                    2 => 'location',
                    3 => 'coordinates'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            134 => [
                'name' => 'External Link',
                'id' => 'external-link',
                'unicode' => 'f08e',
                'created' => 1.0,
                'filter' => [
                    0 => 'open',
                    1 => 'new'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            135 => [
                'name' => 'Sign In',
                'id' => 'sign-in',
                'unicode' => 'f090',
                'created' => 1.0,
                'filter' => [
                    0 => 'enter',
                    1 => 'join',
                    2 => 'log in',
                    3 => 'login',
                    4 => 'sign up',
                    5 => 'sign in',
                    6 => 'signin',
                    7 => 'signup',
                    8 => 'arrow'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            136 => [
                'name' => 'trophy',
                'id' => 'trophy',
                'unicode' => 'f091',
                'created' => 1.0,
                'filter' => [
                    0 => 'award',
                    1 => 'achievement',
                    2 => 'cup',
                    3 => 'winner',
                    4 => 'game'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            137 => [
                'name' => 'GitHub Square',
                'id' => 'github-square',
                'unicode' => 'f092',
                'created' => 1.0,
                'url' => 'github.com/logos',
                'filter' => [
                    0 => 'octocat'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            138 => [
                'name' => 'Upload',
                'id' => 'upload',
                'unicode' => 'f093',
                'created' => 1.0,
                'filter' => [
                    0 => 'import'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            139 => [
                'name' => 'Lemon Outlined',
                'id' => 'lemon-o',
                'unicode' => 'f094',
                'created' => 1.0,
                'filter' => [
                    0 => 'food'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            140 => [
                'name' => 'Phone',
                'id' => 'phone',
                'unicode' => 'f095',
                'created' => 2.0,
                'filter' => [
                    0 => 'call',
                    1 => 'voice',
                    2 => 'number',
                    3 => 'support',
                    4 => 'earphone',
                    5 => 'telephone'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            141 => [
                'name' => 'Square Outlined',
                'id' => 'square-o',
                'unicode' => 'f096',
                'created' => 2.0,
                'filter' => [
                    0 => 'block',
                    1 => 'square',
                    2 => 'box'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            142 => [
                'name' => 'Bookmark Outlined',
                'id' => 'bookmark-o',
                'unicode' => 'f097',
                'created' => 2.0,
                'filter' => [
                    0 => 'save'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            143 => [
                'name' => 'Phone Square',
                'id' => 'phone-square',
                'unicode' => 'f098',
                'created' => 2.0,
                'filter' => [
                    0 => 'call',
                    1 => 'voice',
                    2 => 'number',
                    3 => 'support',
                    4 => 'telephone'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            144 => [
                'name' => 'Twitter',
                'id' => 'twitter',
                'unicode' => 'f099',
                'created' => 2.0,
                'filter' => [
                    0 => 'tweet',
                    1 => 'social network'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            145 => [
                'name' => 'Facebook',
                'id' => 'facebook',
                'unicode' => 'f09a',
                'created' => 2.0,
                'aliases' => [
                    0 => 'facebook-f'
                ],
                'filter' => [
                    0 => 'social network'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            146 => [
                'name' => 'GitHub',
                'id' => 'github',
                'unicode' => 'f09b',
                'created' => 2.0,
                'url' => 'github.com/logos',
                'filter' => [
                    0 => 'octocat'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            147 => [
                'name' => 'unlock',
                'id' => 'unlock',
                'unicode' => 'f09c',
                'created' => 2.0,
                'filter' => [
                    0 => 'protect',
                    1 => 'admin',
                    2 => 'password',
                    3 => 'lock'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            148 => [
                'name' => 'credit-card',
                'id' => 'credit-card',
                'unicode' => 'f09d',
                'created' => 2.0,
                'filter' => [
                    0 => 'money',
                    1 => 'buy',
                    2 => 'debit',
                    3 => 'checkout',
                    4 => 'purchase',
                    5 => 'payment'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Payment Icons'
                ]
            ],
            149 => [
                'name' => 'rss',
                'id' => 'rss',
                'unicode' => 'f09e',
                'created' => 2.0,
                'filter' => [
                    0 => 'blog'
                ],
                'aliases' => [
                    0 => 'feed'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            150 => [
                'name' => 'HDD',
                'id' => 'hdd-o',
                'unicode' => 'f0a0',
                'created' => 2.0,
                'filter' => [
                    0 => 'harddrive',
                    1 => 'hard drive',
                    2 => 'storage',
                    3 => 'save'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            151 => [
                'name' => 'bullhorn',
                'id' => 'bullhorn',
                'unicode' => 'f0a1',
                'created' => 2.0,
                'filter' => [
                    0 => 'announcement',
                    1 => 'share',
                    2 => 'broadcast',
                    3 => 'louder',
                    4 => 'megaphone'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            152 => [
                'name' => 'bell',
                'id' => 'bell',
                'unicode' => 'f0f3',
                'created' => 2.0,
                'filter' => [
                    0 => 'alert',
                    1 => 'reminder',
                    2 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            153 => [
                'name' => 'certificate',
                'id' => 'certificate',
                'unicode' => 'f0a3',
                'created' => 2.0,
                'filter' => [
                    0 => 'badge',
                    1 => 'star'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            154 => [
                'name' => 'Hand Outlined Right',
                'id' => 'hand-o-right',
                'unicode' => 'f0a4',
                'created' => 2.0,
                'filter' => [
                    0 => 'point',
                    1 => 'right',
                    2 => 'next',
                    3 => 'forward',
                    4 => 'finger'
                ],
                'categories' => [
                    0 => 'Directional Icons',
                    1 => 'Hand Icons'
                ]
            ],
            155 => [
                'name' => 'Hand Outlined Left',
                'id' => 'hand-o-left',
                'unicode' => 'f0a5',
                'created' => 2.0,
                'filter' => [
                    0 => 'point',
                    1 => 'left',
                    2 => 'previous',
                    3 => 'back',
                    4 => 'finger'
                ],
                'categories' => [
                    0 => 'Directional Icons',
                    1 => 'Hand Icons'
                ]
            ],
            156 => [
                'name' => 'Hand Outlined Up',
                'id' => 'hand-o-up',
                'unicode' => 'f0a6',
                'created' => 2.0,
                'filter' => [
                    0 => 'point',
                    1 => 'finger'
                ],
                'categories' => [
                    0 => 'Directional Icons',
                    1 => 'Hand Icons'
                ]
            ],
            157 => [
                'name' => 'Hand Outlined Down',
                'id' => 'hand-o-down',
                'unicode' => 'f0a7',
                'created' => 2.0,
                'filter' => [
                    0 => 'point',
                    1 => 'finger'
                ],
                'categories' => [
                    0 => 'Directional Icons',
                    1 => 'Hand Icons'
                ]
            ],
            158 => [
                'name' => 'Arrow Circle Left',
                'id' => 'arrow-circle-left',
                'unicode' => 'f0a8',
                'created' => 2.0,
                'filter' => [
                    0 => 'previous',
                    1 => 'back'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            159 => [
                'name' => 'Arrow Circle Right',
                'id' => 'arrow-circle-right',
                'unicode' => 'f0a9',
                'created' => 2.0,
                'filter' => [
                    0 => 'next',
                    1 => 'forward'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            160 => [
                'name' => 'Arrow Circle Up',
                'id' => 'arrow-circle-up',
                'unicode' => 'f0aa',
                'created' => 2.0,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            161 => [
                'name' => 'Arrow Circle Down',
                'id' => 'arrow-circle-down',
                'unicode' => 'f0ab',
                'created' => 2.0,
                'filter' => [
                    0 => 'download'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            162 => [
                'name' => 'Globe',
                'id' => 'globe',
                'unicode' => 'f0ac',
                'created' => 2.0,
                'filter' => [
                    0 => 'world',
                    1 => 'planet',
                    2 => 'map',
                    3 => 'place',
                    4 => 'travel',
                    5 => 'earth',
                    6 => 'global',
                    7 => 'translate',
                    8 => 'all',
                    9 => 'language',
                    10 => 'localize',
                    11 => 'location',
                    12 => 'coordinates',
                    13 => 'country'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            163 => [
                'name' => 'Wrench',
                'id' => 'wrench',
                'unicode' => 'f0ad',
                'created' => 2.0,
                'filter' => [
                    0 => 'settings',
                    1 => 'fix',
                    2 => 'update',
                    3 => 'spanner'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            164 => [
                'name' => 'Tasks',
                'id' => 'tasks',
                'unicode' => 'f0ae',
                'created' => 2.0,
                'filter' => [
                    0 => 'progress',
                    1 => 'loading',
                    2 => 'downloading',
                    3 => 'downloads',
                    4 => 'settings'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            165 => [
                'name' => 'Filter',
                'id' => 'filter',
                'unicode' => 'f0b0',
                'created' => 2.0,
                'filter' => [
                    0 => 'funnel',
                    1 => 'options'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            166 => [
                'name' => 'Briefcase',
                'id' => 'briefcase',
                'unicode' => 'f0b1',
                'created' => 2.0,
                'filter' => [
                    0 => 'work',
                    1 => 'business',
                    2 => 'office',
                    3 => 'luggage',
                    4 => 'bag'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            167 => [
                'name' => 'Arrows Alt',
                'id' => 'arrows-alt',
                'unicode' => 'f0b2',
                'created' => 2.0,
                'filter' => [
                    0 => 'expand',
                    1 => 'enlarge',
                    2 => 'fullscreen',
                    3 => 'bigger',
                    4 => 'move',
                    5 => 'reorder',
                    6 => 'resize',
                    7 => 'arrow'
                ],
                'categories' => [
                    0 => 'Video Player Icons',
                    1 => 'Directional Icons'
                ]
            ],
            168 => [
                'name' => 'Users',
                'id' => 'users',
                'unicode' => 'f0c0',
                'created' => 2.0,
                'filter' => [
                    0 => 'people',
                    1 => 'profiles',
                    2 => 'persons'
                ],
                'aliases' => [
                    0 => 'group'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            169 => [
                'name' => 'Link',
                'id' => 'link',
                'unicode' => 'f0c1',
                'created' => 2.0,
                'filter' => [
                    0 => 'chain'
                ],
                'aliases' => [
                    0 => 'chain'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            170 => [
                'name' => 'Cloud',
                'id' => 'cloud',
                'filter' => [
                    0 => 'save'
                ],
                'unicode' => 'f0c2',
                'created' => 2.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            171 => [
                'name' => 'Flask',
                'id' => 'flask',
                'unicode' => 'f0c3',
                'created' => 2.0,
                'filter' => [
                    0 => 'science',
                    1 => 'beaker',
                    2 => 'experimental',
                    3 => 'labs'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            172 => [
                'name' => 'Scissors',
                'id' => 'scissors',
                'unicode' => 'f0c4',
                'created' => 2.0,
                'aliases' => [
                    0 => 'cut'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            173 => [
                'name' => 'Files Outlined',
                'id' => 'files-o',
                'unicode' => 'f0c5',
                'created' => 2.0,
                'filter' => [
                    0 => 'duplicate',
                    1 => 'clone',
                    2 => 'copy'
                ],
                'aliases' => [
                    0 => 'copy'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            174 => [
                'name' => 'Paperclip',
                'id' => 'paperclip',
                'unicode' => 'f0c6',
                'created' => 2.0,
                'filter' => [
                    0 => 'attachment'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            175 => [
                'name' => 'Floppy Outlined',
                'id' => 'floppy-o',
                'unicode' => 'f0c7',
                'created' => 2.0,
                'aliases' => [
                    0 => 'save'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            176 => [
                'name' => 'Square',
                'id' => 'square',
                'unicode' => 'f0c8',
                'created' => 2.0,
                'filter' => [
                    0 => 'block',
                    1 => 'box'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            177 => [
                'name' => 'Bars',
                'id' => 'bars',
                'unicode' => 'f0c9',
                'created' => 2.0,
                'aliases' => [
                    0 => 'navicon',
                    1 => 'reorder'
                ],
                'filter' => [
                    0 => 'menu',
                    1 => 'drag',
                    2 => 'reorder',
                    3 => 'settings',
                    4 => 'list',
                    5 => 'ul',
                    6 => 'ol',
                    7 => 'checklist',
                    8 => 'todo',
                    9 => 'list',
                    10 => 'hamburger'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            178 => [
                'name' => 'list-ul',
                'id' => 'list-ul',
                'unicode' => 'f0ca',
                'created' => 2.0,
                'filter' => [
                    0 => 'ul',
                    1 => 'ol',
                    2 => 'checklist',
                    3 => 'todo',
                    4 => 'list'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            179 => [
                'name' => 'list-ol',
                'id' => 'list-ol',
                'unicode' => 'f0cb',
                'created' => 2.0,
                'filter' => [
                    0 => 'ul',
                    1 => 'ol',
                    2 => 'checklist',
                    3 => 'list',
                    4 => 'todo',
                    5 => 'list',
                    6 => 'numbers'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            180 => [
                'name' => 'Strikethrough',
                'id' => 'strikethrough',
                'unicode' => 'f0cc',
                'created' => 2.0,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            181 => [
                'name' => 'Underline',
                'id' => 'underline',
                'unicode' => 'f0cd',
                'created' => 2.0,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            182 => [
                'name' => 'table',
                'id' => 'table',
                'unicode' => 'f0ce',
                'created' => 2.0,
                'filter' => [
                    0 => 'data',
                    1 => 'excel',
                    2 => 'spreadsheet'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            183 => [
                'name' => 'magic',
                'id' => 'magic',
                'unicode' => 'f0d0',
                'created' => 2.0,
                'filter' => [
                    0 => 'wizard',
                    1 => 'automatic',
                    2 => 'autocomplete'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            184 => [
                'name' => 'truck',
                'id' => 'truck',
                'unicode' => 'f0d1',
                'created' => 2.0,
                'filter' => [
                    0 => 'shipping'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            185 => [
                'name' => 'Pinterest',
                'id' => 'pinterest',
                'unicode' => 'f0d2',
                'created' => 2.0,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            186 => [
                'name' => 'Pinterest Square',
                'id' => 'pinterest-square',
                'unicode' => 'f0d3',
                'created' => 2.0,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            187 => [
                'name' => 'Google Plus Square',
                'id' => 'google-plus-square',
                'unicode' => 'f0d4',
                'created' => 2.0,
                'filter' => [
                    0 => 'social network'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            188 => [
                'name' => 'Google Plus',
                'id' => 'google-plus',
                'unicode' => 'f0d5',
                'created' => 2.0,
                'filter' => [
                    0 => 'social network'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            189 => [
                'name' => 'Money',
                'id' => 'money',
                'unicode' => 'f0d6',
                'created' => 2.0,
                'filter' => [
                    0 => 'cash',
                    1 => 'money',
                    2 => 'buy',
                    3 => 'checkout',
                    4 => 'purchase',
                    5 => 'payment'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Currency Icons'
                ]
            ],
            190 => [
                'name' => 'Caret Down',
                'id' => 'caret-down',
                'unicode' => 'f0d7',
                'created' => 2.0,
                'filter' => [
                    0 => 'more',
                    1 => 'dropdown',
                    2 => 'menu',
                    3 => 'triangle down',
                    4 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            191 => [
                'name' => 'Caret Up',
                'id' => 'caret-up',
                'unicode' => 'f0d8',
                'created' => 2.0,
                'filter' => [
                    0 => 'triangle up',
                    1 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            192 => [
                'name' => 'Caret Left',
                'id' => 'caret-left',
                'unicode' => 'f0d9',
                'created' => 2.0,
                'filter' => [
                    0 => 'previous',
                    1 => 'back',
                    2 => 'triangle left',
                    3 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            193 => [
                'name' => 'Caret Right',
                'id' => 'caret-right',
                'unicode' => 'f0da',
                'created' => 2.0,
                'filter' => [
                    0 => 'next',
                    1 => 'forward',
                    2 => 'triangle right',
                    3 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            194 => [
                'name' => 'Columns',
                'id' => 'columns',
                'unicode' => 'f0db',
                'created' => 2.0,
                'filter' => [
                    0 => 'split',
                    1 => 'panes'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            195 => [
                'name' => 'Sort',
                'id' => 'sort',
                'unicode' => 'f0dc',
                'created' => 2.0,
                'filter' => [
                    0 => 'order'
                ],
                'aliases' => [
                    0 => 'unsorted'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            196 => [
                'name' => 'Sort Descending',
                'id' => 'sort-desc',
                'unicode' => 'f0dd',
                'created' => 2.0,
                'filter' => [
                    0 => 'dropdown',
                    1 => 'more',
                    2 => 'menu',
                    3 => 'arrow'
                ],
                'aliases' => [
                    0 => 'sort-down'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            197 => [
                'name' => 'Sort Ascending',
                'id' => 'sort-asc',
                'unicode' => 'f0de',
                'created' => 2.0,
                'aliases' => [
                    0 => 'sort-up'
                ],
                'filter' => [
                    0 => 'arrow'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            198 => [
                'name' => 'Envelope',
                'id' => 'envelope',
                'unicode' => 'f0e0',
                'created' => 2.0,
                'filter' => [
                    0 => 'email',
                    1 => 'e-mail',
                    2 => 'letter',
                    3 => 'support',
                    4 => 'mail',
                    5 => 'message',
                    6 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            199 => [
                'name' => 'LinkedIn',
                'id' => 'linkedin',
                'unicode' => 'f0e1',
                'created' => 2.0,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            200 => [
                'name' => 'Undo',
                'id' => 'undo',
                'unicode' => 'f0e2',
                'created' => 2.0,
                'filter' => [
                    0 => 'back'
                ],
                'aliases' => [
                    0 => 'rotate-left'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            201 => [
                'name' => 'Gavel',
                'id' => 'gavel',
                'unicode' => 'f0e3',
                'created' => 2.0,
                'filter' => [
                    0 => 'judge',
                    1 => 'lawyer',
                    2 => 'opinion'
                ],
                'aliases' => [
                    0 => 'legal'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            202 => [
                'name' => 'Tachometer',
                'id' => 'tachometer',
                'unicode' => 'f0e4',
                'created' => 2.0,
                'filter' => [
                    0 => 'speedometer',
                    1 => 'fast'
                ],
                'aliases' => [
                    0 => 'dashboard'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            203 => [
                'name' => 'comment-o',
                'id' => 'comment-o',
                'unicode' => 'f0e5',
                'created' => 2.0,
                'filter' => [
                    0 => 'speech',
                    1 => 'notification',
                    2 => 'note',
                    3 => 'chat',
                    4 => 'bubble',
                    5 => 'feedback',
                    6 => 'message',
                    7 => 'texting',
                    8 => 'sms',
                    9 => 'conversation'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            204 => [
                'name' => 'comments-o',
                'id' => 'comments-o',
                'unicode' => 'f0e6',
                'created' => 2.0,
                'filter' => [
                    0 => 'speech',
                    1 => 'notification',
                    2 => 'note',
                    3 => 'chat',
                    4 => 'bubble',
                    5 => 'feedback',
                    6 => 'message',
                    7 => 'texting',
                    8 => 'sms',
                    9 => 'conversation'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            205 => [
                'name' => 'Lightning Bolt',
                'id' => 'bolt',
                'unicode' => 'f0e7',
                'created' => 2.0,
                'filter' => [
                    0 => 'lightning',
                    1 => 'weather'
                ],
                'aliases' => [
                    0 => 'flash'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            206 => [
                'name' => 'Sitemap',
                'id' => 'sitemap',
                'unicode' => 'f0e8',
                'created' => 2.0,
                'filter' => [
                    0 => 'directory',
                    1 => 'hierarchy',
                    2 => 'organization'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            207 => [
                'name' => 'Umbrella',
                'id' => 'umbrella',
                'unicode' => 'f0e9',
                'created' => 2.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            208 => [
                'name' => 'Clipboard',
                'id' => 'clipboard',
                'unicode' => 'f0ea',
                'created' => 2.0,
                'filter' => [
                    0 => 'copy'
                ],
                'aliases' => [
                    0 => 'paste'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            209 => [
                'name' => 'Lightbulb Outlined',
                'id' => 'lightbulb-o',
                'unicode' => 'f0eb',
                'created' => 3.0,
                'filter' => [
                    0 => 'idea',
                    1 => 'inspiration'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            210 => [
                'name' => 'Exchange',
                'id' => 'exchange',
                'unicode' => 'f0ec',
                'created' => 3.0,
                'filter' => [
                    0 => 'transfer',
                    1 => 'arrows',
                    2 => 'arrow'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            211 => [
                'name' => 'Cloud Download',
                'id' => 'cloud-download',
                'unicode' => 'f0ed',
                'created' => 3.0,
                'filter' => [
                    0 => 'import'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            212 => [
                'name' => 'Cloud Upload',
                'id' => 'cloud-upload',
                'unicode' => 'f0ee',
                'created' => 3.0,
                'filter' => [
                    0 => 'import'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            213 => [
                'name' => 'user-md',
                'id' => 'user-md',
                'unicode' => 'f0f0',
                'created' => 2.0,
                'filter' => [
                    0 => 'doctor',
                    1 => 'profile',
                    2 => 'medical',
                    3 => 'nurse'
                ],
                'categories' => [
                    0 => 'Medical Icons'
                ]
            ],
            214 => [
                'name' => 'Stethoscope',
                'id' => 'stethoscope',
                'unicode' => 'f0f1',
                'created' => 3.0,
                'categories' => [
                    0 => 'Medical Icons'
                ]
            ],
            215 => [
                'name' => 'Suitcase',
                'id' => 'suitcase',
                'unicode' => 'f0f2',
                'created' => 3.0,
                'filter' => [
                    0 => 'trip',
                    1 => 'luggage',
                    2 => 'travel',
                    3 => 'move',
                    4 => 'baggage'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            216 => [
                'name' => 'Bell Outlined',
                'id' => 'bell-o',
                'unicode' => 'f0a2',
                'created' => 3.0,
                'filter' => [
                    0 => 'alert',
                    1 => 'reminder',
                    2 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            217 => [
                'name' => 'Coffee',
                'id' => 'coffee',
                'unicode' => 'f0f4',
                'created' => 3.0,
                'filter' => [
                    0 => 'morning',
                    1 => 'mug',
                    2 => 'breakfast',
                    3 => 'tea',
                    4 => 'drink',
                    5 => 'cafe'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            218 => [
                'name' => 'Cutlery',
                'id' => 'cutlery',
                'unicode' => 'f0f5',
                'created' => 3.0,
                'filter' => [
                    0 => 'food',
                    1 => 'restaurant',
                    2 => 'spoon',
                    3 => 'knife',
                    4 => 'dinner',
                    5 => 'eat'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            219 => [
                'name' => 'File Text Outlined',
                'id' => 'file-text-o',
                'unicode' => 'f0f6',
                'created' => 3.0,
                'filter' => [
                    0 => 'new',
                    1 => 'page',
                    2 => 'pdf',
                    3 => 'document'
                ],
                'categories' => [
                    0 => 'Text Editor Icons',
                    1 => 'File Type Icons'
                ]
            ],
            220 => [
                'name' => 'Building Outlined',
                'id' => 'building-o',
                'unicode' => 'f0f7',
                'created' => 3.0,
                'filter' => [
                    0 => 'work',
                    1 => 'business',
                    2 => 'apartment',
                    3 => 'office',
                    4 => 'company'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            221 => [
                'name' => 'hospital Outlined',
                'id' => 'hospital-o',
                'unicode' => 'f0f8',
                'created' => 3.0,
                'filter' => [
                    0 => 'building'
                ],
                'categories' => [
                    0 => 'Medical Icons'
                ]
            ],
            222 => [
                'name' => 'ambulance',
                'id' => 'ambulance',
                'unicode' => 'f0f9',
                'created' => 3.0,
                'filter' => [
                    0 => 'vehicle',
                    1 => 'support',
                    2 => 'help'
                ],
                'categories' => [
                    0 => 'Medical Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            223 => [
                'name' => 'medkit',
                'id' => 'medkit',
                'unicode' => 'f0fa',
                'created' => 3.0,
                'filter' => [
                    0 => 'first aid',
                    1 => 'firstaid',
                    2 => 'help',
                    3 => 'support',
                    4 => 'health'
                ],
                'categories' => [
                    0 => 'Medical Icons'
                ]
            ],
            224 => [
                'name' => 'fighter-jet',
                'id' => 'fighter-jet',
                'unicode' => 'f0fb',
                'created' => 3.0,
                'filter' => [
                    0 => 'fly',
                    1 => 'plane',
                    2 => 'airplane',
                    3 => 'quick',
                    4 => 'fast',
                    5 => 'travel'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            225 => [
                'name' => 'beer',
                'id' => 'beer',
                'unicode' => 'f0fc',
                'created' => 3.0,
                'filter' => [
                    0 => 'alcohol',
                    1 => 'stein',
                    2 => 'drink',
                    3 => 'mug',
                    4 => 'bar',
                    5 => 'liquor'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            226 => [
                'name' => 'H Square',
                'id' => 'h-square',
                'unicode' => 'f0fd',
                'created' => 3.0,
                'filter' => [
                    0 => 'hospital',
                    1 => 'hotel'
                ],
                'categories' => [
                    0 => 'Medical Icons'
                ]
            ],
            227 => [
                'name' => 'Plus Square',
                'id' => 'plus-square',
                'unicode' => 'f0fe',
                'created' => 3.0,
                'filter' => [
                    0 => 'add',
                    1 => 'new',
                    2 => 'create',
                    3 => 'expand'
                ],
                'categories' => [
                    0 => 'Medical Icons',
                    1 => 'Web Application Icons',
                    2 => 'Form Control Icons'
                ]
            ],
            228 => [
                'name' => 'Angle Double Left',
                'id' => 'angle-double-left',
                'unicode' => 'f100',
                'created' => 3.0,
                'filter' => [
                    0 => 'laquo',
                    1 => 'quote',
                    2 => 'previous',
                    3 => 'back',
                    4 => 'arrows'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            229 => [
                'name' => 'Angle Double Right',
                'id' => 'angle-double-right',
                'unicode' => 'f101',
                'created' => 3.0,
                'filter' => [
                    0 => 'raquo',
                    1 => 'quote',
                    2 => 'next',
                    3 => 'forward',
                    4 => 'arrows'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            230 => [
                'name' => 'Angle Double Up',
                'id' => 'angle-double-up',
                'unicode' => 'f102',
                'created' => 3.0,
                'filter' => [
                    0 => 'arrows'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            231 => [
                'name' => 'Angle Double Down',
                'id' => 'angle-double-down',
                'unicode' => 'f103',
                'created' => 3.0,
                'filter' => [
                    0 => 'arrows'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            232 => [
                'name' => 'angle-left',
                'id' => 'angle-left',
                'unicode' => 'f104',
                'created' => 3.0,
                'filter' => [
                    0 => 'previous',
                    1 => 'back',
                    2 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            233 => [
                'name' => 'angle-right',
                'id' => 'angle-right',
                'unicode' => 'f105',
                'created' => 3.0,
                'filter' => [
                    0 => 'next',
                    1 => 'forward',
                    2 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            234 => [
                'name' => 'angle-up',
                'id' => 'angle-up',
                'unicode' => 'f106',
                'created' => 3.0,
                'filter' => [
                    0 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            235 => [
                'name' => 'angle-down',
                'id' => 'angle-down',
                'unicode' => 'f107',
                'created' => 3.0,
                'filter' => [
                    0 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            236 => [
                'name' => 'Desktop',
                'id' => 'desktop',
                'unicode' => 'f108',
                'created' => 3.0,
                'filter' => [
                    0 => 'monitor',
                    1 => 'screen',
                    2 => 'desktop',
                    3 => 'computer',
                    4 => 'demo',
                    5 => 'device'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            237 => [
                'name' => 'Laptop',
                'id' => 'laptop',
                'unicode' => 'f109',
                'created' => 3.0,
                'filter' => [
                    0 => 'demo',
                    1 => 'computer',
                    2 => 'device'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            238 => [
                'name' => 'tablet',
                'id' => 'tablet',
                'unicode' => 'f10a',
                'created' => 3.0,
                'filter' => [
                    0 => 'ipad',
                    1 => 'device'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            239 => [
                'name' => 'Mobile Phone',
                'id' => 'mobile',
                'unicode' => 'f10b',
                'created' => 3.0,
                'filter' => [
                    0 => 'cell phone',
                    1 => 'cellphone',
                    2 => 'text',
                    3 => 'call',
                    4 => 'iphone',
                    5 => 'number',
                    6 => 'telephone'
                ],
                'aliases' => [
                    0 => 'mobile-phone'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            240 => [
                'name' => 'Circle Outlined',
                'id' => 'circle-o',
                'unicode' => 'f10c',
                'created' => 3.0,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            241 => [
                'name' => 'quote-left',
                'id' => 'quote-left',
                'unicode' => 'f10d',
                'created' => 3.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            242 => [
                'name' => 'quote-right',
                'id' => 'quote-right',
                'unicode' => 'f10e',
                'created' => 3.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            243 => [
                'name' => 'Spinner',
                'id' => 'spinner',
                'unicode' => 'f110',
                'created' => 3.0,
                'filter' => [
                    0 => 'loading',
                    1 => 'progress'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Spinner Icons'
                ]
            ],
            244 => [
                'name' => 'Circle',
                'id' => 'circle',
                'unicode' => 'f111',
                'created' => 3.0,
                'filter' => [
                    0 => 'dot',
                    1 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            245 => [
                'name' => 'Reply',
                'id' => 'reply',
                'unicode' => 'f112',
                'created' => 3.0,
                'aliases' => [
                    0 => 'mail-reply'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            246 => [
                'name' => 'GitHub Alt',
                'id' => 'github-alt',
                'unicode' => 'f113',
                'created' => 3.0,
                'url' => 'github.com/logos',
                'filter' => [
                    0 => 'octocat'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            247 => [
                'name' => 'Folder Outlined',
                'id' => 'folder-o',
                'unicode' => 'f114',
                'created' => 3.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            248 => [
                'name' => 'Folder Open Outlined',
                'id' => 'folder-open-o',
                'unicode' => 'f115',
                'created' => 3.0,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            249 => [
                'name' => 'Smile Outlined',
                'id' => 'smile-o',
                'unicode' => 'f118',
                'created' => 3.1,
                'filter' => [
                    0 => 'face',
                    1 => 'emoticon',
                    2 => 'happy',
                    3 => 'approve',
                    4 => 'satisfied',
                    5 => 'rating'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            250 => [
                'name' => 'Frown Outlined',
                'id' => 'frown-o',
                'unicode' => 'f119',
                'created' => 3.1,
                'filter' => [
                    0 => 'face',
                    1 => 'emoticon',
                    2 => 'sad',
                    3 => 'disapprove',
                    4 => 'rating'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            251 => [
                'name' => 'Meh Outlined',
                'id' => 'meh-o',
                'unicode' => 'f11a',
                'created' => 3.1,
                'filter' => [
                    0 => 'face',
                    1 => 'emoticon',
                    2 => 'rating',
                    3 => 'neutral'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            252 => [
                'name' => 'Gamepad',
                'id' => 'gamepad',
                'unicode' => 'f11b',
                'created' => 3.1,
                'filter' => [
                    0 => 'controller'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            253 => [
                'name' => 'Keyboard Outlined',
                'id' => 'keyboard-o',
                'unicode' => 'f11c',
                'created' => 3.1,
                'filter' => [
                    0 => 'type',
                    1 => 'input'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            254 => [
                'name' => 'Flag Outlined',
                'id' => 'flag-o',
                'unicode' => 'f11d',
                'created' => 3.1,
                'filter' => [
                    0 => 'report',
                    1 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            255 => [
                'name' => 'flag-checkered',
                'id' => 'flag-checkered',
                'unicode' => 'f11e',
                'created' => 3.1,
                'filter' => [
                    0 => 'report',
                    1 => 'notification',
                    2 => 'notify'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            256 => [
                'name' => 'Terminal',
                'id' => 'terminal',
                'unicode' => 'f120',
                'created' => 3.1,
                'filter' => [
                    0 => 'command',
                    1 => 'prompt',
                    2 => 'code'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            257 => [
                'name' => 'Code',
                'id' => 'code',
                'unicode' => 'f121',
                'created' => 3.1,
                'filter' => [
                    0 => 'html',
                    1 => 'brackets'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            258 => [
                'name' => 'reply-all',
                'id' => 'reply-all',
                'unicode' => 'f122',
                'created' => 3.1,
                'aliases' => [
                    0 => 'mail-reply-all'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            259 => [
                'name' => 'Star Half Outlined',
                'id' => 'star-half-o',
                'unicode' => 'f123',
                'created' => 3.1,
                'filter' => [
                    0 => 'award',
                    1 => 'achievement',
                    2 => 'rating',
                    3 => 'score'
                ],
                'aliases' => [
                    0 => 'star-half-empty',
                    1 => 'star-half-full'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            260 => [
                'name' => 'location-arrow',
                'id' => 'location-arrow',
                'unicode' => 'f124',
                'created' => 3.1,
                'filter' => [
                    0 => 'map',
                    1 => 'coordinates',
                    2 => 'location',
                    3 => 'address',
                    4 => 'place',
                    5 => 'where'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            261 => [
                'name' => 'crop',
                'id' => 'crop',
                'unicode' => 'f125',
                'created' => 3.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            262 => [
                'name' => 'code-fork',
                'id' => 'code-fork',
                'unicode' => 'f126',
                'created' => 3.1,
                'filter' => [
                    0 => 'git',
                    1 => 'fork',
                    2 => 'vcs',
                    3 => 'svn',
                    4 => 'github',
                    5 => 'rebase',
                    6 => 'version',
                    7 => 'merge'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            263 => [
                'name' => 'Chain Broken',
                'id' => 'chain-broken',
                'unicode' => 'f127',
                'created' => 3.1,
                'filter' => [
                    0 => 'remove'
                ],
                'aliases' => [
                    0 => 'unlink'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            264 => [
                'name' => 'Question',
                'id' => 'question',
                'unicode' => 'f128',
                'created' => 3.1,
                'filter' => [
                    0 => 'help',
                    1 => 'information',
                    2 => 'unknown',
                    3 => 'support'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            265 => [
                'name' => 'Info',
                'id' => 'info',
                'unicode' => 'f129',
                'created' => 3.1,
                'filter' => [
                    0 => 'help',
                    1 => 'information',
                    2 => 'more',
                    3 => 'details'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            266 => [
                'name' => 'exclamation',
                'id' => 'exclamation',
                'unicode' => 'f12a',
                'created' => 3.1,
                'filter' => [
                    0 => 'warning',
                    1 => 'error',
                    2 => 'problem',
                    3 => 'notification',
                    4 => 'notify',
                    5 => 'alert'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            267 => [
                'name' => 'superscript',
                'id' => 'superscript',
                'unicode' => 'f12b',
                'created' => 3.1,
                'filter' => [
                    0 => 'exponential'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            268 => [
                'name' => 'subscript',
                'id' => 'subscript',
                'unicode' => 'f12c',
                'created' => 3.1,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            269 => [
                'name' => 'eraser',
                'id' => 'eraser',
                'unicode' => 'f12d',
                'created' => 3.1,
                'filter' => [
                    0 => 'remove',
                    1 => 'delete'
                ],
                'categories' => [
                    0 => 'Text Editor Icons',
                    1 => 'Web Application Icons'
                ]
            ],
            270 => [
                'name' => 'Puzzle Piece',
                'id' => 'puzzle-piece',
                'unicode' => 'f12e',
                'created' => 3.1,
                'filter' => [
                    0 => 'addon',
                    1 => 'add-on',
                    2 => 'section'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            271 => [
                'name' => 'microphone',
                'id' => 'microphone',
                'unicode' => 'f130',
                'created' => 3.1,
                'filter' => [
                    0 => 'record',
                    1 => 'voice',
                    2 => 'sound'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            272 => [
                'name' => 'Microphone Slash',
                'id' => 'microphone-slash',
                'unicode' => 'f131',
                'created' => 3.1,
                'filter' => [
                    0 => 'record',
                    1 => 'voice',
                    2 => 'sound',
                    3 => 'mute'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            273 => [
                'name' => 'shield',
                'id' => 'shield',
                'unicode' => 'f132',
                'created' => 3.1,
                'filter' => [
                    0 => 'award',
                    1 => 'achievement',
                    2 => 'security',
                    3 => 'winner'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            274 => [
                'name' => 'calendar-o',
                'id' => 'calendar-o',
                'unicode' => 'f133',
                'created' => 3.1,
                'filter' => [
                    0 => 'date',
                    1 => 'time',
                    2 => 'when',
                    3 => 'event'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            275 => [
                'name' => 'fire-extinguisher',
                'id' => 'fire-extinguisher',
                'unicode' => 'f134',
                'created' => 3.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            276 => [
                'name' => 'rocket',
                'id' => 'rocket',
                'unicode' => 'f135',
                'created' => 3.1,
                'filter' => [
                    0 => 'app'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            277 => [
                'name' => 'MaxCDN',
                'id' => 'maxcdn',
                'unicode' => 'f136',
                'created' => 3.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            278 => [
                'name' => 'Chevron Circle Left',
                'id' => 'chevron-circle-left',
                'unicode' => 'f137',
                'created' => 3.1,
                'filter' => [
                    0 => 'previous',
                    1 => 'back',
                    2 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            279 => [
                'name' => 'Chevron Circle Right',
                'id' => 'chevron-circle-right',
                'unicode' => 'f138',
                'created' => 3.1,
                'filter' => [
                    0 => 'next',
                    1 => 'forward',
                    2 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            280 => [
                'name' => 'Chevron Circle Up',
                'id' => 'chevron-circle-up',
                'unicode' => 'f139',
                'created' => 3.1,
                'filter' => [
                    0 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            281 => [
                'name' => 'Chevron Circle Down',
                'id' => 'chevron-circle-down',
                'unicode' => 'f13a',
                'created' => 3.1,
                'filter' => [
                    0 => 'more',
                    1 => 'dropdown',
                    2 => 'menu',
                    3 => 'arrow'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            282 => [
                'name' => 'HTML 5 Logo',
                'id' => 'html5',
                'unicode' => 'f13b',
                'created' => 3.1,
                'code' => [
                    0 => 'code',
                    1 => 'html5'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            283 => [
                'name' => 'CSS 3 Logo',
                'id' => 'css3',
                'unicode' => 'f13c',
                'created' => 3.1,
                'filter' => [
                    0 => 'code'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            284 => [
                'name' => 'Anchor',
                'id' => 'anchor',
                'unicode' => 'f13d',
                'created' => 3.1,
                'filter' => [
                    0 => 'link'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            285 => [
                'name' => 'Unlock Alt',
                'id' => 'unlock-alt',
                'unicode' => 'f13e',
                'created' => 3.1,
                'filter' => [
                    0 => 'protect',
                    1 => 'admin',
                    2 => 'password',
                    3 => 'lock'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            286 => [
                'name' => 'Bullseye',
                'id' => 'bullseye',
                'unicode' => 'f140',
                'created' => 3.1,
                'filter' => [
                    0 => 'target'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            287 => [
                'name' => 'Ellipsis Horizontal',
                'id' => 'ellipsis-h',
                'unicode' => 'f141',
                'created' => 3.1,
                'filter' => [
                    0 => 'dots'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            288 => [
                'name' => 'Ellipsis Vertical',
                'id' => 'ellipsis-v',
                'unicode' => 'f142',
                'created' => 3.1,
                'filter' => [
                    0 => 'dots'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            289 => [
                'name' => 'RSS Square',
                'id' => 'rss-square',
                'unicode' => 'f143',
                'created' => 3.1,
                'filter' => [
                    0 => 'feed',
                    1 => 'blog'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            290 => [
                'name' => 'Play Circle',
                'id' => 'play-circle',
                'unicode' => 'f144',
                'created' => 3.1,
                'filter' => [
                    0 => 'start',
                    1 => 'playing'
                ],
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            291 => [
                'name' => 'Ticket',
                'id' => 'ticket',
                'unicode' => 'f145',
                'created' => 3.1,
                'filter' => [
                    0 => 'movie',
                    1 => 'pass',
                    2 => 'support'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            292 => [
                'name' => 'Minus Square',
                'id' => 'minus-square',
                'unicode' => 'f146',
                'created' => 3.1,
                'filter' => [
                    0 => 'hide',
                    1 => 'minify',
                    2 => 'delete',
                    3 => 'remove',
                    4 => 'trash',
                    5 => 'hide',
                    6 => 'collapse'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            293 => [
                'name' => 'Minus Square Outlined',
                'id' => 'minus-square-o',
                'unicode' => 'f147',
                'created' => 3.1,
                'filter' => [
                    0 => 'hide',
                    1 => 'minify',
                    2 => 'delete',
                    3 => 'remove',
                    4 => 'trash',
                    5 => 'hide',
                    6 => 'collapse'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            294 => [
                'name' => 'Level Up',
                'id' => 'level-up',
                'unicode' => 'f148',
                'created' => 3.1,
                'filter' => [
                    0 => 'arrow'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            295 => [
                'name' => 'Level Down',
                'id' => 'level-down',
                'unicode' => 'f149',
                'created' => 3.1,
                'filter' => [
                    0 => 'arrow'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            296 => [
                'name' => 'Check Square',
                'id' => 'check-square',
                'unicode' => 'f14a',
                'created' => 3.1,
                'filter' => [
                    0 => 'checkmark',
                    1 => 'done',
                    2 => 'todo',
                    3 => 'agree',
                    4 => 'accept',
                    5 => 'confirm',
                    6 => 'ok'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            297 => [
                'name' => 'Pencil Square',
                'id' => 'pencil-square',
                'unicode' => 'f14b',
                'created' => 3.1,
                'filter' => [
                    0 => 'write',
                    1 => 'edit',
                    2 => 'update'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            298 => [
                'name' => 'External Link Square',
                'id' => 'external-link-square',
                'unicode' => 'f14c',
                'created' => 3.1,
                'filter' => [
                    0 => 'open',
                    1 => 'new'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            299 => [
                'name' => 'Share Square',
                'id' => 'share-square',
                'unicode' => 'f14d',
                'created' => 3.1,
                'filter' => [
                    0 => 'social',
                    1 => 'send'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            300 => [
                'name' => 'Compass',
                'id' => 'compass',
                'unicode' => 'f14e',
                'created' => 3.2,
                'filter' => [
                    0 => 'safari',
                    1 => 'directory',
                    2 => 'menu',
                    3 => 'location'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            301 => [
                'name' => 'Caret Square Outlined Down',
                'id' => 'caret-square-o-down',
                'unicode' => 'f150',
                'created' => 3.2,
                'aliases' => [
                    0 => 'toggle-down'
                ],
                'filter' => [
                    0 => 'more',
                    1 => 'dropdown',
                    2 => 'menu'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            302 => [
                'name' => 'Caret Square Outlined Up',
                'id' => 'caret-square-o-up',
                'unicode' => 'f151',
                'created' => 3.2,
                'aliases' => [
                    0 => 'toggle-up'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            303 => [
                'name' => 'Caret Square Outlined Right',
                'id' => 'caret-square-o-right',
                'unicode' => 'f152',
                'created' => 3.2,
                'filter' => [
                    0 => 'next',
                    1 => 'forward'
                ],
                'aliases' => [
                    0 => 'toggle-right'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            304 => [
                'name' => 'Euro (EUR)',
                'id' => 'eur',
                'unicode' => 'f153',
                'created' => 3.2,
                'aliases' => [
                    0 => 'euro'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            305 => [
                'name' => 'GBP',
                'id' => 'gbp',
                'unicode' => 'f154',
                'created' => 3.2,
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            306 => [
                'name' => 'US Dollar',
                'id' => 'usd',
                'unicode' => 'f155',
                'created' => 3.2,
                'aliases' => [
                    0 => 'dollar'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            307 => [
                'name' => 'Indian Rupee (INR)',
                'id' => 'inr',
                'unicode' => 'f156',
                'created' => 3.2,
                'aliases' => [
                    0 => 'rupee'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            308 => [
                'name' => 'Japanese Yen (JPY)',
                'id' => 'jpy',
                'unicode' => 'f157',
                'created' => 3.2,
                'aliases' => [
                    0 => 'cny',
                    1 => 'rmb',
                    2 => 'yen'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            309 => [
                'name' => 'Russian Ruble (RUB)',
                'id' => 'rub',
                'unicode' => 'f158',
                'created' => 4.0,
                'aliases' => [
                    0 => 'ruble',
                    1 => 'rouble'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            310 => [
                'name' => 'Korean Won (KRW)',
                'id' => 'krw',
                'unicode' => 'f159',
                'created' => 3.2,
                'aliases' => [
                    0 => 'won'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            311 => [
                'name' => 'Bitcoin (BTC)',
                'id' => 'btc',
                'unicode' => 'f15a',
                'created' => 3.2,
                'aliases' => [
                    0 => 'bitcoin'
                ],
                'categories' => [
                    0 => 'Currency Icons',
                    1 => 'Brand Icons'
                ]
            ],
            312 => [
                'name' => 'File',
                'id' => 'file',
                'unicode' => 'f15b',
                'created' => 3.2,
                'filter' => [
                    0 => 'new',
                    1 => 'page',
                    2 => 'pdf',
                    3 => 'document'
                ],
                'categories' => [
                    0 => 'Text Editor Icons',
                    1 => 'File Type Icons'
                ]
            ],
            313 => [
                'name' => 'File Text',
                'id' => 'file-text',
                'unicode' => 'f15c',
                'created' => 3.2,
                'filter' => [
                    0 => 'new',
                    1 => 'page',
                    2 => 'pdf',
                    3 => 'document'
                ],
                'categories' => [
                    0 => 'Text Editor Icons',
                    1 => 'File Type Icons'
                ]
            ],
            314 => [
                'name' => 'Sort Alpha Ascending',
                'id' => 'sort-alpha-asc',
                'unicode' => 'f15d',
                'created' => 3.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            315 => [
                'name' => 'Sort Alpha Descending',
                'id' => 'sort-alpha-desc',
                'unicode' => 'f15e',
                'created' => 3.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            316 => [
                'name' => 'Sort Amount Ascending',
                'id' => 'sort-amount-asc',
                'unicode' => 'f160',
                'created' => 3.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            317 => [
                'name' => 'Sort Amount Descending',
                'id' => 'sort-amount-desc',
                'unicode' => 'f161',
                'created' => 3.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            318 => [
                'name' => 'Sort Numeric Ascending',
                'id' => 'sort-numeric-asc',
                'unicode' => 'f162',
                'created' => 3.2,
                'filter' => [
                    0 => 'numbers'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            319 => [
                'name' => 'Sort Numeric Descending',
                'id' => 'sort-numeric-desc',
                'unicode' => 'f163',
                'created' => 3.2,
                'filter' => [
                    0 => 'numbers'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            320 => [
                'name' => 'thumbs-up',
                'id' => 'thumbs-up',
                'unicode' => 'f164',
                'created' => 3.2,
                'filter' => [
                    0 => 'like',
                    1 => 'favorite',
                    2 => 'approve',
                    3 => 'agree',
                    4 => 'hand'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            321 => [
                'name' => 'thumbs-down',
                'id' => 'thumbs-down',
                'unicode' => 'f165',
                'created' => 3.2,
                'filter' => [
                    0 => 'dislike',
                    1 => 'disapprove',
                    2 => 'disagree',
                    3 => 'hand'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            322 => [
                'name' => 'YouTube Square',
                'id' => 'youtube-square',
                'unicode' => 'f166',
                'created' => 3.2,
                'filter' => [
                    0 => 'video',
                    1 => 'film'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            323 => [
                'name' => 'YouTube',
                'id' => 'youtube',
                'unicode' => 'f167',
                'created' => 3.2,
                'filter' => [
                    0 => 'video',
                    1 => 'film'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            324 => [
                'name' => 'Xing',
                'id' => 'xing',
                'unicode' => 'f168',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            325 => [
                'name' => 'Xing Square',
                'id' => 'xing-square',
                'unicode' => 'f169',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            326 => [
                'name' => 'YouTube Play',
                'id' => 'youtube-play',
                'unicode' => 'f16a',
                'created' => 3.2,
                'filter' => [
                    0 => 'start',
                    1 => 'playing'
                ],
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Video Player Icons'
                ]
            ],
            327 => [
                'name' => 'Dropbox',
                'id' => 'dropbox',
                'unicode' => 'f16b',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            328 => [
                'name' => 'Stack Overflow',
                'id' => 'stack-overflow',
                'unicode' => 'f16c',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            329 => [
                'name' => 'Instagram',
                'id' => 'instagram',
                'unicode' => 'f16d',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            330 => [
                'name' => 'Flickr',
                'id' => 'flickr',
                'unicode' => 'f16e',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            331 => [
                'name' => 'App.net',
                'id' => 'adn',
                'unicode' => 'f170',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            332 => [
                'name' => 'Bitbucket',
                'id' => 'bitbucket',
                'unicode' => 'f171',
                'created' => 3.2,
                'filter' => [
                    0 => 'git'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            333 => [
                'name' => 'Bitbucket Square',
                'id' => 'bitbucket-square',
                'unicode' => 'f172',
                'created' => 3.2,
                'filter' => [
                    0 => 'git'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            334 => [
                'name' => 'Tumblr',
                'id' => 'tumblr',
                'unicode' => 'f173',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            335 => [
                'name' => 'Tumblr Square',
                'id' => 'tumblr-square',
                'unicode' => 'f174',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            336 => [
                'name' => 'Long Arrow Down',
                'id' => 'long-arrow-down',
                'unicode' => 'f175',
                'created' => 3.2,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            337 => [
                'name' => 'Long Arrow Up',
                'id' => 'long-arrow-up',
                'unicode' => 'f176',
                'created' => 3.2,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            338 => [
                'name' => 'Long Arrow Left',
                'id' => 'long-arrow-left',
                'unicode' => 'f177',
                'created' => 3.2,
                'filter' => [
                    0 => 'previous',
                    1 => 'back'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            339 => [
                'name' => 'Long Arrow Right',
                'id' => 'long-arrow-right',
                'unicode' => 'f178',
                'created' => 3.2,
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            340 => [
                'name' => 'Apple',
                'id' => 'apple',
                'unicode' => 'f179',
                'created' => 3.2,
                'filter' => [
                    0 => 'osx',
                    1 => 'food'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            341 => [
                'name' => 'Windows',
                'id' => 'windows',
                'unicode' => 'f17a',
                'created' => 3.2,
                'filter' => [
                    0 => 'microsoft'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            342 => [
                'name' => 'Android',
                'id' => 'android',
                'unicode' => 'f17b',
                'created' => 3.2,
                'filter' => [
                    0 => 'robot'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            343 => [
                'name' => 'Linux',
                'id' => 'linux',
                'unicode' => 'f17c',
                'created' => 3.2,
                'filter' => [
                    0 => 'tux'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            344 => [
                'name' => 'Dribbble',
                'id' => 'dribbble',
                'unicode' => 'f17d',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            345 => [
                'name' => 'Skype',
                'id' => 'skype',
                'unicode' => 'f17e',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            346 => [
                'name' => 'Foursquare',
                'id' => 'foursquare',
                'unicode' => 'f180',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            347 => [
                'name' => 'Trello',
                'id' => 'trello',
                'unicode' => 'f181',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            348 => [
                'name' => 'Female',
                'id' => 'female',
                'unicode' => 'f182',
                'created' => 3.2,
                'filter' => [
                    0 => 'woman',
                    1 => 'user',
                    2 => 'person',
                    3 => 'profile'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            349 => [
                'name' => 'Male',
                'id' => 'male',
                'unicode' => 'f183',
                'created' => 3.2,
                'filter' => [
                    0 => 'man',
                    1 => 'user',
                    2 => 'person',
                    3 => 'profile'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            350 => [
                'name' => 'Gratipay (Gittip)',
                'id' => 'gratipay',
                'unicode' => 'f184',
                'created' => 3.2,
                'aliases' => [
                    0 => 'gittip'
                ],
                'filter' => [
                    0 => 'heart',
                    1 => 'like',
                    2 => 'favorite',
                    3 => 'love'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            351 => [
                'name' => 'Sun Outlined',
                'id' => 'sun-o',
                'unicode' => 'f185',
                'created' => 3.2,
                'filter' => [
                    0 => 'weather',
                    1 => 'contrast',
                    2 => 'lighter',
                    3 => 'brighten',
                    4 => 'day'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            352 => [
                'name' => 'Moon Outlined',
                'id' => 'moon-o',
                'unicode' => 'f186',
                'created' => 3.2,
                'filter' => [
                    0 => 'night',
                    1 => 'darker',
                    2 => 'contrast'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            353 => [
                'name' => 'Archive',
                'id' => 'archive',
                'unicode' => 'f187',
                'created' => 3.2,
                'filter' => [
                    0 => 'box',
                    1 => 'storage'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            354 => [
                'name' => 'Bug',
                'id' => 'bug',
                'unicode' => 'f188',
                'created' => 3.2,
                'filter' => [
                    0 => 'report',
                    1 => 'insect'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            355 => [
                'name' => 'VK',
                'id' => 'vk',
                'unicode' => 'f189',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            356 => [
                'name' => 'Weibo',
                'id' => 'weibo',
                'unicode' => 'f18a',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            357 => [
                'name' => 'Renren',
                'id' => 'renren',
                'unicode' => 'f18b',
                'created' => 3.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            358 => [
                'name' => 'Pagelines',
                'id' => 'pagelines',
                'unicode' => 'f18c',
                'created' => 4.0,
                'filter' => [
                    0 => 'leaf',
                    1 => 'leaves',
                    2 => 'tree',
                    3 => 'plant',
                    4 => 'eco',
                    5 => 'nature'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            359 => [
                'name' => 'Stack Exchange',
                'id' => 'stack-exchange',
                'unicode' => 'f18d',
                'created' => 4.0,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            360 => [
                'name' => 'Arrow Circle Outlined Right',
                'id' => 'arrow-circle-o-right',
                'unicode' => 'f18e',
                'created' => 4.0,
                'filter' => [
                    0 => 'next',
                    1 => 'forward'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            361 => [
                'name' => 'Arrow Circle Outlined Left',
                'id' => 'arrow-circle-o-left',
                'unicode' => 'f190',
                'created' => 4.0,
                'filter' => [
                    0 => 'previous',
                    1 => 'back'
                ],
                'categories' => [
                    0 => 'Directional Icons'
                ]
            ],
            362 => [
                'name' => 'Caret Square Outlined Left',
                'id' => 'caret-square-o-left',
                'unicode' => 'f191',
                'created' => 4.0,
                'filter' => [
                    0 => 'previous',
                    1 => 'back'
                ],
                'aliases' => [
                    0 => 'toggle-left'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Directional Icons'
                ]
            ],
            363 => [
                'name' => 'Dot Circle Outlined',
                'id' => 'dot-circle-o',
                'unicode' => 'f192',
                'created' => 4.0,
                'filter' => [
                    0 => 'target',
                    1 => 'bullseye',
                    2 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            364 => [
                'name' => 'Wheelchair',
                'id' => 'wheelchair',
                'unicode' => 'f193',
                'created' => 4.0,
                'filter' => [
                    0 => 'handicap',
                    1 => 'person'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Medical Icons',
                    2 => 'Transportation Icons',
                    3 => 'Accessibility Icons'
                ]
            ],
            365 => [
                'name' => 'Vimeo Square',
                'id' => 'vimeo-square',
                'unicode' => 'f194',
                'created' => 4.0,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            366 => [
                'name' => 'Turkish Lira (TRY)',
                'id' => 'try',
                'unicode' => 'f195',
                'created' => 4.0,
                'aliases' => [
                    0 => 'turkish-lira'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            367 => [
                'name' => 'Plus Square Outlined',
                'id' => 'plus-square-o',
                'unicode' => 'f196',
                'created' => 4.0,
                'filter' => [
                    0 => 'add',
                    1 => 'new',
                    2 => 'create',
                    3 => 'expand'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Form Control Icons'
                ]
            ],
            368 => [
                'name' => 'Space Shuttle',
                'id' => 'space-shuttle',
                'unicode' => 'f197',
                'created' => 4.1,
                'filter' => NULL,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            369 => [
                'name' => 'Slack Logo',
                'id' => 'slack',
                'unicode' => 'f198',
                'created' => 4.1,
                'filter' => [
                    0 => 'hashtag',
                    1 => 'anchor',
                    2 => 'hash'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            370 => [
                'name' => 'Envelope Square',
                'id' => 'envelope-square',
                'unicode' => 'f199',
                'created' => 4.1,
                'filter' => [
                    0 => 'email',
                    1 => 'e-mail',
                    2 => 'letter',
                    3 => 'support',
                    4 => 'mail',
                    5 => 'message',
                    6 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            371 => [
                'name' => 'WordPress Logo',
                'id' => 'wordpress',
                'unicode' => 'f19a',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            372 => [
                'name' => 'OpenID',
                'id' => 'openid',
                'unicode' => 'f19b',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            373 => [
                'name' => 'University',
                'id' => 'university',
                'unicode' => 'f19c',
                'created' => 4.1,
                'aliases' => [
                    0 => 'institution',
                    1 => 'bank'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            374 => [
                'name' => 'Graduation Cap',
                'id' => 'graduation-cap',
                'unicode' => 'f19d',
                'created' => 4.1,
                'aliases' => [
                    0 => 'mortar-board'
                ],
                'filter' => [
                    0 => 'learning',
                    1 => 'school',
                    2 => 'student'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            375 => [
                'name' => 'Yahoo Logo',
                'id' => 'yahoo',
                'unicode' => 'f19e',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            376 => [
                'name' => 'Google Logo',
                'id' => 'google',
                'unicode' => 'f1a0',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            377 => [
                'name' => 'reddit Logo',
                'id' => 'reddit',
                'unicode' => 'f1a1',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            378 => [
                'name' => 'reddit Square',
                'id' => 'reddit-square',
                'unicode' => 'f1a2',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            379 => [
                'name' => 'StumbleUpon Circle',
                'id' => 'stumbleupon-circle',
                'unicode' => 'f1a3',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            380 => [
                'name' => 'StumbleUpon Logo',
                'id' => 'stumbleupon',
                'unicode' => 'f1a4',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            381 => [
                'name' => 'Delicious Logo',
                'id' => 'delicious',
                'unicode' => 'f1a5',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            382 => [
                'name' => 'Digg Logo',
                'id' => 'digg',
                'unicode' => 'f1a6',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            383 => [
                'name' => 'Pied Piper PP Logo (Old)',
                'id' => 'pied-piper-pp',
                'unicode' => 'f1a7',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            384 => [
                'name' => 'Pied Piper Alternate Logo',
                'id' => 'pied-piper-alt',
                'unicode' => 'f1a8',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            385 => [
                'name' => 'Drupal Logo',
                'id' => 'drupal',
                'unicode' => 'f1a9',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            386 => [
                'name' => 'Joomla Logo',
                'id' => 'joomla',
                'unicode' => 'f1aa',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            387 => [
                'name' => 'Language',
                'id' => 'language',
                'unicode' => 'f1ab',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            388 => [
                'name' => 'Fax',
                'id' => 'fax',
                'unicode' => 'f1ac',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            389 => [
                'name' => 'Building',
                'id' => 'building',
                'unicode' => 'f1ad',
                'created' => 4.1,
                'filter' => [
                    0 => 'work',
                    1 => 'business',
                    2 => 'apartment',
                    3 => 'office',
                    4 => 'company'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            390 => [
                'name' => 'Child',
                'id' => 'child',
                'unicode' => 'f1ae',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            391 => [
                'name' => 'Paw',
                'id' => 'paw',
                'unicode' => 'f1b0',
                'created' => 4.1,
                'filter' => [
                    0 => 'pet'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            392 => [
                'name' => 'spoon',
                'id' => 'spoon',
                'unicode' => 'f1b1',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            393 => [
                'name' => 'Cube',
                'id' => 'cube',
                'unicode' => 'f1b2',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            394 => [
                'name' => 'Cubes',
                'id' => 'cubes',
                'unicode' => 'f1b3',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            395 => [
                'name' => 'Behance',
                'id' => 'behance',
                'unicode' => 'f1b4',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            396 => [
                'name' => 'Behance Square',
                'id' => 'behance-square',
                'unicode' => 'f1b5',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            397 => [
                'name' => 'Steam',
                'id' => 'steam',
                'unicode' => 'f1b6',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            398 => [
                'name' => 'Steam Square',
                'id' => 'steam-square',
                'unicode' => 'f1b7',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            399 => [
                'name' => 'Recycle',
                'id' => 'recycle',
                'unicode' => 'f1b8',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            400 => [
                'name' => 'Car',
                'id' => 'car',
                'unicode' => 'f1b9',
                'created' => 4.1,
                'aliases' => [
                    0 => 'automobile'
                ],
                'filter' => [
                    0 => 'vehicle'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            401 => [
                'name' => 'Taxi',
                'id' => 'taxi',
                'unicode' => 'f1ba',
                'created' => 4.1,
                'aliases' => [
                    0 => 'cab'
                ],
                'filter' => [
                    0 => 'vehicle'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            402 => [
                'name' => 'Tree',
                'id' => 'tree',
                'unicode' => 'f1bb',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            403 => [
                'name' => 'Spotify',
                'id' => 'spotify',
                'unicode' => 'f1bc',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            404 => [
                'name' => 'deviantART',
                'id' => 'deviantart',
                'unicode' => 'f1bd',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            405 => [
                'name' => 'SoundCloud',
                'id' => 'soundcloud',
                'unicode' => 'f1be',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            406 => [
                'name' => 'Database',
                'id' => 'database',
                'unicode' => 'f1c0',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            407 => [
                'name' => 'PDF File Outlined',
                'id' => 'file-pdf-o',
                'unicode' => 'f1c1',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            408 => [
                'name' => 'Word File Outlined',
                'id' => 'file-word-o',
                'unicode' => 'f1c2',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            409 => [
                'name' => 'Excel File Outlined',
                'id' => 'file-excel-o',
                'unicode' => 'f1c3',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            410 => [
                'name' => 'Powerpoint File Outlined',
                'id' => 'file-powerpoint-o',
                'unicode' => 'f1c4',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            411 => [
                'name' => 'Image File Outlined',
                'id' => 'file-image-o',
                'unicode' => 'f1c5',
                'created' => 4.1,
                'aliases' => [
                    0 => 'file-photo-o',
                    1 => 'file-picture-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            412 => [
                'name' => 'Archive File Outlined',
                'id' => 'file-archive-o',
                'unicode' => 'f1c6',
                'created' => 4.1,
                'aliases' => [
                    0 => 'file-zip-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            413 => [
                'name' => 'Audio File Outlined',
                'id' => 'file-audio-o',
                'unicode' => 'f1c7',
                'created' => 4.1,
                'aliases' => [
                    0 => 'file-sound-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            414 => [
                'name' => 'Video File Outlined',
                'id' => 'file-video-o',
                'unicode' => 'f1c8',
                'created' => 4.1,
                'aliases' => [
                    0 => 'file-movie-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            415 => [
                'name' => 'Code File Outlined',
                'id' => 'file-code-o',
                'unicode' => 'f1c9',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'File Type Icons'
                ]
            ],
            416 => [
                'name' => 'Vine',
                'id' => 'vine',
                'unicode' => 'f1ca',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            417 => [
                'name' => 'Codepen',
                'id' => 'codepen',
                'unicode' => 'f1cb',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            418 => [
                'name' => 'jsFiddle',
                'id' => 'jsfiddle',
                'unicode' => 'f1cc',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            419 => [
                'name' => 'Life Ring',
                'id' => 'life-ring',
                'unicode' => 'f1cd',
                'created' => 4.1,
                'aliases' => [
                    0 => 'life-bouy',
                    1 => 'life-buoy',
                    2 => 'life-saver',
                    3 => 'support'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            420 => [
                'name' => 'Circle Outlined Notched',
                'id' => 'circle-o-notch',
                'unicode' => 'f1ce',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Spinner Icons'
                ]
            ],
            421 => [
                'name' => 'Rebel Alliance',
                'id' => 'rebel',
                'unicode' => 'f1d0',
                'created' => 4.1,
                'aliases' => [
                    0 => 'ra',
                    1 => 'resistance'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            422 => [
                'name' => 'Galactic Empire',
                'id' => 'empire',
                'unicode' => 'f1d1',
                'created' => 4.1,
                'aliases' => [
                    0 => 'ge'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            423 => [
                'name' => 'Git Square',
                'id' => 'git-square',
                'unicode' => 'f1d2',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            424 => [
                'name' => 'Git',
                'id' => 'git',
                'unicode' => 'f1d3',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            425 => [
                'name' => 'Hacker News',
                'id' => 'hacker-news',
                'unicode' => 'f1d4',
                'created' => 4.1,
                'aliases' => [
                    0 => 'y-combinator-square',
                    1 => 'yc-square'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            426 => [
                'name' => 'Tencent Weibo',
                'id' => 'tencent-weibo',
                'unicode' => 'f1d5',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            427 => [
                'name' => 'QQ',
                'id' => 'qq',
                'unicode' => 'f1d6',
                'created' => 4.1,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            428 => [
                'name' => 'Weixin (WeChat)',
                'id' => 'weixin',
                'unicode' => 'f1d7',
                'created' => 4.1,
                'aliases' => [
                    0 => 'wechat'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            429 => [
                'name' => 'Paper Plane',
                'id' => 'paper-plane',
                'unicode' => 'f1d8',
                'created' => 4.1,
                'aliases' => [
                    0 => 'send'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            430 => [
                'name' => 'Paper Plane Outlined',
                'id' => 'paper-plane-o',
                'unicode' => 'f1d9',
                'created' => 4.1,
                'aliases' => [
                    0 => 'send-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            431 => [
                'name' => 'History',
                'id' => 'history',
                'unicode' => 'f1da',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            432 => [
                'name' => 'Circle Outlined Thin',
                'id' => 'circle-thin',
                'unicode' => 'f1db',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            433 => [
                'name' => 'header',
                'id' => 'header',
                'unicode' => 'f1dc',
                'created' => 4.1,
                'filter' => [
                    0 => 'heading'
                ],
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            434 => [
                'name' => 'paragraph',
                'id' => 'paragraph',
                'unicode' => 'f1dd',
                'created' => 4.1,
                'categories' => [
                    0 => 'Text Editor Icons'
                ]
            ],
            435 => [
                'name' => 'Sliders',
                'id' => 'sliders',
                'unicode' => 'f1de',
                'created' => 4.1,
                'filter' => [
                    0 => 'settings'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            436 => [
                'name' => 'Share Alt',
                'id' => 'share-alt',
                'unicode' => 'f1e0',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Brand Icons'
                ]
            ],
            437 => [
                'name' => 'Share Alt Square',
                'id' => 'share-alt-square',
                'unicode' => 'f1e1',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Brand Icons'
                ]
            ],
            438 => [
                'name' => 'Bomb',
                'id' => 'bomb',
                'unicode' => 'f1e2',
                'created' => 4.1,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            439 => [
                'name' => 'Futbol Outlined',
                'id' => 'futbol-o',
                'unicode' => 'f1e3',
                'created' => 4.2,
                'aliases' => [
                    0 => 'soccer-ball-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            440 => [
                'name' => 'TTY',
                'id' => 'tty',
                'unicode' => 'f1e4',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            441 => [
                'name' => 'Binoculars',
                'id' => 'binoculars',
                'unicode' => 'f1e5',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            442 => [
                'name' => 'Plug',
                'id' => 'plug',
                'unicode' => 'f1e6',
                'created' => 4.2,
                'filter' => [
                    0 => 'power',
                    1 => 'connect'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            443 => [
                'name' => 'Slideshare',
                'id' => 'slideshare',
                'unicode' => 'f1e7',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            444 => [
                'name' => 'Twitch',
                'id' => 'twitch',
                'unicode' => 'f1e8',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            445 => [
                'name' => 'Yelp',
                'id' => 'yelp',
                'unicode' => 'f1e9',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            446 => [
                'name' => 'Newspaper Outlined',
                'id' => 'newspaper-o',
                'unicode' => 'f1ea',
                'created' => 4.2,
                'filter' => [
                    0 => 'press'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            447 => [
                'name' => 'WiFi',
                'id' => 'wifi',
                'unicode' => 'f1eb',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            448 => [
                'name' => 'Calculator',
                'id' => 'calculator',
                'unicode' => 'f1ec',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            449 => [
                'name' => 'Paypal',
                'id' => 'paypal',
                'unicode' => 'f1ed',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            450 => [
                'name' => 'Google Wallet',
                'id' => 'google-wallet',
                'unicode' => 'f1ee',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            451 => [
                'name' => 'Visa Credit Card',
                'id' => 'cc-visa',
                'unicode' => 'f1f0',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            452 => [
                'name' => 'MasterCard Credit Card',
                'id' => 'cc-mastercard',
                'unicode' => 'f1f1',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            453 => [
                'name' => 'Discover Credit Card',
                'id' => 'cc-discover',
                'unicode' => 'f1f2',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            454 => [
                'name' => 'American Express Credit Card',
                'id' => 'cc-amex',
                'unicode' => 'f1f3',
                'created' => 4.2,
                'filter' => [
                    0 => 'amex'
                ],
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            455 => [
                'name' => 'Paypal Credit Card',
                'id' => 'cc-paypal',
                'unicode' => 'f1f4',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            456 => [
                'name' => 'Stripe Credit Card',
                'id' => 'cc-stripe',
                'unicode' => 'f1f5',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            457 => [
                'name' => 'Bell Slash',
                'id' => 'bell-slash',
                'unicode' => 'f1f6',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            458 => [
                'name' => 'Bell Slash Outlined',
                'id' => 'bell-slash-o',
                'unicode' => 'f1f7',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            459 => [
                'name' => 'Trash',
                'id' => 'trash',
                'unicode' => 'f1f8',
                'created' => 4.2,
                'filter' => [
                    0 => 'garbage',
                    1 => 'delete',
                    2 => 'remove',
                    3 => 'hide'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            460 => [
                'name' => 'Copyright',
                'id' => 'copyright',
                'unicode' => 'f1f9',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            461 => [
                'name' => 'At',
                'id' => 'at',
                'unicode' => 'f1fa',
                'created' => 4.2,
                'filter' => [
                    0 => 'email',
                    1 => 'e-mail'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            462 => [
                'name' => 'Eyedropper',
                'id' => 'eyedropper',
                'unicode' => 'f1fb',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            463 => [
                'name' => 'Paint Brush',
                'id' => 'paint-brush',
                'unicode' => 'f1fc',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            464 => [
                'name' => 'Birthday Cake',
                'id' => 'birthday-cake',
                'unicode' => 'f1fd',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            465 => [
                'name' => 'Area Chart',
                'id' => 'area-chart',
                'unicode' => 'f1fe',
                'created' => 4.2,
                'filter' => [
                    0 => 'graph',
                    1 => 'analytics'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Chart Icons'
                ]
            ],
            466 => [
                'name' => 'Pie Chart',
                'id' => 'pie-chart',
                'unicode' => 'f200',
                'created' => 4.2,
                'filter' => [
                    0 => 'graph',
                    1 => 'analytics'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Chart Icons'
                ]
            ],
            467 => [
                'name' => 'Line Chart',
                'id' => 'line-chart',
                'unicode' => 'f201',
                'created' => 4.2,
                'filter' => [
                    0 => 'graph',
                    1 => 'analytics'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Chart Icons'
                ]
            ],
            468 => [
                'name' => 'last.fm',
                'id' => 'lastfm',
                'unicode' => 'f202',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            469 => [
                'name' => 'last.fm Square',
                'id' => 'lastfm-square',
                'unicode' => 'f203',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            470 => [
                'name' => 'Toggle Off',
                'id' => 'toggle-off',
                'unicode' => 'f204',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            471 => [
                'name' => 'Toggle On',
                'id' => 'toggle-on',
                'unicode' => 'f205',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            472 => [
                'name' => 'Bicycle',
                'id' => 'bicycle',
                'unicode' => 'f206',
                'created' => 4.2,
                'filter' => [
                    0 => 'vehicle',
                    1 => 'bike'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            473 => [
                'name' => 'Bus',
                'id' => 'bus',
                'unicode' => 'f207',
                'created' => 4.2,
                'filter' => [
                    0 => 'vehicle'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            474 => [
                'name' => 'ioxhost',
                'id' => 'ioxhost',
                'unicode' => 'f208',
                'created' => 4.2,
                'url' => 'ioxhost.co.uk',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            475 => [
                'name' => 'AngelList',
                'id' => 'angellist',
                'unicode' => 'f209',
                'created' => 4.2,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            476 => [
                'name' => 'Closed Captions',
                'id' => 'cc',
                'unicode' => 'f20a',
                'created' => 4.2,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            477 => [
                'name' => 'Shekel (ILS)',
                'id' => 'ils',
                'unicode' => 'f20b',
                'created' => 4.2,
                'aliases' => [
                    0 => 'shekel',
                    1 => 'sheqel'
                ],
                'categories' => [
                    0 => 'Currency Icons'
                ]
            ],
            478 => [
                'name' => 'meanpath',
                'id' => 'meanpath',
                'unicode' => 'f20c',
                'created' => 4.2,
                'url' => 'meanpath.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            479 => [
                'name' => 'BuySellAds',
                'id' => 'buysellads',
                'unicode' => 'f20d',
                'created' => 4.3,
                'url' => 'buysellads.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            480 => [
                'name' => 'Connect Develop',
                'id' => 'connectdevelop',
                'unicode' => 'f20e',
                'created' => 4.3,
                'url' => 'connectdevelop.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            481 => [
                'name' => 'DashCube',
                'id' => 'dashcube',
                'unicode' => 'f210',
                'created' => 4.3,
                'url' => 'dashcube.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            482 => [
                'name' => 'Forumbee',
                'id' => 'forumbee',
                'unicode' => 'f211',
                'created' => 4.3,
                'url' => 'forumbee.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            483 => [
                'name' => 'Leanpub',
                'id' => 'leanpub',
                'unicode' => 'f212',
                'created' => 4.3,
                'url' => 'leanpub.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            484 => [
                'name' => 'Sellsy',
                'id' => 'sellsy',
                'unicode' => 'f213',
                'created' => 4.3,
                'url' => 'sellsy.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            485 => [
                'name' => 'Shirts in Bulk',
                'id' => 'shirtsinbulk',
                'unicode' => 'f214',
                'created' => 4.3,
                'url' => 'shirtsinbulk.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            486 => [
                'name' => 'SimplyBuilt',
                'id' => 'simplybuilt',
                'unicode' => 'f215',
                'created' => 4.3,
                'url' => 'simplybuilt.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            487 => [
                'name' => 'skyatlas',
                'id' => 'skyatlas',
                'unicode' => 'f216',
                'created' => 4.3,
                'url' => 'skyatlas.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            488 => [
                'name' => 'Add to Shopping Cart',
                'id' => 'cart-plus',
                'unicode' => 'f217',
                'created' => 4.3,
                'filter' => [
                    0 => 'add',
                    1 => 'shopping'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            489 => [
                'name' => 'Shopping Cart Arrow Down',
                'id' => 'cart-arrow-down',
                'unicode' => 'f218',
                'created' => 4.3,
                'filter' => [
                    0 => 'shopping'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            490 => [
                'name' => 'Diamond',
                'id' => 'diamond',
                'unicode' => 'f219',
                'created' => 4.3,
                'filter' => [
                    0 => 'gem',
                    1 => 'gemstone'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            491 => [
                'name' => 'Ship',
                'id' => 'ship',
                'unicode' => 'f21a',
                'created' => 4.3,
                'filter' => [
                    0 => 'boat',
                    1 => 'sea'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            492 => [
                'name' => 'User Secret',
                'id' => 'user-secret',
                'unicode' => 'f21b',
                'created' => 4.3,
                'filter' => [
                    0 => 'whisper',
                    1 => 'spy',
                    2 => 'incognito',
                    3 => 'privacy'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            493 => [
                'name' => 'Motorcycle',
                'id' => 'motorcycle',
                'unicode' => 'f21c',
                'created' => 4.3,
                'filter' => [
                    0 => 'vehicle',
                    1 => 'bike'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Transportation Icons'
                ]
            ],
            494 => [
                'name' => 'Street View',
                'id' => 'street-view',
                'unicode' => 'f21d',
                'created' => 4.3,
                'filter' => [
                    0 => 'map'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            495 => [
                'name' => 'Heartbeat',
                'id' => 'heartbeat',
                'unicode' => 'f21e',
                'created' => 4.3,
                'filter' => [
                    0 => 'ekg'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Medical Icons'
                ]
            ],
            496 => [
                'name' => 'Venus',
                'id' => 'venus',
                'unicode' => 'f221',
                'created' => 4.3,
                'filter' => [
                    0 => 'female'
                ],
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            497 => [
                'name' => 'Mars',
                'id' => 'mars',
                'unicode' => 'f222',
                'created' => 4.3,
                'filter' => [
                    0 => 'male'
                ],
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            498 => [
                'name' => 'Mercury',
                'id' => 'mercury',
                'unicode' => 'f223',
                'created' => 4.3,
                'filter' => [
                    0 => 'transgender'
                ],
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            499 => [
                'name' => 'Transgender',
                'id' => 'transgender',
                'unicode' => 'f224',
                'created' => 4.3,
                'aliases' => [
                    0 => 'intersex'
                ],
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            500 => [
                'name' => 'Transgender Alt',
                'id' => 'transgender-alt',
                'unicode' => 'f225',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            501 => [
                'name' => 'Venus Double',
                'id' => 'venus-double',
                'unicode' => 'f226',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            502 => [
                'name' => 'Mars Double',
                'id' => 'mars-double',
                'unicode' => 'f227',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            503 => [
                'name' => 'Venus Mars',
                'id' => 'venus-mars',
                'unicode' => 'f228',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            504 => [
                'name' => 'Mars Stroke',
                'id' => 'mars-stroke',
                'unicode' => 'f229',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            505 => [
                'name' => 'Mars Stroke Vertical',
                'id' => 'mars-stroke-v',
                'unicode' => 'f22a',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            506 => [
                'name' => 'Mars Stroke Horizontal',
                'id' => 'mars-stroke-h',
                'unicode' => 'f22b',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            507 => [
                'name' => 'Neuter',
                'id' => 'neuter',
                'unicode' => 'f22c',
                'created' => 4.3,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            508 => [
                'name' => 'Genderless',
                'id' => 'genderless',
                'unicode' => 'f22d',
                'created' => 4.4,
                'categories' => [
                    0 => 'Gender Icons'
                ]
            ],
            509 => [
                'name' => 'Facebook Official',
                'id' => 'facebook-official',
                'unicode' => 'f230',
                'created' => 4.3,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            510 => [
                'name' => 'Pinterest P',
                'id' => 'pinterest-p',
                'unicode' => 'f231',
                'created' => 4.3,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            511 => [
                'name' => 'What\'s App',
                'id' => 'whatsapp',
                'unicode' => 'f232',
                'created' => 4.3,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            512 => [
                'name' => 'Server',
                'id' => 'server',
                'unicode' => 'f233',
                'created' => 4.3,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            513 => [
                'name' => 'Add User',
                'id' => 'user-plus',
                'unicode' => 'f234',
                'created' => 4.3,
                'filter' => [
                    0 => 'sign up',
                    1 => 'signup'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            514 => [
                'name' => 'Remove User',
                'id' => 'user-times',
                'unicode' => 'f235',
                'created' => 4.3,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            515 => [
                'name' => 'Bed',
                'id' => 'bed',
                'unicode' => 'f236',
                'created' => 4.3,
                'filter' => [
                    0 => 'travel'
                ],
                'aliases' => [
                    0 => 'hotel'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            516 => [
                'name' => 'Viacoin',
                'id' => 'viacoin',
                'unicode' => 'f237',
                'created' => 4.3,
                'url' => 'viacoin.org',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            517 => [
                'name' => 'Train',
                'id' => 'train',
                'unicode' => 'f238',
                'created' => 4.3,
                'categories' => [
                    0 => 'Transportation Icons'
                ]
            ],
            518 => [
                'name' => 'Subway',
                'id' => 'subway',
                'unicode' => 'f239',
                'created' => 4.3,
                'categories' => [
                    0 => 'Transportation Icons'
                ]
            ],
            519 => [
                'name' => 'Medium',
                'id' => 'medium',
                'unicode' => 'f23a',
                'created' => 4.3,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            520 => [
                'name' => 'Y Combinator',
                'id' => 'y-combinator',
                'unicode' => 'f23b',
                'created' => 4.4,
                'aliases' => [
                    0 => 'yc'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            521 => [
                'name' => 'Optin Monster',
                'id' => 'optin-monster',
                'unicode' => 'f23c',
                'created' => 4.4,
                'url' => 'optinmonster.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            522 => [
                'name' => 'OpenCart',
                'id' => 'opencart',
                'unicode' => 'f23d',
                'created' => 4.4,
                'url' => 'opencart.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            523 => [
                'name' => 'ExpeditedSSL',
                'id' => 'expeditedssl',
                'unicode' => 'f23e',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            524 => [
                'name' => 'Battery Full',
                'id' => 'battery-full',
                'unicode' => 'f240',
                'created' => 4.4,
                'aliases' => [
                    0 => 'battery-4',
                    1 => 'battery'
                ],
                'filter' => [
                    0 => 'power'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            525 => [
                'name' => 'Battery 3/4 Full',
                'id' => 'battery-three-quarters',
                'unicode' => 'f241',
                'created' => 4.4,
                'aliases' => [
                    0 => 'battery-3'
                ],
                'filter' => [
                    0 => 'power'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            526 => [
                'name' => 'Battery 1/2 Full',
                'id' => 'battery-half',
                'unicode' => 'f242',
                'created' => 4.4,
                'aliases' => [
                    0 => 'battery-2'
                ],
                'filter' => [
                    0 => 'power'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            527 => [
                'name' => 'Battery 1/4 Full',
                'id' => 'battery-quarter',
                'unicode' => 'f243',
                'created' => 4.4,
                'aliases' => [
                    0 => 'battery-1'
                ],
                'filter' => [
                    0 => 'power'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            528 => [
                'name' => 'Battery Empty',
                'id' => 'battery-empty',
                'unicode' => 'f244',
                'created' => 4.4,
                'aliases' => [
                    0 => 'battery-0'
                ],
                'filter' => [
                    0 => 'power'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            529 => [
                'name' => 'Mouse Pointer',
                'id' => 'mouse-pointer',
                'unicode' => 'f245',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            530 => [
                'name' => 'I Beam Cursor',
                'id' => 'i-cursor',
                'unicode' => 'f246',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            531 => [
                'name' => 'Object Group',
                'id' => 'object-group',
                'unicode' => 'f247',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            532 => [
                'name' => 'Object Ungroup',
                'id' => 'object-ungroup',
                'unicode' => 'f248',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            533 => [
                'name' => 'Sticky Note',
                'id' => 'sticky-note',
                'unicode' => 'f249',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            534 => [
                'name' => 'Sticky Note Outlined',
                'id' => 'sticky-note-o',
                'unicode' => 'f24a',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            535 => [
                'name' => 'JCB Credit Card',
                'id' => 'cc-jcb',
                'unicode' => 'f24b',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            536 => [
                'name' => 'Diner\'s Club Credit Card',
                'id' => 'cc-diners-club',
                'unicode' => 'f24c',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons',
                    1 => 'Payment Icons'
                ]
            ],
            537 => [
                'name' => 'Clone',
                'id' => 'clone',
                'unicode' => 'f24d',
                'created' => 4.4,
                'filter' => [
                    0 => 'copy'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            538 => [
                'name' => 'Balance Scale',
                'id' => 'balance-scale',
                'unicode' => 'f24e',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            539 => [
                'name' => 'Hourglass Outlined',
                'id' => 'hourglass-o',
                'unicode' => 'f250',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            540 => [
                'name' => 'Hourglass Start',
                'id' => 'hourglass-start',
                'unicode' => 'f251',
                'created' => 4.4,
                'aliases' => [
                    0 => 'hourglass-1'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            541 => [
                'name' => 'Hourglass Half',
                'id' => 'hourglass-half',
                'unicode' => 'f252',
                'created' => 4.4,
                'aliases' => [
                    0 => 'hourglass-2'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            542 => [
                'name' => 'Hourglass End',
                'id' => 'hourglass-end',
                'unicode' => 'f253',
                'created' => 4.4,
                'aliases' => [
                    0 => 'hourglass-3'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            543 => [
                'name' => 'Hourglass',
                'id' => 'hourglass',
                'unicode' => 'f254',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            544 => [
                'name' => 'Rock (Hand)',
                'id' => 'hand-rock-o',
                'unicode' => 'f255',
                'created' => 4.4,
                'aliases' => [
                    0 => 'hand-grab-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            545 => [
                'name' => 'Paper (Hand)',
                'id' => 'hand-paper-o',
                'unicode' => 'f256',
                'created' => 4.4,
                'aliases' => [
                    0 => 'hand-stop-o'
                ],
                'filter' => [
                    0 => 'stop'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            546 => [
                'name' => 'Scissors (Hand)',
                'id' => 'hand-scissors-o',
                'unicode' => 'f257',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            547 => [
                'name' => 'Lizard (Hand)',
                'id' => 'hand-lizard-o',
                'unicode' => 'f258',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            548 => [
                'name' => 'Spock (Hand)',
                'id' => 'hand-spock-o',
                'unicode' => 'f259',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            549 => [
                'name' => 'Hand Pointer',
                'id' => 'hand-pointer-o',
                'unicode' => 'f25a',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            550 => [
                'name' => 'Hand Peace',
                'id' => 'hand-peace-o',
                'unicode' => 'f25b',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Hand Icons'
                ]
            ],
            551 => [
                'name' => 'Trademark',
                'id' => 'trademark',
                'unicode' => 'f25c',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            552 => [
                'name' => 'Registered Trademark',
                'id' => 'registered',
                'unicode' => 'f25d',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            553 => [
                'name' => 'Creative Commons',
                'id' => 'creative-commons',
                'unicode' => 'f25e',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            554 => [
                'name' => 'GG Currency',
                'id' => 'gg',
                'unicode' => 'f260',
                'created' => 4.4,
                'categories' => [
                    0 => 'Currency Icons',
                    1 => 'Brand Icons'
                ]
            ],
            555 => [
                'name' => 'GG Currency Circle',
                'id' => 'gg-circle',
                'unicode' => 'f261',
                'created' => 4.4,
                'categories' => [
                    0 => 'Currency Icons',
                    1 => 'Brand Icons'
                ]
            ],
            556 => [
                'name' => 'TripAdvisor',
                'id' => 'tripadvisor',
                'unicode' => 'f262',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            557 => [
                'name' => 'Odnoklassniki',
                'id' => 'odnoklassniki',
                'unicode' => 'f263',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            558 => [
                'name' => 'Odnoklassniki Square',
                'id' => 'odnoklassniki-square',
                'unicode' => 'f264',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            559 => [
                'name' => 'Get Pocket',
                'id' => 'get-pocket',
                'unicode' => 'f265',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            560 => [
                'name' => 'Wikipedia W',
                'id' => 'wikipedia-w',
                'unicode' => 'f266',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            561 => [
                'name' => 'Safari',
                'id' => 'safari',
                'unicode' => 'f267',
                'created' => 4.4,
                'filter' => [
                    0 => 'browser'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            562 => [
                'name' => 'Chrome',
                'id' => 'chrome',
                'unicode' => 'f268',
                'created' => 4.4,
                'filter' => [
                    0 => 'browser'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            563 => [
                'name' => 'Firefox',
                'id' => 'firefox',
                'unicode' => 'f269',
                'created' => 4.4,
                'filter' => [
                    0 => 'browser'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            564 => [
                'name' => 'Opera',
                'id' => 'opera',
                'unicode' => 'f26a',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            565 => [
                'name' => 'Internet-explorer',
                'id' => 'internet-explorer',
                'unicode' => 'f26b',
                'created' => 4.4,
                'filter' => [
                    0 => 'browser',
                    1 => 'ie'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            566 => [
                'name' => 'Television',
                'id' => 'television',
                'unicode' => 'f26c',
                'created' => 4.4,
                'aliases' => [
                    0 => 'tv'
                ],
                'filter' => [
                    0 => 'display',
                    1 => 'computer',
                    2 => 'monitor'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            567 => [
                'name' => 'Contao',
                'id' => 'contao',
                'unicode' => 'f26d',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            568 => [
                'name' => '500px',
                'id' => '500px',
                'unicode' => 'f26e',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            569 => [
                'name' => 'Amazon',
                'id' => 'amazon',
                'unicode' => 'f270',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            570 => [
                'name' => 'Calendar Plus Outlined',
                'id' => 'calendar-plus-o',
                'unicode' => 'f271',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            571 => [
                'name' => 'Calendar Minus Outlined',
                'id' => 'calendar-minus-o',
                'unicode' => 'f272',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            572 => [
                'name' => 'Calendar Times Outlined',
                'id' => 'calendar-times-o',
                'unicode' => 'f273',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            573 => [
                'name' => 'Calendar Check Outlined',
                'id' => 'calendar-check-o',
                'unicode' => 'f274',
                'created' => 4.4,
                'filter' => [
                    0 => 'ok'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            574 => [
                'name' => 'Industry',
                'id' => 'industry',
                'unicode' => 'f275',
                'created' => 4.4,
                'filter' => [
                    0 => 'factory'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            575 => [
                'name' => 'Map Pin',
                'id' => 'map-pin',
                'unicode' => 'f276',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            576 => [
                'name' => 'Map Signs',
                'id' => 'map-signs',
                'unicode' => 'f277',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            577 => [
                'name' => 'Map Outlined',
                'id' => 'map-o',
                'unicode' => 'f278',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            578 => [
                'name' => 'Map',
                'id' => 'map',
                'unicode' => 'f279',
                'created' => 4.4,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            579 => [
                'name' => 'Commenting',
                'id' => 'commenting',
                'unicode' => 'f27a',
                'created' => 4.4,
                'filter' => [
                    0 => 'speech',
                    1 => 'notification',
                    2 => 'note',
                    3 => 'chat',
                    4 => 'bubble',
                    5 => 'feedback',
                    6 => 'message',
                    7 => 'texting',
                    8 => 'sms',
                    9 => 'conversation'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            580 => [
                'name' => 'Commenting Outlined',
                'id' => 'commenting-o',
                'unicode' => 'f27b',
                'created' => 4.4,
                'filter' => [
                    0 => 'speech',
                    1 => 'notification',
                    2 => 'note',
                    3 => 'chat',
                    4 => 'bubble',
                    5 => 'feedback',
                    6 => 'message',
                    7 => 'texting',
                    8 => 'sms',
                    9 => 'conversation'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            581 => [
                'name' => 'Houzz',
                'id' => 'houzz',
                'unicode' => 'f27c',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            582 => [
                'name' => 'Vimeo',
                'id' => 'vimeo',
                'unicode' => 'f27d',
                'created' => 4.4,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            583 => [
                'name' => 'Font Awesome Black Tie',
                'id' => 'black-tie',
                'unicode' => 'f27e',
                'created' => 4.4,
                'url' => 'blacktie.io',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            584 => [
                'name' => 'Fonticons',
                'id' => 'fonticons',
                'unicode' => 'f280',
                'created' => 4.4,
                'url' => 'fonticons.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            585 => [
                'name' => 'reddit Alien',
                'id' => 'reddit-alien',
                'unicode' => 'f281',
                'created' => 4.5,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            586 => [
                'name' => 'Edge Browser',
                'id' => 'edge',
                'unicode' => 'f282',
                'created' => 4.5,
                'filter' => [
                    0 => 'browser',
                    1 => 'ie'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            587 => [
                'name' => 'Credit Card',
                'id' => 'credit-card-alt',
                'unicode' => 'f283',
                'created' => 4.5,
                'filter' => [
                    0 => 'money',
                    1 => 'buy',
                    2 => 'debit',
                    3 => 'checkout',
                    4 => 'purchase',
                    5 => 'payment',
                    6 => 'credit card'
                ],
                'categories' => [
                    0 => 'Payment Icons',
                    1 => 'Web Application Icons'
                ]
            ],
            588 => [
                'name' => 'Codie Pie',
                'id' => 'codiepie',
                'unicode' => 'f284',
                'created' => 4.5,
                'url' => 'codiepie.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            589 => [
                'name' => 'MODX',
                'id' => 'modx',
                'unicode' => 'f285',
                'created' => 4.5,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            590 => [
                'name' => 'Fort Awesome',
                'id' => 'fort-awesome',
                'unicode' => 'f286',
                'created' => 4.5,
                'url' => 'fortawesome.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            591 => [
                'name' => 'USB',
                'id' => 'usb',
                'unicode' => 'f287',
                'created' => 4.5,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            592 => [
                'name' => 'Product Hunt',
                'id' => 'product-hunt',
                'unicode' => 'f288',
                'created' => 4.5,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            593 => [
                'name' => 'Mixcloud',
                'id' => 'mixcloud',
                'unicode' => 'f289',
                'created' => 4.5,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            594 => [
                'name' => 'Scribd',
                'id' => 'scribd',
                'unicode' => 'f28a',
                'created' => 4.5,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            595 => [
                'name' => 'Pause Circle',
                'id' => 'pause-circle',
                'unicode' => 'f28b',
                'created' => 4.5,
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            596 => [
                'name' => 'Pause Circle Outlined',
                'id' => 'pause-circle-o',
                'unicode' => 'f28c',
                'created' => 4.5,
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            597 => [
                'name' => 'Stop Circle',
                'id' => 'stop-circle',
                'unicode' => 'f28d',
                'created' => 4.5,
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            598 => [
                'name' => 'Stop Circle Outlined',
                'id' => 'stop-circle-o',
                'unicode' => 'f28e',
                'created' => 4.5,
                'categories' => [
                    0 => 'Video Player Icons'
                ]
            ],
            599 => [
                'name' => 'Shopping Bag',
                'id' => 'shopping-bag',
                'unicode' => 'f290',
                'created' => 4.5,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            600 => [
                'name' => 'Shopping Basket',
                'id' => 'shopping-basket',
                'unicode' => 'f291',
                'created' => 4.5,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            601 => [
                'name' => 'Hashtag',
                'id' => 'hashtag',
                'unicode' => 'f292',
                'created' => 4.5,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            602 => [
                'name' => 'Bluetooth',
                'id' => 'bluetooth',
                'unicode' => 'f293',
                'created' => 4.5,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Brand Icons'
                ]
            ],
            603 => [
                'name' => 'Bluetooth',
                'id' => 'bluetooth-b',
                'unicode' => 'f294',
                'created' => 4.5,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Brand Icons'
                ]
            ],
            604 => [
                'name' => 'Percent',
                'id' => 'percent',
                'unicode' => 'f295',
                'created' => 4.5,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            605 => [
                'name' => 'GitLab',
                'id' => 'gitlab',
                'unicode' => 'f296',
                'created' => 4.6,
                'url' => 'gitlab.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            606 => [
                'name' => 'WPBeginner',
                'id' => 'wpbeginner',
                'unicode' => 'f297',
                'created' => 4.6,
                'url' => 'wpbeginner.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            607 => [
                'name' => 'WPForms',
                'id' => 'wpforms',
                'unicode' => 'f298',
                'created' => 4.6,
                'url' => 'wpforms.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            608 => [
                'name' => 'Envira Gallery',
                'id' => 'envira',
                'unicode' => 'f299',
                'created' => 4.6,
                'url' => 'enviragallery.com',
                'filter' => [
                    0 => 'leaf'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            609 => [
                'name' => 'Universal Access',
                'id' => 'universal-access',
                'unicode' => 'f29a',
                'created' => 4.6,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            610 => [
                'name' => 'Wheelchair Alt',
                'id' => 'wheelchair-alt',
                'unicode' => 'f29b',
                'created' => 4.6,
                'filter' => [
                    0 => 'handicap',
                    1 => 'person'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Medical Icons',
                    2 => 'Transportation Icons',
                    3 => 'Accessibility Icons'
                ]
            ],
            611 => [
                'name' => 'Question Circle Outlined',
                'id' => 'question-circle-o',
                'unicode' => 'f29c',
                'created' => 4.6,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            612 => [
                'name' => 'Blind',
                'id' => 'blind',
                'unicode' => 'f29d',
                'created' => 4.6,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            613 => [
                'name' => 'Audio Description',
                'id' => 'audio-description',
                'unicode' => 'f29e',
                'created' => 4.6,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            614 => [
                'name' => 'Volume Control Phone',
                'id' => 'volume-control-phone',
                'unicode' => 'f2a0',
                'created' => 4.6,
                'filter' => [
                    0 => 'telephone'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            615 => [
                'name' => 'Braille',
                'id' => 'braille',
                'unicode' => 'f2a1',
                'created' => 4.6,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            616 => [
                'name' => 'Assistive Listening Systems',
                'id' => 'assistive-listening-systems',
                'unicode' => 'f2a2',
                'created' => 4.6,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            617 => [
                'name' => 'American Sign Language Interpreting',
                'id' => 'american-sign-language-interpreting',
                'unicode' => 'f2a3',
                'created' => 4.6,
                'aliases' => [
                    0 => 'asl-interpreting'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            618 => [
                'name' => 'Deaf',
                'id' => 'deaf',
                'unicode' => 'f2a4',
                'created' => 4.6,
                'aliases' => [
                    0 => 'deafness',
                    1 => 'hard-of-hearing'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            619 => [
                'name' => 'Glide',
                'id' => 'glide',
                'unicode' => 'f2a5',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            620 => [
                'name' => 'Glide G',
                'id' => 'glide-g',
                'unicode' => 'f2a6',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            621 => [
                'name' => 'Sign Language',
                'id' => 'sign-language',
                'unicode' => 'f2a7',
                'created' => 4.6,
                'aliases' => [
                    0 => 'signing'
                ],
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            622 => [
                'name' => 'Low Vision',
                'id' => 'low-vision',
                'unicode' => 'f2a8',
                'created' => 4.6,
                'categories' => [
                    0 => 'Web Application Icons',
                    1 => 'Accessibility Icons'
                ]
            ],
            623 => [
                'name' => 'Viadeo',
                'id' => 'viadeo',
                'unicode' => 'f2a9',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            624 => [
                'name' => 'Viadeo Square',
                'id' => 'viadeo-square',
                'unicode' => 'f2aa',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            625 => [
                'name' => 'Snapchat',
                'id' => 'snapchat',
                'unicode' => 'f2ab',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            626 => [
                'name' => 'Snapchat Ghost',
                'id' => 'snapchat-ghost',
                'unicode' => 'f2ac',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            627 => [
                'name' => 'Snapchat Square',
                'id' => 'snapchat-square',
                'unicode' => 'f2ad',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            628 => [
                'name' => 'Pied Piper Logo',
                'id' => 'pied-piper',
                'unicode' => 'f2ae',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            629 => [
                'name' => 'First Order',
                'id' => 'first-order',
                'unicode' => 'f2b0',
                'created' => 4.6,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            630 => [
                'name' => 'Yoast',
                'id' => 'yoast',
                'unicode' => 'f2b1',
                'created' => 4.6,
                'url' => 'yoast.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            631 => [
                'name' => 'ThemeIsle',
                'id' => 'themeisle',
                'unicode' => 'f2b2',
                'created' => 4.6,
                'url' => 'themeisle.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            632 => [
                'name' => 'Google Plus Official',
                'id' => 'google-plus-official',
                'unicode' => 'f2b3',
                'created' => 4.6,
                'aliases' => [
                    0 => 'google-plus-circle'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            633 => [
                'name' => 'Font Awesome',
                'id' => 'font-awesome',
                'unicode' => 'f2b4',
                'created' => 4.6,
                'aliases' => [
                    0 => 'fa'
                ],
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            634 => [
                'name' => 'Handshake Outlined',
                'id' => 'handshake-o',
                'unicode' => 'f2b5',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            635 => [
                'name' => 'Envelope Open',
                'id' => 'envelope-open',
                'unicode' => 'f2b6',
                'created' => 4.7,
                'filter' => [
                    0 => 'email',
                    1 => 'e-mail',
                    2 => 'letter',
                    3 => 'support',
                    4 => 'mail',
                    5 => 'message',
                    6 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            636 => [
                'name' => 'Envelope Open Outlined',
                'id' => 'envelope-open-o',
                'unicode' => 'f2b7',
                'created' => 4.7,
                'filter' => [
                    0 => 'email',
                    1 => 'e-mail',
                    2 => 'letter',
                    3 => 'support',
                    4 => 'mail',
                    5 => 'message',
                    6 => 'notification'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            637 => [
                'name' => 'Linode',
                'id' => 'linode',
                'unicode' => 'f2b8',
                'created' => 4.7,
                'url' => 'linode.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            638 => [
                'name' => 'Address Book',
                'id' => 'address-book',
                'unicode' => 'f2b9',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            639 => [
                'name' => 'Address Book Outlined',
                'id' => 'address-book-o',
                'unicode' => 'f2ba',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            640 => [
                'name' => 'Address Card',
                'id' => 'address-card',
                'unicode' => 'f2bb',
                'created' => 4.7,
                'aliases' => [
                    0 => 'vcard'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            641 => [
                'name' => 'Address Card Outlined',
                'id' => 'address-card-o',
                'unicode' => 'f2bc',
                'created' => 4.7,
                'aliases' => [
                    0 => 'vcard-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            642 => [
                'name' => 'User Circle',
                'id' => 'user-circle',
                'unicode' => 'f2bd',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            643 => [
                'name' => 'User Circle Outlined',
                'id' => 'user-circle-o',
                'unicode' => 'f2be',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            644 => [
                'name' => 'User Outlined',
                'id' => 'user-o',
                'unicode' => 'f2c0',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            645 => [
                'name' => 'Identification Badge',
                'id' => 'id-badge',
                'unicode' => 'f2c1',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            646 => [
                'name' => 'Identification Card',
                'id' => 'id-card',
                'unicode' => 'f2c2',
                'created' => 4.7,
                'aliases' => [
                    0 => 'drivers-license'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            647 => [
                'name' => 'Identification Card Outlined',
                'id' => 'id-card-o',
                'unicode' => 'f2c3',
                'created' => 4.7,
                'aliases' => [
                    0 => 'drivers-license-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            648 => [
                'name' => 'Quora',
                'id' => 'quora',
                'unicode' => 'f2c4',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            649 => [
                'name' => 'Free Code Camp',
                'id' => 'free-code-camp',
                'unicode' => 'f2c5',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            650 => [
                'name' => 'Telegram',
                'id' => 'telegram',
                'unicode' => 'f2c6',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            651 => [
                'name' => 'Thermometer Full',
                'id' => 'thermometer-full',
                'unicode' => 'f2c7',
                'created' => 4.7,
                'aliases' => [
                    0 => 'thermometer-4',
                    1 => 'thermometer'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            652 => [
                'name' => 'Thermometer 3/4 Full',
                'id' => 'thermometer-three-quarters',
                'unicode' => 'f2c8',
                'created' => 4.7,
                'aliases' => [
                    0 => 'thermometer-3'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            653 => [
                'name' => 'Thermometer 1/2 Full',
                'id' => 'thermometer-half',
                'unicode' => 'f2c9',
                'created' => 4.7,
                'aliases' => [
                    0 => 'thermometer-2'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            654 => [
                'name' => 'Thermometer 1/4 Full',
                'id' => 'thermometer-quarter',
                'unicode' => 'f2ca',
                'created' => 4.7,
                'aliases' => [
                    0 => 'thermometer-1'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            655 => [
                'name' => 'Thermometer Empty',
                'id' => 'thermometer-empty',
                'unicode' => 'f2cb',
                'created' => 4.7,
                'aliases' => [
                    0 => 'thermometer-0'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            656 => [
                'name' => 'Shower',
                'id' => 'shower',
                'unicode' => 'f2cc',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            657 => [
                'name' => 'Bath',
                'id' => 'bath',
                'unicode' => 'f2cd',
                'created' => 4.7,
                'aliases' => [
                    0 => 'bathtub',
                    1 => 's15'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            658 => [
                'name' => 'Podcast',
                'id' => 'podcast',
                'unicode' => 'f2ce',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            659 => [
                'name' => 'Window Maximize',
                'id' => 'window-maximize',
                'unicode' => 'f2d0',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            660 => [
                'name' => 'Window Minimize',
                'id' => 'window-minimize',
                'unicode' => 'f2d1',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            661 => [
                'name' => 'Window Restore',
                'id' => 'window-restore',
                'unicode' => 'f2d2',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            662 => [
                'name' => 'Window Close',
                'id' => 'window-close',
                'unicode' => 'f2d3',
                'created' => 4.7,
                'aliases' => [
                    0 => 'times-rectangle'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            663 => [
                'name' => 'Window Close Outline',
                'id' => 'window-close-o',
                'unicode' => 'f2d4',
                'created' => 4.7,
                'aliases' => [
                    0 => 'times-rectangle-o'
                ],
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            664 => [
                'name' => 'Bandcamp',
                'id' => 'bandcamp',
                'unicode' => 'f2d5',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            665 => [
                'name' => 'Grav',
                'id' => 'grav',
                'unicode' => 'f2d6',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            666 => [
                'name' => 'Etsy',
                'id' => 'etsy',
                'unicode' => 'f2d7',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            667 => [
                'name' => 'IMDB',
                'id' => 'imdb',
                'unicode' => 'f2d8',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            668 => [
                'name' => 'Ravelry',
                'id' => 'ravelry',
                'unicode' => 'f2d9',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            669 => [
                'name' => 'Eercast',
                'id' => 'eercast',
                'unicode' => 'f2da',
                'created' => 4.7,
                'url' => 'eercast.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            670 => [
                'name' => 'Microchip',
                'id' => 'microchip',
                'unicode' => 'f2db',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            671 => [
                'name' => 'Snowflake Outlined',
                'id' => 'snowflake-o',
                'unicode' => 'f2dc',
                'created' => 4.7,
                'categories' => [
                    0 => 'Web Application Icons'
                ]
            ],
            672 => [
                'name' => 'Superpowers',
                'id' => 'superpowers',
                'unicode' => 'f2dd',
                'created' => 4.7,
                'url' => 'superpowers.io',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            673 => [
                'name' => 'WPExplorer',
                'id' => 'wpexplorer',
                'unicode' => 'f2de',
                'created' => 4.7,
                'url' => 'wpexplorer.com',
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ],
            674 => [
                'name' => 'Meetup',
                'id' => 'meetup',
                'unicode' => 'f2e0',
                'created' => 4.7,
                'categories' => [
                    0 => 'Brand Icons'
                ]
            ]
        ]
    ]
];
