<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/admin/languages/ru.yaml',
    'modified' => 1730089692,
    'size' => 104407,
    'data' => [
        'PLUGIN_ADMIN' => [
            'ADMIN_NOSCRIPT_MSG' => 'Пожалуйста, включите JavaScript в вашем браузере.',
            'ADMIN_BETA_MSG' => 'Это бета-релиз! Используете его в продакшене на свой страх и риск...',
            'ADMIN_REPORT_ISSUE' => 'Нашли ошибку? Пожалуйста, сообщите об этом на GitHub.',
            'EMAIL_FOOTER' => '<a href="https://getgrav.org">Работает на Grav</a> - Современная CMS на файлах',
            'LOGIN_BTN' => 'Логин',
            'LOGIN_BTN_FORGOT' => 'Не помню',
            'LOGIN_BTN_RESET' => 'Сбросить пароль',
            'LOGIN_BTN_SEND_INSTRUCTIONS' => 'Отправить инструкции по сбросу',
            'LOGIN_BTN_CLEAR' => 'Очистить форму',
            'LOGIN_BTN_CREATE_USER' => 'Создать пользователя',
            'LOGIN_LOGGED_IN' => 'Вы успешно вошли в систему',
            'LOGIN_FAILED' => 'Не удалось войти',
            'LOGGED_OUT' => 'Вы вышли из системы',
            'RESET_NEW_PASSWORD' => 'Пожалуйста, введите новый пароль &hellip;',
            'RESET_LINK_EXPIRED' => 'Время ссылки для сброса истекло, пожалуйста, повторите попытку',
            'RESET_PASSWORD_RESET' => 'Пароль был сброшен',
            'RESET_INVALID_LINK' => 'Неверная ссылка сброса пароля, пожалуйста, повторите попытку',
            'FORGOT_INSTRUCTIONS_SENT_VIA_EMAIL' => 'Инструкции для сброса пароля были высланы на ваш адрес электронной почты',
            'FORGOT_FAILED_TO_EMAIL' => 'Не удалось отправить инструкции по электронной почте, пожалуйста, повторите попытку позже',
            'FORGOT_CANNOT_RESET_EMAIL_NO_EMAIL' => 'Невозможно сбросить пароль для %s, не указан имейл',
            'FORGOT_USERNAME_DOES_NOT_EXIST' => 'Пользователь с логином <b>%s</b> не существует',
            'FORGOT_EMAIL_NOT_CONFIGURED' => 'Не удаётся сбросить пароль. Этот сайт не настроен для отправки электронной почты',
            'FORGOT_EMAIL_SUBJECT' => '%s Запрос на восстановление пароля',
            'FORGOT_EMAIL_BODY' => '<h1>Сброс пароля</h1><p>%1$s,</p><p>Был отправлен запрос на <b>%4$s</b> для сброса пароля.</p><p><br /><a href="%2$s" class="btn-primary">Нажмите эту кнопку, чтобы сбросить свой пароль</a><br /><br /></p><p>Или скопируйте следующий URL в адресную строку вашего браузера:</p> <p>%2$s</p><p><br />С уважением,<br /><br />%3$s</p>',
            'MANAGE_PAGES' => 'Управление страницами',
            'PAGES' => 'Страницы',
            'PLUGINS' => 'Плагины',
            'PLUGIN' => 'Плагин',
            'THEMES' => 'Темы',
            'LOGOUT' => 'Выход',
            'BACK' => 'Назад',
            'NEXT' => 'Далее',
            'PREVIOUS' => 'Ранее',
            'ADD_PAGE' => 'Добавить страницу',
            'MOVE' => 'Перенести',
            'DELETE' => 'Удалить',
            'UNSET' => 'Сброс',
            'VIEW' => 'Просмотр',
            'SAVE' => 'Сохранить',
            'NORMAL' => 'Обычный',
            'EXPERT' => 'Экспертный',
            'EXPAND_ALL' => 'Развернуть все',
            'COLLAPSE_ALL' => 'Свернуть все',
            'ERROR' => 'Ошибка',
            'CLOSE' => 'Закрыть',
            'CANCEL' => 'Отменить',
            'CONTINUE' => 'Продолжить',
            'CONFIRM' => 'Подтвердить',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_TITLE' => 'Требуется подтверждение',
            'MODAL_CHANGED_DETECTED_TITLE' => 'Обнаружены изменения',
            'MODAL_CHANGED_DETECTED_DESC' => 'У вас есть несохраненные изменения. Вы уверены, что хотите выйти без сохранения?',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_TITLE' => 'Требуется подтверждение',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_DESC' => 'Хотите удалить этот файл? Это действие не может быть отменено.',
            'MODAL_UPDATE_GRAV_CONFIRMATION_REQUIRED_DESC' => 'Вы собираетесь обновить Grav до последней версии. Хотите продолжить?',
            'ADD_FILTERS' => 'Добавить фильтры',
            'SEARCH_PAGES' => 'Поиск страниц',
            'VERSION' => 'Версия',
            'WAS_MADE_WITH' => 'сделано с',
            'BY' => 'От',
            'UPDATE_THEME' => 'Обновить тему',
            'UPDATE_PLUGIN' => 'Обновить плагин',
            'OF_THIS_THEME_IS_NOW_AVAILABLE' => 'этой темы теперь доступно',
            'OF_THIS_PLUGIN_IS_NOW_AVAILABLE' => 'этого плагина теперь доступно',
            'AUTHOR' => 'Автор',
            'HOMEPAGE' => 'Домашняя страница',
            'DEMO' => 'Демо',
            'BUG_TRACKER' => 'Баг-трекер',
            'KEYWORDS' => 'Ключевые слова',
            'LICENSE' => 'Лицензия',
            'DESCRIPTION' => 'Описание',
            'README' => 'Инструкция',
            'DOCS' => 'Документация',
            'REMOVE_THEME' => 'Удалить тему',
            'INSTALL_THEME' => 'Установить тему',
            'THEME' => 'Тема',
            'BACK_TO_THEMES' => 'Назад к темам',
            'BACK_TO_PLUGINS' => 'Назад к плагинам',
            'CHECK_FOR_UPDATES' => 'Проверить обновления',
            'ADD' => 'Добавить',
            'CLEAR_CACHE' => 'Очистить кэш',
            'CLEAR_CACHE_ALL_CACHE' => 'Весь кэш',
            'CLEAR_CACHE_ASSETS_ONLY' => 'Только ресурсы',
            'CLEAR_CACHE_IMAGES_ONLY' => 'Только изображения',
            'CLEAR_CACHE_CACHE_ONLY' => 'Только кэш',
            'CLEAR_CACHE_TMP_ONLY' => 'Только временные файлы',
            'UPDATES_AVAILABLE' => 'Доступны обновления',
            'DAYS' => 'Дни',
            'UPDATE' => 'Обновление',
            'BACKUP' => 'Резервная копия',
            'BACKUPS' => 'Резервные копии',
            'BACKUP_NOW' => 'Создать резервную копию',
            'BACKUPS_STATS' => 'Статистика резервного копирования',
            'BACKUPS_HISTORY' => 'История резервного копирования',
            'BACKUPS_PURGE_CONFIG' => 'Очистить конфигурацию резервного копирования',
            'BACKUPS_PROFILES' => 'Профили резервного копирования',
            'BACKUPS_COUNT' => 'Количество резервных копий',
            'BACKUPS_PROFILES_COUNT' => 'Количество профилей',
            'BACKUPS_TOTAL_SIZE' => 'Занято пространства',
            'BACKUPS_NEWEST' => 'Самая новая резервная копия',
            'BACKUPS_OLDEST' => 'Самая старая резервная копия',
            'BACKUPS_PURGE' => 'Очистить',
            'BACKUPS_NOT_GENERATED' => 'Резервные копии еще не созданы...',
            'BACKUPS_PURGE_NUMBER' => 'Использовано %s из %s слотов резервных копий',
            'BACKUPS_PURGE_TIME' => 'Резервной копии осталось %s дней',
            'BACKUPS_PURGE_SPACE' => 'Используется %s из %s',
            'BACKUP_DELETED' => 'Резервная копия успешно удалена',
            'BACKUP_NOT_FOUND' => 'Резервная копия не найдена',
            'BACKUP_DATE' => 'Дата резервной копии',
            'STATISTICS' => 'Статистика',
            'VIEWS_STATISTICS' => 'Статистика просмотров страниц',
            'TODAY' => 'Сегодня',
            'WEEK' => 'Неделя',
            'MONTH' => 'Месяц',
            'LATEST_PAGE_UPDATES' => 'Последние обновлённые страницы',
            'MAINTENANCE' => 'Техническое обслуживание',
            'UPDATED' => 'Обновлено',
            'MON' => 'Пн',
            'TUE' => 'Вт',
            'WED' => 'Ср',
            'THU' => 'Чт',
            'FRI' => 'Пт',
            'SAT' => 'Сб',
            'SUN' => 'Вс',
            'COPY' => 'Копировать',
            'EDIT' => 'Редактировать',
            'CREATE' => 'Создать',
            'GRAV_ADMIN' => 'Админ панель Grav',
            'GRAV_OFFICIAL_PLUGIN' => 'Официальный плагин Grav',
            'GRAV_OFFICIAL_THEME' => 'Официальная тема Grav',
            'PLUGIN_SYMBOLICALLY_LINKED' => 'Этот плагин символически связан. Обновления не будут найдены.',
            'THEME_SYMBOLICALLY_LINKED' => 'Эта тема символически связана. Обновления не будут найдены.',
            'REMOVE_PLUGIN' => 'Удалить плагин',
            'INSTALL_PLUGIN' => 'Установить плагин',
            'AVAILABLE' => 'Доступен',
            'INSTALLED' => 'Установлен',
            'INSTALL' => 'Установить',
            'ACTIVE_THEME' => 'Активная тема',
            'SWITCHING_TO' => 'Переключить',
            'SWITCHING_TO_DESCRIPTION' => 'При переключении на другую тему нет никакой гарантии того, что поддерживаются все макеты страниц. Возможно, это приведет к ошибкам при попытке загрузки указанных страниц.',
            'SWITCHING_TO_CONFIRMATION' => 'Хотите продолжить и переключиться на тему',
            'CREATE_NEW_USER' => 'Создать нового пользователя',
            'REMOVE_USER' => 'Удалить пользователя',
            'ACCESS_DENIED' => 'Доступ запрещен',
            'ACCOUNT_NOT_ADMIN' => 'ваша учетная запись не имеет прав администратора',
            'PHP_INFO' => 'Информация о PHP',
            'INSTALLER' => 'Установщик',
            'AVAILABLE_THEMES' => 'Доступные темы',
            'AVAILABLE_PLUGINS' => 'Доступные плагины',
            'INSTALLED_THEMES' => 'Установленные темы',
            'INSTALLED_PLUGINS' => 'Установленные плагины',
            'BROWSE_ERROR_LOGS' => 'Просмотр журналов ошибок',
            'SITE' => 'Сайт',
            'INFO' => 'Информация',
            'SYSTEM' => 'Система',
            'USER' => 'Пользователь',
            'ADD_ACCOUNT' => 'Добавить учетную запись',
            'SWITCH_LANGUAGE' => 'Переключить язык',
            'SUCCESSFULLY_ENABLED_PLUGIN' => 'Плагин успешно включён',
            'SUCCESSFULLY_DISABLED_PLUGIN' => 'Плагин успешно отключён',
            'SUCCESSFULLY_CHANGED_THEME' => 'Успешно изменена тема по умолчанию',
            'INSTALLATION_FAILED' => 'Ошибка установки',
            'INSTALLATION_SUCCESSFUL' => 'Установка завершена успешно',
            'UNINSTALL_FAILED' => 'Удаление не удалось',
            'UNINSTALL_SUCCESSFUL' => 'Удаление успешно',
            'SUCCESSFULLY_SAVED' => 'Сохранение успешно',
            'SUCCESSFULLY_COPIED' => 'Копирование успешно',
            'REORDERING_WAS_SUCCESSFUL' => 'Изменение порядка прошло успешно',
            'SUCCESSFULLY_DELETED' => 'Успешно удалено',
            'SUCCESSFULLY_SWITCHED_LANGUAGE' => 'Язык подключён успешно',
            'INSUFFICIENT_PERMISSIONS_FOR_TASK' => 'У вас недостаточно прав доступа для задачи',
            'CACHE_CLEARED' => 'Кэш очищен',
            'METHOD' => 'Метод',
            'ERROR_CLEARING_CACHE' => 'Ошибка очистки кэша',
            'AN_ERROR_OCCURRED' => 'Произошла ошибка',
            'YOUR_BACKUP_IS_READY_FOR_DOWNLOAD' => 'Резервная копия готова',
            'DOWNLOAD_BACKUP' => 'Скачать',
            'PAGES_FILTERED' => 'Фильтр страниц',
            'NO_PAGE_FOUND' => 'Страница не найдена',
            'INVALID_PARAMETERS' => 'Неверные параметры',
            'NO_FILES_SENT' => 'Файлы не отправлены',
            'EXCEEDED_FILESIZE_LIMIT' => 'Превышен заданный в конфигурации PHP предельный размер файла',
            'EXCEEDED_POSTMAX_LIMIT' => 'Превышено значение post_max_size',
            'UNKNOWN_ERRORS' => 'Неизвестная ошибка',
            'EXCEEDED_GRAV_FILESIZE_LIMIT' => 'Превышен заданный в конфигурации Grav предельный размер файла',
            'UNSUPPORTED_FILE_TYPE' => 'Неподдерживаемый тип файла',
            'FAILED_TO_MOVE_UPLOADED_FILE' => 'Не удалось переместить загруженный файл.',
            'FILE_UPLOADED_SUCCESSFULLY' => 'Файл успешно загружен',
            'FILE_DELETED' => 'Файл удалён',
            'FILE_COULD_NOT_BE_DELETED' => 'Файл не может быть удалён',
            'FILE_NOT_FOUND' => 'Файл не найден',
            'NO_FILE_FOUND' => 'Файлы не найдены',
            'GRAV_WAS_SUCCESSFULLY_UPDATED_TO' => 'Grav успешно обновлён на',
            'GRAV_UPDATE_FAILED' => 'Ошибка обновления Grav',
            'EVERYTHING_UPDATED' => 'Все обновлено',
            'UPDATES_FAILED' => 'Обновление не удалось',
            'AVATAR_BY' => 'Аватар',
            'AVATAR_UPLOAD_OWN' => 'Или загрузите свой...',
            'LAST_BACKUP' => 'Последняя резервная копия',
            'FULL_NAME' => 'Полное имя',
            'USERNAME' => 'Имя пользователя',
            'EMAIL' => 'Имейл',
            'USERNAME_EMAIL' => 'Логин или имейл',
            'PASSWORD' => 'Пароль',
            'PASSWORD_CONFIRM' => 'Подтвердить пароль',
            'TITLE' => 'Заголовок',
            'ACCOUNT' => 'Учетная запись',
            'EMAIL_VALIDATION_MESSAGE' => 'Адрес электронной почты должен быть действительным',
            'PASSWORD_VALIDATION_MESSAGE' => 'Пароль должен содержать хотя бы одну цифру, одну заглавную и строчную букву, и по крайней мере 8 или более символов',
            'LANGUAGE' => 'Язык',
            'LANGUAGE_HELP' => 'Выберите предпочитаемый язык',
            'LANGUAGE_DEBUG_HELP' => 'Включите отладку языков, использующих фильтр |t twig, добавив вокруг них элемент span, который можно стилизовать для диагностики проблем',
            'MEDIA' => 'Медиа',
            'DEFAULTS' => 'По умолчанию',
            'SITE_TITLE' => 'Заголовок сайта',
            'SITE_TITLE_PLACEHOLDER' => 'Название сайта',
            'SITE_TITLE_HELP' => 'Название сайта по умолчанию, часто используется в темах оформления.',
            'SITE_DEFAULT_LANG' => 'Язык по умолчанию',
            'SITE_DEFAULT_LANG_PLACEHOLDER' => 'Язык, используемый по умолчанию в теге <HTML> в шаблонах темы.',
            'SITE_DEFAULT_LANG_HELP' => 'Язык, используемый по умолчанию в теге <HTML> в шаблонах темы.',
            'DEFAULT_AUTHOR' => 'Автор по умолчанию',
            'DEFAULT_AUTHOR_HELP' => 'Имя автора по умолчанию, часто используется в темах оформления или содержимом страниц.',
            'DEFAULT_EMAIL' => 'Email по умолчанию',
            'DEFAULT_EMAIL_HELP' => 'Имейл по умолчанию используется для создания ссылки на почту в темах или на страницах.',
            'TAXONOMY_TYPES' => 'Типы таксономии',
            'TAXONOMY_TYPES_HELP' => 'Типы таксономии должны быть определены здесь, если хотите использовать их на страницах сайта',
            'PAGE_SUMMARY' => 'Анонс',
            'ENABLED' => 'Включено',
            'ENABLED_HELP' => 'Показывать анонс (содержание страницы до разделителя).',
            'YES' => 'Да',
            'NO' => 'Нет',
            'SUMMARY_SIZE' => 'Размер анонса',
            'SUMMARY_SIZE_HELP' => 'Количество символов страницы, выводимых в анонсе',
            'FORMAT' => 'Формат',
            'FORMAT_HELP' => 'Короткий = использовать первое вхождение разделителя или размер; Длинный = разделитель игнорируется',
            'SHORT' => 'Короткий',
            'LONG' => 'Длинный',
            'DELIMITER' => 'Разделитель',
            'DELIMITER_HELP' => 'Разделитель для функции «Читать далее...» (по умолчанию \'===\')',
            'METADATA' => 'Метаданные',
            'METADATA_HELP' => 'По умолчанию значения метаданных, которые будут отображаться на каждой странице, если это не определено у конкретной страницы.',
            'NAME' => 'Имя',
            'CONTENT' => 'Содержимое',
            'SIZE' => 'Размер',
            'ACTION' => 'Действие',
            'REDIRECTS_AND_ROUTES' => 'Перенаправления и маршруты',
            'CUSTOM_REDIRECTS' => 'Пользовательские перенаправления',
            'CUSTOM_REDIRECTS_HELP' => 'Маршрутизация для перенаправления на другие страницы. Действует замена стандартных регулярных выражений',
            'CUSTOM_REDIRECTS_PLACEHOLDER_KEY' => '/your/alias',
            'CUSTOM_REDIRECTS_PLACEHOLDER_VALUE' => '/your/redirect',
            'CUSTOM_ROUTES' => 'Нестандартная маршрутизация',
            'CUSTOM_ROUTES_HELP' => 'маршруты к псевдониму на другие страницы. Доступна замена стандартными регулярными выражениями',
            'CUSTOM_ROUTES_PLACEHOLDER_KEY' => '/your/alias',
            'CUSTOM_ROUTES_PLACEHOLDER_VALUE' => '/your/route',
            'FILE_STREAMS' => 'Файловые потоки',
            'DEFAULT' => 'По умолчанию',
            'PAGE_MEDIA' => 'Вложения',
            'OPTIONS' => 'Опции',
            'PUBLISHED' => 'Опубликовано',
            'PUBLISHED_HELP' => 'По умолчанию страница опубликована, если явно не указано published: false или publish_date в будущем, unpublish_date в прошлом',
            'DATE' => 'Дата',
            'DATE_HELP' => 'Переменная даты позволяет специально установить дату, связанную с этой страницей.',
            'PUBLISHED_DATE' => 'Дата публикации',
            'PUBLISHED_DATE_HELP' => 'Можете указать дату автоматической публикации страницы.',
            'UNPUBLISHED_DATE' => 'Дата снятия с публикации',
            'UNPUBLISHED_DATE_HELP' => 'Можете указать дату автоматического снятия с публикации.',
            'ROBOTS' => 'Роботы',
            'TAXONOMIES' => 'Таксономии',
            'TAXONOMY' => 'Таксономия',
            'ADVANCED' => 'Дополнительно',
            'SETTINGS' => 'Настройки',
            'FOLDER_NUMERIC_PREFIX' => 'Числовой префикс папки',
            'FOLDER_NUMERIC_PREFIX_HELP' => 'Числовой префикс, позволяет упорядочить страницы вручную, и подразумевает видимость страницы',
            'FOLDER_NAME' => 'Имя папки',
            'FOLDER_NAME_HELP' => 'Имя папки, которое будут храниться в файловой системе для этой страницы.',
            'PARENT' => 'Родитель',
            'DEFAULT_OPTION_ROOT' => '- Корень -',
            'DEFAULT_OPTION_SELECT' => '- Выбрать -',
            'DISPLAY_TEMPLATE' => 'Шаблон отображения',
            'DISPLAY_TEMPLATE_HELP' => 'Тип страницы, который определяет, какая ветка шаблона отображает страницу.',
            'ORDERING' => 'Порядок',
            'PAGE_ORDER' => 'Порядок страниц',
            'OVERRIDES' => 'Переопределения',
            'MENU' => 'Меню',
            'MENU_HELP' => 'Строка, которая будет использоваться в меню. Если не задана, будет использоваться заголовок.',
            'SLUG' => 'Псевдоним',
            'SLUG_HELP' => 'Эта переменная позволяет установить псевдоним, часть URL-адреса страницы',
            'SLUG_VALIDATE_MESSAGE' => 'Псевдоним должен состоять только из строчных алфавитно-цифровых символов и дефисов',
            'PROCESS' => 'Обработка',
            'PROCESS_HELP' => 'Регулирует тип обработчика содержимого страниц. Можно установить как глобально, так и отдельно для страницы.',
            'DEFAULT_CHILD_TYPE' => 'Тип вложенного элемента',
            'USE_GLOBAL' => 'Использовать глобальный',
            'ROUTABLE' => 'Маршрутизируемая',
            'ROUTABLE_HELP' => 'Если страница доступна из URL',
            'CACHING' => 'Кэширование',
            'VISIBLE' => 'Видимая',
            'VISIBLE_HELP' => 'Определяет, отображается ли страница в навигации.',
            'DISABLED' => 'Отключено',
            'ITEMS' => 'Элементы',
            'ORDER_BY' => 'Сортировать по',
            'ORDER' => 'Порядок',
            'FOLDER' => 'Папка',
            'ASCENDING' => 'По возрастанию',
            'DESCENDING' => 'По убыванию',
            'PAGE_TITLE' => 'Заголовок страницы',
            'PAGE_TITLE_HELP' => 'Заголовок текущей страницы',
            'PAGE' => 'Страница',
            'FRONTMATTER' => 'Вступление',
            'FILENAME' => 'Имя файла',
            'PARENT_PAGE' => 'Родительская страница',
            'HOME_PAGE' => 'Главная страница',
            'HOME_PAGE_HELP' => 'Страница, которую Grav будет использовать по умолчанию в качестве главной.',
            'DEFAULT_THEME' => 'Тема по умолчанию',
            'DEFAULT_THEME_HELP' => 'Шаблон оформления, используемый по умолчанию.',
            'TIMEZONE' => 'Часовой пояс',
            'TIMEZONE_HELP' => 'Изменить значение часового пояса, используемого сервером по умолчанию.',
            'SHORT_DATE_FORMAT' => 'Краткий формат даты',
            'SHORT_DATE_FORMAT_HELP' => 'Укажите короткий формат даты, который может использоваться в темах оформления.',
            'LONG_DATE_FORMAT' => 'Длинный формат даты',
            'LONG_DATE_FORMAT_HELP' => 'Укажите длинный формат даты, который может использоваться в темах оформления.',
            'DEFAULT_ORDERING' => 'Порядок по умолчанию',
            'DEFAULT_ORDERING_HELP' => 'Страницы в списке будут выводиться с учетом этого порядка, если он не переопределен.',
            'DEFAULT_ORDERING_DEFAULT' => 'По умолчанию - в зависимости от имени папки',
            'DEFAULT_ORDERING_FOLDER' => 'Папка - в зависимости от приставки имени папки',
            'DEFAULT_ORDERING_TITLE' => 'Заголовок - в зависимости от названия поля заголовка',
            'DEFAULT_ORDERING_DATE' => 'Дата - в зависимости от поля даты',
            'DEFAULT_ORDER_DIRECTION' => 'Порядок сортировки по умолчанию',
            'DEFAULT_ORDER_DIRECTION_HELP' => 'Направление сортировки страниц в списке материалов.',
            'DEFAULT_PAGE_COUNT' => 'Количество страниц по умолчанию',
            'DEFAULT_PAGE_COUNT_HELP' => 'Максимальное количество страниц в списке по умолчанию.',
            'DATE_BASED_PUBLISHING' => 'Публикация на основе даты',
            'DATE_BASED_PUBLISHING_HELP' => 'Автоматически (не)публикует посты на основе их даты.',
            'EVENTS' => 'События',
            'EVENTS_HELP' => 'Включить или отключить специфические события. Отключение этих событий может привести к ошибкам в работе плагинов.',
            'REDIRECT_DEFAULT_ROUTE' => 'Перенаправить маршрут по умолчанию',
            'REDIRECT_DEFAULT_ROUTE_HELP' => 'Автоматическое перенаправление страниц согласно маршруту',
            'LANGUAGES' => 'Языки',
            'SUPPORTED' => 'Поддерживаемые языки',
            'SUPPORTED_HELP' => 'Укажите необходимые вам языки через запятую (например \'en,ru,uk\')',
            'SUPPORTED_PLACEHOLDER' => 'напр. en, fr',
            'TRANSLATIONS_FALLBACK' => 'Резервный перевод',
            'TRANSLATIONS_FALLBACK_HELP' => 'Использовать резервный вариант перевода, если активного языка не существует',
            'ACTIVE_LANGUAGE_IN_SESSION' => 'Активный язык в сессии',
            'ACTIVE_LANGUAGE_IN_SESSION_HELP' => 'Хранить активный язык в сессии пользователя',
            'HTTP_HEADERS' => 'HTTP заголовки',
            'EXPIRES' => 'Срок действия',
            'EXPIRES_HELP' => 'Устанавливает заголовок Expires. Значение в секундах.',
            'CACHE_CONTROL' => 'HTTP кэш-контроль',
            'CACHE_CONTROL_HELP' => 'Установите допустимое значение переменной кэш-контроля сервера из списка: `no-cache, no-store, must-revalidate`.',
            'CACHE_CONTROL_PLACEHOLDER' => 'напр. public, max-age=31536000',
            'LAST_MODIFIED' => 'Дата последнего изменения',
            'LAST_MODIFIED_HELP' => 'Устанавливает заголовок последнего изменения, который может помочь оптимизировать кэширование прокси и браузера',
            'ETAG' => 'ETag',
            'ETAG_HELP' => 'Устанавливает заголовок ETag, чтобы помочь определить, когда страница была изменена.',
            'VARY_ACCEPT_ENCODING' => 'Варьировать допустимую кодировку',
            'VARY_ACCEPT_ENCODING_HELP' => 'Установите заголовок `Vary: Accept Encoding`, чтобы избежать проблем при работе с общедоступными прокси-серверами и CDN, которые не распознают наличие заголовка Content-Encoding.',
            'MARKDOWN' => 'Markdown',
            'MARKDOWN_EXTRA' => 'Расширенный Markdown',
            'MARKDOWN_EXTRA_HELP' => 'Включение поддержки Markdown Extra - https://michelf.ca/projects/php-markdown/extra/',
            'MARKDOWN_EXTRA_ESCAPE_FENCES' => 'Экранирование HTML-элементов (синтаксис fences) в Markdown Extra',
            'MARKDOWN_EXTRA_ESCAPE_FENCES_HELP' => 'Избегать HTML-элементов в Markdown Extra для синтаксиса fences',
            'AUTO_LINE_BREAKS' => 'Авто разрывы строк',
            'AUTO_LINE_BREAKS_HELP' => 'Включить поддержку автоматических разрывов строк в markdown',
            'AUTO_URL_LINKS' => 'Авто URL ссылки',
            'AUTO_URL_LINKS_HELP' => 'Включить автоматическое преобразование URL в HTML-код гиперссылки.',
            'ESCAPE_MARKUP' => 'Экранирование разметки',
            'ESCAPE_MARKUP_HELP' => 'Избегать тегов разметки в HTML объектах',
            'CACHING_HELP' => 'Глобальный выключатель для включения/отключения кэширования Grav',
            'CACHE_CHECK_METHOD' => 'Метод проверки кэша',
            'CACHE_CHECK_METHOD_HELP' => 'Выберите метод, который Grav использует для проверки, были ли файлы страниц изменены.',
            'CACHE_DRIVER' => 'Драйвер кэширования',
            'CACHE_DRIVER_HELP' => 'Выберите метод кэширования, который должен использоваться Grav. \'Auto Detect\' - попытается определить лучший метод самостоятельно',
            'CACHE_PREFIX' => 'Префикс кэша',
            'CACHE_PREFIX_HELP' => 'Идентификатор для части ключа Grav. Не изменяйте, если не знаете, к чему это приведет.',
            'CACHE_PREFIX_PLACEHOLDER' => 'Производный от базового URL (переопределите, введя произвольную строку)',
            'CACHE_PURGE_JOB' => 'Запускать запланированную очистку',
            'CACHE_PURGE_JOB_HELP' => 'Посредством планировщика можно периодически очищать старые папки кэша файлов Doctrine',
            'CACHE_CLEAR_JOB' => 'Запускать запланированную очистку',
            'CACHE_CLEAR_JOB_HELP' => 'С помощью планировщика можно периодически очищать кэш Grav',
            'CACHE_JOB_TYPE' => 'Тип работы кэша',
            'CACHE_JOB_TYPE_HELP' => 'Очистить либо с помощью \'стандартной\' очистки кэша папок, либо \'все\' папки',
            'CACHE_PURGE' => 'Очистить старый кэш',
            'LIFETIME' => 'Время жизни',
            'LIFETIME_HELP' => 'Задает время жизни кэша в секундах. 0 = бесконечно.',
            'GZIP_COMPRESSION' => 'Сжатие Gzip',
            'GZIP_COMPRESSION_HELP' => 'Включить сжатие Gzip в Grav для увеличения скорости загрузки страниц.',
            'TWIG_TEMPLATING' => 'Шаблонизация Twig',
            'TWIG_CACHING' => 'Кэширование Twig',
            'TWIG_CACHING_HELP' => 'Управление механизмом кэширования Twig. Оставьте его включённым для обеспечения лучшей производительности.',
            'TWIG_DEBUG' => 'Отладка Twig',
            'TWIG_DEBUG_HELP' => 'Позволяет не загружать расширение Twig Debugger.',
            'DETECT_CHANGES' => 'Обнаружение изменений',
            'DETECT_CHANGES_HELP' => 'Twig автоматически перекомпилирует кэш Twig, если обнаружит какие-либо изменения в шаблонах Twig',
            'AUTOESCAPE_VARIABLES' => 'Экранирование переменных',
            'AUTOESCAPE_VARIABLES_HELP' => 'Автоматическое экранирование всех используемых переменных. Это скорей всего нарушит работу сайта.',
            'ASSETS' => 'Ресурсы',
            'CSS_ASSETS' => 'Активы CSS',
            'CSS_PIPELINE' => 'CSS конвейер',
            'CSS_PIPELINE_HELP' => 'Позволяет объединить несколько CSS файлов в один',
            'CSS_PIPELINE_INCLUDE_EXTERNALS' => 'Включать внешние ресурсы в CSS конвейер',
            'CSS_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'Внешние URL-адреса иногда имеют связанные ссылки на файлы и не могут быть в конвейере',
            'CSS_PIPELINE_BEFORE_EXCLUDES' => 'CSS конвейер рендерит первым',
            'CSS_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Рендер CSS конвейера происходит до остальных CSS связей, которые не включены',
            'CSS_MINIFY' => 'Минимизировать CSS',
            'CSS_MINIFY_HELP' => 'Минимизировать CSS файлы во время объединения',
            'CSS_MINIFY_WINDOWS_OVERRIDE' => 'Минимизировать CSS (переопределение для Windows)',
            'CSS_MINIFY_WINDOWS_OVERRIDE_HELP' => 'Минимизировать переопределение для платформ Windows. Отключено по умолчанию из-за ThreadStackSize.',
            'CSS_REWRITE' => 'Переписать CSS',
            'CSS_REWRITE_HELP' => 'Переписать любые CSS относительные URL-адреса во время объединения',
            'JS_ASSETS' => 'Активы JavaScript',
            'JAVASCRIPT_PIPELINE' => 'Объединение JavaScript',
            'JAVASCRIPT_PIPELINE_HELP' => 'Объединение нескольких JS файлов в один',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS' => 'Включать внешние ресурсы в JS пайплайн',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'Внешние URL-адреса иногда имеют относительные ссылки на файлы и не могут быть в пайплайне',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES' => 'Рендер JS пайплайна сначала',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Рендер JS пайплайна до остальных JS связей, которые не включены',
            'JS_MODULE_ASSETS' => 'Активы модуля JavaScript',
            'JAVASCRIPT_MODULE_PIPELINE' => 'Конвейер модулей JavaScript',
            'JAVASCRIPT_MODULE_PIPELINE_HELP' => 'Конвейер JS-модулей - это объединение нескольких ресурсов JS в один файл',
            'JAVASCRIPT_MODULE_PIPELINE_INCLUDE_EXTERNALS' => 'Включать внешние ресурсы в JS конвейер',
            'JAVASCRIPT_MODULE_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'Внешние URL-адреса иногда содержат ссылки на файлы и не должны использоваться в конвейере',
            'JAVASCRIPT_MODULE_PIPELINE_BEFORE_EXCLUDES' => 'Конвейер JS обрабатывается в первую очередь',
            'JAVASCRIPT_MODULE_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Конвейер JS обрабатывается перед другими JS ссылками',
            'GENERAL_CONFIG' => 'Общая конфигурация активов',
            'JAVASCRIPT_MINIFY' => 'Минимизировать JavaScript',
            'JAVASCRIPT_MINIFY_HELP' => 'Минимизировать в JS во время объединения',
            'ENABLED_TIMESTAMPS_ON_ASSETS' => 'Включить временные метки по активам',
            'ENABLED_TIMESTAMPS_ON_ASSETS_HELP' => 'Включить временные метки активов',
            'ENABLED_SRI_ON_ASSETS' => 'Включить SRI для ассетов',
            'ENABLED_SRI_ON_ASSETS_HELP' => 'Включить SRI ассетов',
            'COLLECTIONS' => 'Коллекции',
            'ERROR_HANDLER' => 'Обработчик ошибок',
            'DISPLAY_ERRORS' => 'Выводить ошибки',
            'DISPLAY_ERRORS_HELP' => 'Отобразить полную цепочку стилей при ошибке на странице',
            'LOG_ERRORS' => 'Логи ошибок',
            'LOG_ERRORS_HELP' => 'Логи ошибок находятся в папке /logs',
            'LOG_HANDLER' => 'Обработчик логов',
            'LOG_HANDLER_HELP' => 'Где хранить логи',
            'SYSLOG_FACILITY' => 'Категория syslog',
            'SYSLOG_FACILITY_HELP' => 'Категория syslog для вывода',
            'DEBUGGER' => 'Отладчик',
            'DEBUGGER_HELP' => 'Включите отладчик Grav и после параметров настройки',
            'DEBUG_TWIG' => 'Отладчик Twig',
            'DEBUG_TWIG_HELP' => 'Включите отладчик шаблонов Twig',
            'SHUTDOWN_CLOSE_CONNECTION' => 'Завершение открытых соединений',
            'SHUTDOWN_CLOSE_CONNECTION_HELP' => 'Закройте соединение до вызова onShutdown(). ложно для отладки',
            'DEFAULT_IMAGE_QUALITY' => 'Качество изображений по умолчанию',
            'DEFAULT_IMAGE_QUALITY_HELP' => 'Качество изображения по умолчанию для использования при повторной выборки или кэширования изображений (85%)',
            'CACHE_ALL' => 'Кэшировать все картинки',
            'CACHE_ALL_HELP' => 'Выполните все изображения через систему кэша Grav, даже если у них нет взаимодействия диском',
            'IMAGES_DEBUG' => 'Отладка водяного знака изображения',
            'IMAGES_DEBUG_HELP' => 'Показать накладку над изображениями с указанием глубины пикселя изображения при работе с сеткой',
            'IMAGES_LOADING' => 'Поведение загрузки изображений',
            'IMAGES_LOADING_HELP' => 'Атрибут loading позволяет браузеру отложить загрузку изображений и фреймов до тех пор, пока при прокрутке страницы эти элементы не окажутся в поле зрения пользователя. Этот атрибут поддерживает три значения: auto, lazy, eager',
            'IMAGES_DECODING' => 'Поведение при декодировании изображений',
            'IMAGES_DECODING_HELP' => 'Атрибут decoding позволяет браузеру отложить декодирование внеэкранных изображений до тех пор, пока пользователь не прокрутит страницу рядом с ними. Декодирование поддерживает три значения: автоматический, синхронный, асинхронный',
            'IMAGES_SEOFRIENDLY' => 'ЧПУ имена изображений',
            'IMAGES_SEOFRIENDLY_HELP' => 'Если включено, сначала отображается имя изображения, а потом небольшой хэш для отражения обрабатываемых операций',
            'UPLOAD_LIMIT' => 'Лимит загрузки файла',
            'UPLOAD_LIMIT_HELP' => 'Установите максимальный размер в байтах для загрузки (0 безлимита)',
            'ENABLE_MEDIA_TIMESTAMP' => 'Включить временные метки на медиа',
            'ENABLE_MEDIA_TIMESTAMP_HELP' => 'Добавляет метку на основе даты последнего изменения каждого элемента в медиа',
            'SESSION' => 'Сессия',
            'SESSION_ENABLED_HELP' => 'Включить поддержку сессий в рамках Grav',
            'SESSION_NAME_HELP' => 'Идентификатор используется для формирования имени сессионной куки',
            'SESSION_UNIQUENESS' => 'Уникальная строка',
            'SESSION_UNIQUENESS_HELP' => 'MD5 хэш корневого пути Grav, например `GRAV_ROOT` (по умолчанию) или на основе случайной строки `security.salt`.',
            'ABSOLUTE_URLS' => 'Абсолютный путь',
            'ABSOLUTE_URLS_HELP' => 'Использование абсолютных или относительных URL-адресов для переменной `base_url`.',
            'PARAMETER_SEPARATOR' => 'Разделитель параметров',
            'PARAMETER_SEPARATOR_HELP' => 'Разделитель для передаваемых параметров, которые могут быть изменены для Apache на Windows.',
            'TASK_COMPLETED' => 'Задача завершена',
            'EVERYTHING_UP_TO_DATE' => 'Всё актуально',
            'UPDATES_ARE_AVAILABLE' => 'обновления доступны',
            'IS_AVAILABLE_FOR_UPDATE' => 'доступно для обновления',
            'IS_NOW_AVAILABLE' => 'теперь доступно',
            'CURRENT' => 'Текущий',
            'UPDATE_GRAV_NOW' => 'Обновить Grav сейчас',
            'GRAV_SYMBOLICALLY_LINKED' => 'Grav символично связаны. Обновление не будет доступно',
            'UPDATING_PLEASE_WAIT' => 'Обновление... Пожалуйста, подождите, загрузка',
            'OF_THIS' => 'этого',
            'OF_YOUR' => 'из ваших',
            'HAVE_AN_UPDATE_AVAILABLE' => 'есть доступное обновление',
            'SAVE_AS' => 'Сохранить как',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_DESC' => 'Хотите удалить эту страницу и все вложенные? Если страница переведена на другие языки, эти переводы будут сохранены и должны быть удалены отдельно. В противном случае папка страницы будет удалена вместе со своими подстраницами. Это действие не может быть отменено.',
            'AND' => 'и',
            'UPDATE_AVAILABLE' => 'Доступно обновление',
            'METADATA_KEY' => 'Ключ (например: \'Ключевые слова\')',
            'METADATA_VALUE' => 'Значение (например: \'Blog, Grav\')',
            'USERNAME_HELP' => 'Имя пользователя должно быть от 3 до 16 символов, в том числе строчных букв, цифр, подчеркивания и дефиса. Прописные буквы, пробелы и специальные символы не допускаются',
            'FULLY_UPDATED' => 'Полностью обновлено',
            'SAVE_LOCATION' => 'Расположение',
            'PAGE_FILE' => 'Шаблон страницы',
            'PAGE_FILE_HELP' => 'Название файла шаблона страницы, используемого для отображения содержимого',
            'NO_USER_ACCOUNTS' => 'Учетные записи не найдены, пожалуйста, создайте первую запись...',
            'NO_USER_EXISTS' => 'Локальный пользователь для этой учетной записи не существует, невозможно сохранить...',
            'REDIRECT_TRAILING_SLASH' => 'Перенаправление с замыкающим слэшем',
            'REDIRECT_TRAILING_SLASH_HELP' => 'Выполните 301 редирект на замыкающий слэш.',
            'DEFAULT_DATE_FORMAT' => 'Формат даты',
            'DEFAULT_DATE_FORMAT_HELP' => 'Формат даты страницы, используемый в Grav. По умолчанию Grav пытается самостоятельно определить правильный формат даты, однако можно задать нужный формат вручную с помощью соответствующего синтаксиса (например: Y-m-d H:i)',
            'DEFAULT_DATE_FORMAT_PLACEHOLDER' => 'Определить автоматически',
            'IGNORE_FILES' => 'Игнорировать файлы',
            'IGNORE_FILES_HELP' => 'Файлы, которые будут проигнорированы при обработке страниц.',
            'IGNORE_FOLDERS' => 'Игнорировать папки',
            'IGNORE_FOLDERS_HELP' => 'Папки, которые будут проигнорированы при обработке страниц.',
            'HIDE_EMPTY_FOLDERS' => 'Скрыть пустые папки',
            'HIDE_EMPTY_FOLDERS_HELP' => 'Если в папке нет .md файла, она должна быть скрыта в навигации, а также недоступна для чтения',
            'HTTP_ACCEPT_LANGUAGE' => 'Определять язык браузера',
            'HTTP_ACCEPT_LANGUAGE_HELP' => 'Попробуйте разрешить установку языка на основе \'http_accept_language\' тега заголовка в браузере пользователя.',
            'OVERRIDE_LOCALE' => 'Переопределение локали',
            'OVERRIDE_LOCALE_HELP' => 'Переопределение языковой локали в PHP на основе текущего языка.',
            'REDIRECT' => 'Страница редиректа',
            'REDIRECT_HELP' => 'Введите адрес страницы или внешний URL для страницы сайта, чтобы перенаправить, например, \'/some/route\' или \'http://somesite.com\'',
            'PLUGIN_STATUS' => 'Статус плагина',
            'INCLUDE_DEFAULT_LANG' => 'Включить язык по умолчанию',
            'INCLUDE_DEFAULT_LANG_HELP' => 'Все URL-адреса будут предваряться приставкой языка. Например, `/en/blog/my-post`.',
            'INCLUDE_DEFAULT_LANG_FILE_EXTENSION' => 'Включить язык по умолчанию в расширение файла',
            'INCLUDE_DEFAULT_LANG_HELP_FILE_EXTENSION' => 'При включении этого параметра к расширению файла будет добавляться язык по умолчанию (например, `.en.md`). Отключите его, чтобы для языка по умолчанию использовалось расширение `.md`.',
            'PAGES_FALLBACK_ONLY' => 'Только резервные страницы',
            'PAGES_FALLBACK_ONLY_HELP' => 'Только \'запасной вариант\' для поиска содержимого страницы на поддерживаемых языках. По умолчанию используется любой найденный язык, если отсутствует активный язык',
            'ALLOW_URL_TAXONOMY_FILTERS' => 'URL для фильтров таксономии',
            'ALLOW_URL_TAXONOMY_FILTERS_HELP' => 'Базовая страница для фильтров`/taxonomy:value`.',
            'REDIRECT_DEFAULT_CODE' => 'Перенаправление по умолчанию',
            'REDIRECT_DEFAULT_CODE_HELP' => 'Статус заголовка HTTP при перенаправлении URL.',
            'IGNORE_HIDDEN' => 'Игнорировать скрытые файлы и папки',
            'IGNORE_HIDDEN_HELP' => 'Игнорировать все скрытые файлы и папки',
            'WRAPPED_SITE' => 'Завернутый сайт',
            'WRAPPED_SITE_HELP' => 'Сообщает плагину или теме оформления, что Grav обернут в другую платформу.',
            'FALLBACK_TYPES' => 'Разрешенные резервные типы',
            'FALLBACK_TYPES_HELP' => 'Разрешенные типы файлов, которые могут быть найдены, если обратиться через маршрутизатор страниц',
            'INLINE_TYPES' => 'Встроенные типы запасных вариантов',
            'INLINE_TYPES_HELP' => 'Список типов файлов, которые должны отображаться, как строенные, а не загружаться',
            'APPEND_URL_EXT' => 'Окончание URL-адресов',
            'APPEND_URL_EXT_HELP' => 'Будет добавлено пользовательское расширение URL страницы. Обратите внимание, что Grav будет искать шаблон в формате `<template>.<extension>.twig`',
            'PAGE_MODES' => 'Режимы страницы',
            'PAGE_TYPES' => 'Типы страницы',
            'PAGE_TYPES_HELP' => 'Определяет типы страниц, которые Grav поддерживает, а также порядок, какой тип значения должен вернуться назад, если запрос является двусмысленным',
            'ACCESS_LEVELS' => 'Уровни доступа',
            'GROUPS' => 'Группы',
            'GROUPS_HELP' => 'Список групп пользователей, входящих в',
            'ADMIN_ACCESS' => 'Доступ администратора',
            'SITE_ACCESS' => 'Доступ сайта',
            'INVALID_SECURITY_TOKEN' => 'Неверный маркер безопасности',
            'ACTIVATE' => 'Активировать',
            'TWIG_UMASK_FIX' => 'Исправление Umask (0755 -> 0775)',
            'TWIG_UMASK_FIX_HELP' => 'По умолчанию Twig создает кэшированные файлы с правами 0755, исправить переключатели на 0775',
            'CACHE_PERMS' => 'Права кэша',
            'CACHE_PERMS_HELP' => 'По умолчанию права папки кэша. Обычно 0755 или 0775 в зависимости от настроек сервера',
            'REMOVE_SUCCESSFUL' => 'Успешно удалено',
            'REMOVE_FAILED' => 'Не удалось удалить',
            'HIDE_HOME_IN_URLS' => 'Убрать путь к главной странице из адресов дочерних',
            'HIDE_HOME_IN_URLS_HELP' => 'Все дочерние страницы внутри главной не будут содержать в адресах маршрутизатор главной (например, вместо /blog/mypage будет отображаться просто /mypage)',
            'TWIG_FIRST' => 'Обрабатывать Twig в первую очередь',
            'TWIG_FIRST_HELP' => 'Если включали обработку страницы Twig, то можно сконфигурировать Twig, чтобы обработать прежде или после markdown',
            'SESSION_SECURE' => 'Безопасный',
            'SESSION_SECURE_HELP' => 'Если включено, указывает, что соединение для этого cookie должна быть по зашифрованной передаче. ПРЕДУПРЕЖДЕНИЕ: Включите это только на сайтах, которые работают исключительно на HTTPS',
            'SESSION_HTTPONLY' => 'Только HTTP',
            'SESSION_HTTPONLY_HELP' => 'Если Да, то куки должны быть использованы только по протоколу HTTP, и модификация JavaScript не допускается',
            'REVERSE_PROXY' => 'Обратный прокси-сервер',
            'REVERSE_PROXY_HELP' => 'Включить, если находитесь за обратным прокси и испытываете проблемы с URL-адресов, содержащих некорректные порты',
            'INVALID_FRONTMATTER_COULD_NOT_SAVE' => 'Неверный формат вступления, не удалось сохранить',
            'ADD_FOLDER' => 'Добавить папку',
            'COPY_PAGE' => 'Копировать страницу',
            'PROXY_URL' => 'Адрес прокси',
            'PROXY_URL_HELP' => 'Введите адрес прокси или IP-адрес и номер порта',
            'PROXY_CERT' => 'Путь к сертификату прокси-сервера',
            'PROXY_CERT_HELP' => 'Локальный путь к папке, содержащей pem-файл сертификата прокси-сервера',
            'NOTHING_TO_SAVE' => 'Нечего сохранять',
            'FILE_ERROR_ADD' => 'Произошла ошибка при попытке добавить файл',
            'FILE_ERROR_UPLOAD' => 'Произошла ошибка при попытке загрузить файл',
            'FILE_UNSUPPORTED' => 'Неподдерживаемый тип файла',
            'ADD_ITEM' => 'Добавить элемент',
            'FILE_TOO_LARGE' => 'Файл слишком большой, чтобы быть загруженным, максимально допустимый размер %s согласно <br>настройке PHP. Увеличте значение параметра настройки PHP `post_max_size`',
            'INSTALLING' => 'Установка',
            'LOADING' => 'Загрузка..',
            'DEPENDENCIES_NOT_MET_MESSAGE' => 'Для начала должны быть удовлетворены следующие зависимости:',
            'ERROR_INSTALLING_PACKAGES' => 'Ошибка при установке пакетов',
            'INSTALLING_DEPENDENCIES' => 'Установка зависимостей...',
            'INSTALLING_PACKAGES' => 'Установка пакетов..',
            'PACKAGES_SUCCESSFULLY_INSTALLED' => 'Пакеты успешно установлены.',
            'READY_TO_INSTALL_PACKAGES' => 'Всё готово для установки пакетов',
            'PACKAGES_NOT_INSTALLED' => 'Пакеты не установлены',
            'PACKAGES_NEED_UPDATE' => 'Пакеты уже установлены, но их версии слишком старые',
            'PACKAGES_SUGGESTED_UPDATE' => 'Пакеты уже установлены и их версии в порядке, но они будут обновлены, чтобы использовать последние версии',
            'REMOVE_THE' => 'Удалить %s',
            'CONFIRM_REMOVAL' => 'Хотите удалить %s?',
            'REMOVED_SUCCESSFULLY' => '%s успешно удалён',
            'ERROR_REMOVING_THE' => 'Ошибка удаления %s',
            'ADDITIONAL_DEPENDENCIES_CAN_BE_REMOVED' => '%s требует следующие зависимости, которые не нужны в других установленных пакетах. Если не используете их, можно удалить их прямо отсюда.',
            'READY_TO_UPDATE_PACKAGES' => 'Всё готово для обновления пакетов',
            'ERROR_UPDATING_PACKAGES' => 'Ошибка при обновлении пакетов',
            'UPDATING_PACKAGES' => 'Обновление пакетов..',
            'PACKAGES_SUCCESSFULLY_UPDATED' => 'Пакеты успешно обновлены.',
            'UPDATING' => 'Обновление',
            'GPM_SECTION' => 'Секция GPM',
            'GPM_RELEASES' => 'Релизы GPM',
            'GPM_RELEASES_HELP' => 'Выберите вариант «Testing» для установки бета-релизов или тестовых версий',
            'GPM_METHOD' => 'Метод удаленного обновления',
            'GPM_METHOD_HELP' => 'При использовании значения «Авто» Grav определит, доступна ли функция fopen и использует её, в противном случае будет использована cURL. Чтобы принудительно использовать один из вариантов, укажите его в настройках.',
            'HTTP_SECTION' => 'Секция HTTP',
            'SSL_ENABLE_PROXY' => 'Включить прокси-сервер',
            'SSL_VERIFY_PEER' => 'Удаленная проверка узла',
            'SSL_VERIFY_PEER_HELP' => 'Возможны ошибки проверки SSL-сертификатов',
            'SSL_VERIFY_HOST' => 'Удаленная проверка хоста',
            'SSL_VERIFY_HOST_HELP' => 'Возможны ошибки проверки SSL-сертификатов',
            'HTTP_CONNECTIONS' => 'HTTP соединения',
            'HTTP_CONNECTIONS_HELP' => 'Количество одновременных соединений HTTP во время мультиплексированных запросов',
            'MISC_SECTION' => 'Прочие параметры',
            'AUTO' => 'Авто',
            'FOPEN' => 'fopen',
            'CURL' => 'cURL',
            'STABLE' => 'Стабильная версия',
            'TESTING' => 'Тестирование',
            'FRONTMATTER_PROCESS_TWIG' => 'Обрабатывать Twig в обобщенных страницах',
            'FRONTMATTER_PROCESS_TWIG_HELP' => 'При включении вы можете использовать конфигурационные переменные Twig в обобщенных страницах',
            'FRONTMATTER_IGNORE_FIELDS' => 'Игнорировать обобщенные поля',
            'FRONTMATTER_IGNORE_FIELDS_HELP' => 'Некоторые обобщенные поля могут содержать Twig, но не должны быть обработаны как «формы»',
            'FRONTMATTER_IGNORE_FIELDS_PLACEHOLDER' => 'напр. формы',
            'PACKAGE_X_INSTALLED_SUCCESSFULLY' => 'Пакет %s успешно установлен',
            'ORDERING_DISABLED_BECAUSE_PARENT_SETTING_ORDER' => 'Установка сортировки родителя, изменение порядка отключено',
            'ORDERING_DISABLED_BECAUSE_PAGE_NOT_VISIBLE' => 'Страница не отображаема, изменение порядка отключено',
            'ORDERING_DISABLED_BECAUSE_TOO_MANY_SIBLINGS' => 'Сортировка через админ-панель не поддерживается, поскольку используется более 200 потомков',
            'ORDERING_DISABLED_BECAUSE_PAGE_NO_PREFIX' => 'На этой странице отключен порядок, поскольку <strong>Числовой префикс папки</strong> не включен',
            'CANNOT_ADD_MEDIA_FILES_PAGE_NOT_SAVED' => 'Примечание: добавление файлов невозможно, пока вы не сохраните страницу. Кликните «Сохранить» в верхней части админки.',
            'CANNOT_ADD_FILES_PAGE_NOT_SAVED' => 'Примечание: страница должна быть сохранена перед загрузкой файлов.',
            'DROP_FILES_HERE_TO_UPLOAD' => 'Переместите файлы сюда или <strong>нажмите на эту область</strong>',
            'INSERT' => 'Вставить',
            'UNDO' => 'Отменить',
            'REDO' => 'Повторить',
            'HEADERS' => 'Заголовки',
            'BOLD' => 'Жирный',
            'ITALIC' => 'Курсив',
            'STRIKETHROUGH' => 'Перечеркнутый',
            'SUMMARY_DELIMITER' => 'Разделитель',
            'LINK' => 'Ссылка',
            'IMAGE' => 'Изображение',
            'BLOCKQUOTE' => 'Цитата',
            'UNORDERED_LIST' => 'Маркированный список',
            'ORDERED_LIST' => 'Нумерованный список',
            'EDITOR' => 'Редактор',
            'PREVIEW' => 'Просмотреть',
            'FULLSCREEN' => 'Полноэкранный режим',
            'NON_ROUTABLE' => 'Немаршрутизируемая',
            'NON_VISIBLE' => 'Невидимая',
            'NON_PUBLISHED' => 'Неопубликованная',
            'CHARACTERS' => 'символов',
            'PUBLISHING' => 'Публикация',
            'MEDIA_TYPES' => 'Типы вложений',
            'IMAGE_OPTIONS' => 'Параметры изображения',
            'MIME_TYPE' => 'Mime-тип',
            'THUMB' => 'Миниатюра',
            'TYPE' => 'Тип',
            'FILE_EXTENSION' => 'Расширение файла',
            'LEGEND' => 'Обозначения',
            'MEMCACHE_SERVER' => 'Сервер Memcache',
            'MEMCACHE_SERVER_HELP' => 'Адрес сервера Memcache',
            'MEMCACHE_PORT' => 'Порт Memcache',
            'MEMCACHE_PORT_HELP' => 'Порт сервера Memcache',
            'MEMCACHED_SERVER' => 'Сервер Memcached',
            'MEMCACHED_SERVER_HELP' => 'Адрес сервера Memcached',
            'MEMCACHED_PORT' => 'Порт Memcached',
            'MEMCACHED_PORT_HELP' => 'Порт сервера Memcached',
            'REDIS_SERVER' => 'Сервер Redis',
            'REDIS_SERVER_HELP' => 'Адрес сервера Redis',
            'REDIS_PORT' => 'Порт Redis',
            'REDIS_PORT_HELP' => 'Порт сервера Redis',
            'REDIS_PASSWORD' => 'Redis пароль/секрет',
            'REDIS_DATABASE' => 'ID базы данных Redis',
            'REDIS_DATABASE_HELP' => 'ID экземпляра базы данных Redis',
            'ALL' => 'все',
            'FROM' => 'от',
            'TO' => 'к',
            'RELEASE_DATE' => 'Дата выпуска',
            'SORT_BY' => 'Сортировать по',
            'RESOURCE_FILTER' => 'Фильтр...',
            'FORCE_SSL' => 'Принудительный SSL',
            'FORCE_SSL_HELP' => 'Принудительно включить SSL по всему сайту. Если включено, при открытии сайта по HTTP Grav перенаправит на HTTPS',
            'NEWS_FEED' => 'Лента новостей',
            'EXTERNAL_URL' => 'Внешний URL',
            'SESSION_SAMESITE' => 'Атрибут сессии SameSite',
            'SESSION_SAMESITE_HELP' => 'Lax|Strict|None. Дополнительную информацию см. на сайте https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite',
            'CUSTOM_BASE_URL' => 'Пользовательский базовый URL',
            'CUSTOM_BASE_URL_HELP' => 'Используйте, если хотите переписать домен сайта или использовать подпапку, отличную от той, что использует Grav. Пример: http://localhost',
            'FILEUPLOAD_PREVENT_SELF' => 'Нельзя использовать "%s" за пределами страницы.',
            'FILEUPLOAD_UNABLE_TO_UPLOAD' => 'Не удается загрузить файл %s: %s',
            'FILEUPLOAD_UNABLE_TO_MOVE' => 'Не удается переместить файл %s в "%s"',
            'DROPZONE_CANCEL_UPLOAD' => 'Отменить загрузку',
            'DROPZONE_CANCEL_UPLOAD_CONFIRMATION' => 'Хотите отменить эту загрузку?',
            'DROPZONE_DEFAULT_MESSAGE' => 'Перетащите файлы сюда или <strong>нажмите на эту область</strong>',
            'DROPZONE_FALLBACK_MESSAGE' => 'Ваш браузер не поддерживает жест перетаскивания для загрузки файлов.',
            'DROPZONE_FALLBACK_TEXT' => 'Пожалуйста, используйте резервную форму ниже, чтобы загрузить файлы как раньше.',
            'DROPZONE_FILE_TOO_BIG' => 'Файл слишком велик ({{filesize}}MiB). Максимальный допустимый размер: {{maxFilesize}}MiB.',
            'DROPZONE_INVALID_FILE_TYPE' => 'Вы не можете загружать файлы этого типа.',
            'DROPZONE_MAX_FILES_EXCEEDED' => 'Вы не можете загрузить больше файлов.',
            'DROPZONE_REMOVE_FILE' => 'Удалить файл',
            'DROPZONE_RESPONSE_ERROR' => 'Сервер ответил с кодом {{statusCode}}.',
            'PREMIUM_PRODUCT' => 'Премиум',
            'DESTINATION_NOT_SPECIFIED' => 'Пункт назначения не указан',
            'UPLOAD_ERR_NO_TMP_DIR' => 'Отсутствует временная директория',
            'SESSION_SPLIT' => 'Раздельные сессии',
            'SESSION_SPLIT_HELP' => 'Отдельные сессии для сайта и других плагинов (такими как Admin)',
            'ERROR_FULL_BACKTRACE' => 'Полная трассировка ошибки',
            'ERROR_SIMPLE' => 'Простая ошибка',
            'ERROR_SYSTEM' => 'Системная ошибка',
            'IMAGES_AUTO_FIX_ORIENTATION' => 'Автоориентация изображений',
            'IMAGES_AUTO_FIX_ORIENTATION_HELP' => 'Автоматическое исправление ориентации изображений на основе данных Exif',
            'REDIS_SOCKET' => 'Сокет Redis',
            'REDIS_SOCKET_HELP' => 'Сокет Redis',
            'NOT_SET' => 'Не задано',
            'PERMISSIONS' => 'Разрешения',
            'NEVER_CACHE_TWIG' => 'Никогда не кэшировать Twig',
            'NEVER_CACHE_TWIG_HELP' => 'Кэшировать только содержание и обрабатывать Twig каждый раз при загрузке страниц. Игнорирует параметр twig_first.',
            'ALLOW_WEBSERVER_GZIP' => 'Разрешить сжатие Gzip на сервере',
            'ALLOW_WEBSERVER_GZIP_HELP' => 'Выключено по умолчанию. При включении будет работать сконфигурированное на сервере сжатие Gzip/Deflate, но соединение http не будет закрываться перед событием onShutDown(), приводящим к замедлению загрузки страницы',
            'OFFLINE_WARNING' => 'Невозможно установить подключение к GPM',
            'CLEAR_IMAGES_BY_DEFAULT' => 'Очищать кэш изображений',
            'CLEAR_IMAGES_BY_DEFAULT_HELP' => 'По умолчанию обработанные изображения очищаются при любых чистках кэша, это можно отключить',
            'CLI_COMPATIBILITY' => 'Совместимость с интерфейсом командной строки (CLI)',
            'CLI_COMPATIBILITY_HELP' => 'Гарантирует использование долговременных драйверов кэширования (file, redis, memcache и т. д.)',
            'REINSTALL_PLUGIN' => 'Переустановить плагин',
            'REINSTALL_THEME' => 'Переустановить тему',
            'REINSTALL_THE' => 'Переустановить %s',
            'CONFIRM_REINSTALL' => 'Хотите переустановить %s?',
            'REINSTALLED_SUCCESSFULLY' => '%s успешно переустановлен(а)',
            'ERROR_REINSTALLING_THE' => 'Ошибка переустановки %s',
            'PACKAGE_X_REINSTALLED_SUCCESSFULLY' => 'Пакет %s успешно переустановлен',
            'REINSTALLATION_FAILED' => 'Переустановка не удалась',
            'WARNING_REINSTALL_NOT_LATEST_RELEASE' => 'Установленная версия не является последней. Нажмите на кнопку «Продолжить», чтобы удалить текущую версию и установить самую свежую',
            'TOOLS' => 'Сервис',
            'DIRECT_INSTALL' => 'Прямая установка',
            'NO_PACKAGE_NAME' => 'Имя пакета не указано',
            'PACKAGE_EXTRACTION_FAILED' => 'Не удалось извлечь пакет',
            'NOT_VALID_GRAV_PACKAGE' => 'Недействительный пакет Grav',
            'NAME_COULD_NOT_BE_DETERMINED' => 'Имя не может быть определено',
            'CANNOT_OVERWRITE_SYMLINKS' => 'Невозможно перезаписать символические ссылки',
            'ZIP_PACKAGE_NOT_FOUND' => 'ZIP-пакет не найден',
            'GPM_OFFICIAL_ONLY' => 'Только официальный GPM',
            'GPM_OFFICIAL_ONLY_HELP' => 'Разрешать прямые установки только из официального хранилища GPM.',
            'NO_CHILD_TYPE' => 'Нет дочернего типа для этого маршрута',
            'SORTABLE_PAGES' => 'Сортируемые страницы:',
            'UNSORTABLE_PAGES' => 'Несортируемые страницы',
            'ADMIN_SPECIFIC_OVERRIDES' => 'Специальные переопределения',
            'ADMIN_CHILDREN_DISPLAY_ORDER' => 'Порядок отображения дочерних страниц',
            'ADMIN_CHILDREN_DISPLAY_ORDER_HELP' => 'Порядок отображения дочерних элементов этой страницы в окне «Страницы» плагина Admin',
            'PWD_PLACEHOLDER' => 'Сложная строка длиной не менее 8 символов',
            'PWD_REGEX' => 'Пароль, регулярка',
            'PWD_REGEX_HELP' => 'По умолчанию: Пароль должен содержать как минимум одно число, одну прописную и строчную буквы, и как минимум 8 символов.',
            'USERNAME_PLACEHOLDER' => 'только строчные символы, н. п. \'admin\'',
            'USERNAME_REGEX' => 'Имя пользователя, регулярка',
            'USERNAME_REGEX_HELP' => 'По умолчанию: Только строчные символы, цифры, дефисы и подчеркивания. 3-16 символов.',
            'ENABLE_AUTO_METADATA' => 'Авто метаданные из Exif',
            'ENABLE_AUTO_METADATA_HELP' => 'Автоматическое создание файлов метаданных для изображений с помощью информации из Exif.',
            '2FA_TITLE' => 'Двухфакторная аутентификация',
            '2FA_INSTRUCTIONS' => '##### 2-факторная аутентификация
У вас включена **2FA**. Пожалуйста, используйте ваше приложение **2FA**, чтобы ввести текущий **6-значный код**, для завершения процесса входа в систему.',
            '2FA_REGEN_HINT' => 'Чтобы восстановить секрет, вам потребуется обновить приложение для аутентификации',
            '2FA_LABEL' => 'Доступ администратора',
            '2FA_FAILED' => 'Недействительный код 2FA, пожалуйста попробуйте еще раз...',
            '2FA_ENABLED' => '2FA включен',
            '2FA_CODE_INPUT' => '000000',
            '2FA_SECRET' => 'Секрет Api',
            '2FA_SECRET_HELP' => 'Сканируйте этот QR-код в свое [приложение аутентификации] (https://learn.getgrav.org/admin-panel/2fa#apps). Также рекомендуется резервировать секрет в безопасном месте, если вам нужно переустановить приложение. Для получения дополнительной информации просмотрите документы [Grav docs] (https://learn.getgrav.org/admin-panel/2fa) ',
            '2FA_REGENERATE' => 'Сгенерировать новый',
            'YUBIKEY_ID' => 'Ключ YubiKey',
            'YUBIKEY_OTP_INPUT' => 'Одноразовый пароль YubiKey',
            'YUBIKEY_HELP' => 'Вставьте YubiKey в компьютер и нажмите кнопку для создания одноразового пароля. Первые 12 символов - идентификатор вашего клиента.',
            'FORCE_LOWERCASE_URLS' => 'Принудительное приведение URLs к строчному виду',
            'FORCE_LOWERCASE_URLS_HELP' => 'По умолчанию Grav будет устанавливать все заглушки и маршруты в нижний регистр. Если этот параметр установлен в false, можно использовать заглушки и маршруты в верхнем регистре',
            'INTL_ENABLED' => 'Интеграция модуля Intl',
            'INTL_ENABLED_HELP' => 'Использовать PHP модуль Intl и collate для сортировки коллекций, хранящихся в UTF-8',
            'VIEW_SITE_TIP' => 'Открыть сайт',
            'TOOLS_DIRECT_INSTALL_TITLE' => 'Прямая установка Grav пакетов',
            'TOOLS_DIRECT_INSTALL_UPLOAD_TITLE' => 'Установка пакета через прямую загрузку ZIP-файла',
            'TOOLS_DIRECT_INSTALL_UPLOAD_DESC' => 'Вы с легкостью можете  обновить <strong>Grav</strong> или установить подходящую <strong>тему</strong>, <strong>плагин</strong> для него. Этот пакет не нужно регистрировать с помощью GPM и он позволяет легко откатиться к предыдущей версии или установить тестовую.',
            'TOOLS_DIRECT_INSTALL_URL_TITLE' => 'Установить пакет через удаленный URL-адрес',
            'TOOLS_DIRECT_INSTALL_URL_DESC' => 'Кроме того, вы также можете указать полный URL-адрес к ZIP-файлу пакета и установить его через этот удаленный URL-адрес.',
            'TOOLS_DIRECT_INSTALL_UPLOAD_BUTTON' => 'Загрузить и установить',
            'ROUTE_OVERRIDES' => 'Переадресация маршрута',
            'ROUTE_DEFAULT' => 'Маршрут по умолчанию',
            'ROUTE_CANONICAL' => 'Канонический маршрут',
            'ROUTE_ALIASES' => 'Псевдонимы маршрута',
            'OPEN_NEW_TAB' => 'Открыть в новой вкладке',
            'SESSION_INITIALIZE' => 'Инициализировать сессию',
            'SESSION_INITIALIZE_HELP' => 'Просит Grav начать сеанс. Эта функция необходима для работы любых взаимодействий с пользователем, таких как вход в систему, формы и т. д. На плагин администратора этот параметр не влияет.',
            'STRICT_YAML_COMPAT' => 'YAML совместимость',
            'STRICT_YAML_COMPAT_HELP' => 'Откат к YAML парсеру Symfony 2.4, если нативный парсер 3.4 не работает',
            'STRICT_TWIG_COMPAT' => 'Twig совместимость',
            'STRICT_TWIG_COMPAT_HELP' => 'Включает устаревшую настройку экранирования Twig. Когда этот параметр отключен, для вывода HTML требуется фильтр |raw, поскольку Twig будет автоматически экранировать вывод',
            'SCHEDULER' => 'Планировщик',
            'SCHEDULER_INSTALL_INSTRUCTIONS' => 'Инструкции по установке',
            'SCHEDULER_INSTALLED_READY' => 'Установлено и готово',
            'SCHEDULER_CRON_NA' => 'Cron недоступен для пользователя: <b>%s</b>',
            'SCHEDULER_NOT_ENABLED' => 'Не включено для пользователя: <b>%s</b>',
            'SCHEDULER_SETUP' => 'Настройка планировщика',
            'SCHEDULER_INSTRUCTIONS' => '<b>Планировщик Grav</b> позволяет создавать и планировать пользовательские задания. Он также предоставляет Grav плагинам возможность интегрировать программно и динамически добавлять задания для периодического запуска.',
            'SCHEDULER_POST_INSTRUCTIONS' => 'Чтобы включить функции планировщика, вы должны добавить <b>планировщик Grav</b> в файл crontab вашей системы для пользователя <b>%s</b>. Запустите указанную выше команду из терминала, чтобы добавить её автоматически. После сохранения обновите эту страницу, чтобы увидеть статус \'Готово\'.',
            'SCHEDULER_JOBS' => 'Пользовательские задания планировщика',
            'SCHEDULER_STATUS' => 'Статус планировщика',
            'SCHEDULER_RUNAT' => 'Запуск в',
            'SCHEDULER_RUNAT_HELP' => 'Синтаксис Cron. ВНИМАНИЕ: Часовой пояс UTC!',
            'SCHEDULER_OUTPUT' => 'Выходной файл',
            'SCHEDULER_OUTPUT_HELP' => 'Путь/имя файла вывода (в корне сайта)',
            'SCHEDULER_OUTPUT_TYPE' => 'Тип вывода',
            'SCHEDULER_OUTPUT_TYPE_HELP' => 'Дописывать или перезаписывать файл',
            'SCHEDULER_EMAIL' => 'Имейл',
            'SCHEDULER_EMAIL_HELP' => 'Имейл для отправки вывода. ПРИМЕЧАНИЕ: требуется указать выходной файл',
            'SCHEDULER_WARNING' => 'Планировщик использует crontab вашей системы для выполнения команд. Вы должны использовать это, только если вы опытный пользователь и знаете, что делаете. Неправильная конфигурация или злоупотребление могут привести к уязвимостям безопасности.',
            'SECURITY' => 'Безопасность',
            'XSS_SECURITY' => 'XSS безопасность контента',
            'XSS_WHITELIST_PERMISSIONS' => 'Белый список разрешений',
            'XSS_WHITELIST_PERMISSIONS_HELP' => 'Пользователи с этими разрешениями пропустят правила XSS при сохранении контента',
            'XSS_ON_EVENTS' => 'Фильтр событий',
            'XSS_INVALID_PROTOCOLS' => 'Фильтр неверных протоколов',
            'XSS_INVALID_PROTOCOLS_LIST' => 'Список неверных протоколов',
            'XSS_MOZ_BINDINGS' => 'Фильтр Moz привязок',
            'XSS_HTML_INLINE_STYLES' => 'Фильтровать встроенные стили HTML',
            'XSS_DANGEROUS_TAGS' => 'Фильтровать опасные HTML-теги',
            'XSS_DANGEROUS_TAGS_LIST' => 'Список опасных HTML-тегов',
            'XSS_ONSAVE_ISSUE' => 'Не удалось сохранить: обнаружена проблема с XSS...',
            'XSS_ISSUE' => '<strong>ВНИМАНИЕ:</strong> Grav обнаружил возможные проблемы с XSS в <strong>%s</strong>',
            'UPLOADS_SECURITY' => 'Безопасность загрузок',
            'UPLOADS_DANGEROUS_EXTENSIONS' => 'Опасные Расширения',
            'UPLOADS_DANGEROUS_EXTENSIONS_HELP' => 'Не разрешать загрузку этих расширений в независимости от разрешённых MIME-типов',
            'REPORTS' => 'Отчёты',
            'LOGS' => 'Логи',
            'LOG_VIEWER_FILES' => 'Файлы логов',
            'LOG_VIEWER_FILES_HELP' => 'Файлы в /logs/ будут доступны для просмотра в Инструменты - Логи. Например, grav\' = /logs/grav.log',
            'BACKUPS_STORAGE_PURGE_TRIGGER' => 'Событие очистки места под резервное копирование',
            'BACKUPS_MAX_COUNT' => 'Максимальное количество резервных копий',
            'BACKUPS_MAX_COUNT_HELP' => '0 — без ограничений',
            'BACKUPS_MAX_SPACE' => 'Максимальный размер для Бэкапов',
            'BACKUPS_MAX_RETENTION_TIME' => 'Максимальное время хранения резервной копии',
            'BACKUPS_MAX_RETENTION_TIME_APPEND' => 'в днях',
            'BACKUPS_PROFILE_NAME' => 'Имя резервной копии',
            'BACKUPS_PROFILE_ROOT_FOLDER' => 'Корневой каталог',
            'BACKUPS_PROFILE_ROOT_FOLDER_HELP' => 'Может быть: Абсолютный путь или "поток"',
            'BACKUPS_PROFILE_EXCLUDE_PATHS' => 'Исключенные пути',
            'BACKUPS_PROFILE_EXCLUDE_PATHS_HELP' => 'Абсолютные пути для исключения, по одному в строке',
            'BACKUPS_PROFILE_EXCLUDE_FILES' => 'Исключенные файлы',
            'BACKUPS_PROFILE_EXCLUDE_FILES_HELP' => 'Конкретные файлы или папки для исключения, по одному в строке',
            'BACKUPS_PROFILE_SCHEDULE' => 'Включить планировщик',
            'BACKUPS_PROFILE_SCHEDULE_AT' => 'Запланированное задание',
            'COMMAND' => 'Команда',
            'EXTRA_ARGUMENTS' => 'Дополнительные аргументы',
            'DEFAULT_LANG' => 'Переопределить язык по умолчанию',
            'DEFAULT_LANG_HELP' => 'По умолчанию - первый из поддерживаемых системой языков. Этот параметр можно переопределить, указав один из поддерживаемых языков',
            'DEBUGGER_PROVIDER' => 'Поставщик отладчика',
            'DEBUGGER_PROVIDER_HELP' => 'По умолчанию используется панель отладки PHP, но расширение браузера Clockwork обеспечивает менее навязчивый подход',
            'DEBUGGER_DEBUGBAR' => 'Панель отладки PHP',
            'DEBUGGER_CLOCKWORK' => 'Расширение для браузера Clockwork',
            'PAGE_ROUTE_NOT_FOUND' => 'Маршрут страницы не найден',
            'PAGE_ROUTE_FOUND' => 'Найден маршрут страницы',
            'NO_ROUTE_PROVIDED' => 'Маршрут не указан',
            'CONTENT_LANGUAGE_FALLBACKS' => 'Резервный язык контента',
            'CONTENT_LANGUAGE_FALLBACKS_HELP' => 'По умолчанию, если контент не переведен, Grav будет отображать контент на языке по умолчанию. Используйте этот параметр, чтобы переопределить это поведение для каждого языка.',
            'CONTENT_LANGUAGE_FALLBACK' => 'Резервный язык',
            'CONTENT_LANGUAGE_FALLBACK_HELP' => 'Пожалуйста, введите список языковых кодов. Обратите внимание: если вы опустите код языка по умолчанию, он не будет использоваться.',
            'CONTENT_FALLBACK_LANGUAGE_HELP' => 'Укажите код языка, который вы хотите настроить.',
            'EXPERIMENTAL' => 'Экспериментальное',
            'PAGES_TYPE' => 'Тип страницы на сайте',
            'PAGES_TYPE_HELP' => 'Этот параметр включает страницы объектов Flex во внешнем интерфейсе. Для Flex-страниц админки требуется плагин Flex Objects',
            'ACCOUNTS_TYPE' => 'Тип учетных записей',
            'ACCOUNTS_TYPE_HELP' => 'Система объектов Flex для хранения учётных записей пользователей',
            'ACCOUNTS_STORAGE' => 'Хранилище учётной записи',
            'ACCOUNTS_STORAGE_HELP' => 'Механизм хранения, который будет использоваться для типа учётной записи объекта Flex. Файлы — это традиционный подход, при котором учётные записи хранятся в файле YAML в одной папке, в то время как папка создает новую папку для каждой учётной записи',
            'FLEX' => 'Объект Flex (ЭКСПЕРЕМЕНТАЛЬНО)',
            'REGULAR' => 'Обычный',
            'FILE' => 'Файл',
            'SANITIZE_SVG' => 'Очищать SVG',
            'SANITIZE_SVG_HELP' => 'Удаляет любой XSS код из SVG',
            'ACCOUNTS' => 'Учетные записи',
            'USER_ACCOUNTS' => 'Учетные записи пользователей',
            'USER_GROUPS' => 'Группы пользователей',
            'GROUP_NAME' => 'Имя группы',
            'DISPLAY_NAME' => 'Отображаемое имя',
            'ICON' => 'Иконка',
            'ACCESS' => 'Доступ',
            'NO_ACCESS' => 'Нет доступа',
            'SUPER_USER' => 'Супер пользователь',
            'ALLOWED' => 'Разрешено',
            'DENIED' => 'Запрещено',
            'MODULE' => 'Модуль',
            'NON_MODULE' => 'Немодульная',
            'ADD_MODULE' => 'Добавить модуль',
            'MODULE_SETUP' => 'Настройка модуля',
            'MODULE_TEMPLATE' => 'Шаблон модуля',
            'ADD_MODULE_CONTENT' => 'Добавить содержимое модуля',
            'CHANGELOG' => 'Список изменений',
            'PAGE_ACCESS' => 'Доступ к странице',
            'PAGE PERMISSIONS' => 'Права доступа к странице',
            'PAGE_ACCESS_HELP' => 'Пользователь с следующими правами доступа может получить доступ к странице.',
            'PAGE_VISIBILITY_REQUIRES_ACCESS' => 'Требуется доступ для отображение меню ',
            'PAGE_VISIBILITY_REQUIRES_ACCESS_HELP' => 'Установите Да, если страница должна быть показана в меню только если пользователь может получить к ней доступ.',
            'PAGE_INHERIT_PERMISSIONS' => 'Наследовать разрешения',
            'PAGE_INHERIT_PERMISSIONS_HELP' => 'Наследовать ACL от родительской страницы.',
            'PAGE_AUTHORS' => 'Авторы страницы',
            'PAGE_AUTHORS_HELP' => 'Участники авторов страниц имеют уровень владельца этой страницы, определенный в специальной группе страниц \'Авторы\'.',
            'PAGE_GROUPS' => 'Группы страниц',
            'PAGE_GROUPS_HELP' => 'Пользователи групп страниц имеют специальный доступ к этой странице.',
            'READ' => 'Читать',
            'PUBLISH' => 'Опубликовать',
            'LIST' => 'Список',
            'ACCESS_SITE' => 'Сайт',
            'ACCESS_SITE_LOGIN' => 'Войти на сайт',
            'ACCESS_ADMIN' => 'Администратор',
            'ACCESS_ADMIN_LOGIN' => 'Войти в админ панель',
            'ACCESS_ADMIN_SUPER' => 'Супер пользователь',
            'ACCESS_ADMIN_CACHE' => 'Очистить кэш',
            'ACCESS_ADMIN_CONFIGURATION' => 'Конфигурация',
            'ACCESS_ADMIN_CONFIGURATION_SYSTEM' => 'Управление конфигурацией системы',
            'ACCESS_ADMIN_CONFIGURATION_SITE' => 'Управление конфигурацией сайта',
            'ACCESS_ADMIN_CONFIGURATION_MEDIA' => 'Управление конфигурацией медиа',
            'ACCESS_ADMIN_CONFIGURATION_INFO' => 'Смотреть информацию о сервере',
            'ACCESS_ADMIN_SETTINGS' => 'Настройки',
            'ACCESS_ADMIN_PAGES' => 'Управление страницами',
            'ACCESS_ADMIN_MAINTENANCE' => 'Обслуживание сайта',
            'ACCESS_ADMIN_STATISTICS' => 'Статистика сайта',
            'ACCESS_ADMIN_PLUGINS' => 'Управление плагинами',
            'ACCESS_ADMIN_THEMES' => 'Управление темами',
            'ACCESS_ADMIN_TOOLS' => 'Доступ к инструментам',
            'ACCESS_ADMIN_USERS' => 'Управление пользователями',
            'USERS' => 'Пользователи',
            'ACL' => 'Контроль доступа',
            'FLEX_CACHING' => 'Кэширование Flex',
            'FLEX_INDEX_CACHE_ENABLED' => 'Включить кэширование индексов',
            'FLEX_INDEX_CACHE_LIFETIME' => 'Время жизни кэша индексов (в секундах)',
            'FLEX_OBJECT_CACHE_ENABLED' => 'Включить кэширование объектов',
            'FLEX_OBJECT_CACHE_LIFETIME' => 'Время жизни кэша объектов (в секундах)',
            'FLEX_RENDER_CACHE_ENABLED' => 'Включить кэширование рендеринга',
            'FLEX_RENDER_CACHE_LIFETIME' => 'Время жизни кэша рендеринга (в секундах)',
            'DEBUGGER_CENSORED' => 'Конфиденциальная информация',
            'DEBUGGER_CENSORED_HELP' => 'ТОЛЬКО поставщик Clockwork: если да, подвергать цензуре потенциально конфиденциальную информацию (параметры POST, куки, файлы, конфигурацию и большинство данных массивов/объектов в сообщениях журнала)',
            'LANGUAGE_TRANSLATIONS' => 'Переводы',
            'LANGUAGE_TRANSLATIONS_HELP' => 'Если параметр отключен, вместо переведенных строк используются ключи перевода. Эту функцию можно использовать для исправления плохих переводов или для поиска жестко закодированных английских строк.',
            'STRICT_BLUEPRINT_COMPAT' => 'Совместимость blueprint',
            'STRICT_BLUEPRINT_COMPAT_HELP' => 'Обеспечивает строгую поддержку чертежей с обратной совместимостью. Если этот параметр отключен, новое поведение делает невозможной проверку формы при наличии дополнительных данных, которые не определены в чертеже.',
            'RESET' => 'Сброс',
            'LOGOS' => 'Логотипы',
            'PRESETS' => 'Предустановки',
            'COLOR_SCHEME_LABEL' => 'Цветовая схема',
            'COLOR_SCHEME_HELP' => 'Выберите цветовую схему из списка предопределенных комбинаций или добавьте свой собственный стиль',
            'COLOR_SCHEME_NAME' => 'Название пользовательской цветовой схемы',
            'COLOR_SCHEME_NAME_HELP' => 'Дайте имя вашей пользовательской теме для экспорта и обмена',
            'COLOR_SCHEME_NAME_PLACEHOLDER' => 'Оттенки синего',
            'PRIMARY_ACCENT_LABEL' => 'Основной Акцент',
            'PRIMARY_ACCENT_HELP' => 'Выберите, какой цвет должен быть установлен как основной акцент для цветовой схемы',
            'SECONDARY_ACCENT_LABEL' => 'Вторичный акцент',
            'SECONDARY_ACCENT_HELP' => 'Выберите вторичный набор цветов для своей цветовой схемы.',
            'TERTIARY_ACCENT_LABEL' => 'Третичный акцент',
            'TERTIARY_ACCENT_HELP' => 'Выберите третичный набор цветов для своей цветовой схемы.',
            'WEB_FONTS_LABEL' => 'Веб шрифты',
            'WEB_FONTS_HELP' => 'Использовать пользовательские веб-шрифты',
            'HEADER_FONT_LABEL' => 'Шрифт заголовка',
            'HEADER_FONT_HELP' => 'Шрифт, используемый для заголовков, боковой навигации и заголовков разделов',
            'BODY_FONT_LABEL' => 'Шрифт описания',
            'BODY_FONT_HELP' => 'Основной шрифт, используемый в основной части темы',
            'CUSTOM_CSS_LABEL' => 'Пользовательский CSS',
            'CUSTOM_CSS_PLACEHOLDER' => 'Вставьте ваш пользовательский CSS здесь...',
            'CUSTOM_CSS_HELP' => 'CSS, который будет добавлен на каждую страницу админки',
            'CUSTOM_FOOTER' => 'Пользовательский футер',
            'CUSTOM_FOOTER_HELP' => 'Здесь можно использовать синтаксис HTML и/или Markdown',
            'CUSTOM_FOOTER_PLACEHOLDER' => 'Введите HTML/Markdown для переопределения колонтитула по умолчанию',
            'LOGIN_SCREEN_CUSTOM_LOGO_LABEL' => 'Пользовательский логотип входа в систему',
            'TOP_LEFT_CUSTOM_LOGO_LABEL' => 'Основной пользовательский логотип',
            'LOAD_PRESET' => 'Загрузить пресет',
            'RECOMPILE' => 'Перекомпилировать',
            'EXPORT' => 'Экспортировать',
            'QUICKTRAY_RECOMPILE' => 'Значок перекомпиляции в QuickTray',
            'QUICKTRAY_RECOMPILE_HELP' => 'Перекомпилировать пресет SCSS для получения любых изменений или новых плагинов',
            'CODEMIRROR' => 'Редактор CodeMirror',
            'CODEMIRROR_THEME' => 'Тема редактора',
            'CODEMIRROR_THEME_DESC' => '**ПРИМЕЧАНИЕ:** Для того, чтобы увидеть их в действии используйте [CodeMirror Themes Demo](https://codemirror.net/demo/theme.html?target=_blank). **_Paper_** это Grav тема по умолчанию.',
            'CODEMIRROR_FONTSIZE' => 'Размер шрифта редактора',
            'CODEMIRROR_FONTSIZE_SM' => 'Мелкий шрифт',
            'CODEMIRROR_FONTSIZE_MD' => 'Средний шрифт',
            'CODEMIRROR_FONTSIZE_LG' => 'Крупный шрифт',
            'CODEMIRROR_MD_FONT' => 'Шрифт редактора Markdown',
            'CODEMIRROR_MD_FONT_SANS' => 'Мелкий шрифт',
            'CODEMIRROR_MD_FONT_MONO' => 'Шрифт с фиксированной шириной',
            'CUSTOM_PRESETS' => 'Пользовательские пресеты',
            'CUSTOM_PRESETS_HELP' => 'Перетащите сюда файл темы .yaml или создайте массив предустановок с текстовыми ключами.',
            'CUSTOM_PRESETS_PLACEHOLDER' => 'Разместите свои пресеты здесь.',
            'GENERAL' => 'Общие настройки',
            'CONTENT_EDITOR' => 'Редактор контента',
            'CONTENT_EDITOR_HELP' => 'Пользовательские редакторы могут быть предпочтительнее для редактирования контента',
            'BAD_FILENAME' => 'Неверное имя файла',
            'SHOW_SENSITIVE' => 'Показать конфиденциальные данные',
            'SHOW_SENSITIVE_HELP' => 'ТОЛЬКО для Clockwork: обрабатывать потенциально конфиденциальную информацию (параметры POST, куки, файлы, конфигурацию и большинство данных массивов/объектов в сообщениях журнала)',
            'VALID_LINK_ATTRIBUTES' => 'Допустимые атрибуты ссылок',
            'VALID_LINK_ATTRIBUTES_HELP' => 'Атрибуты, которые будут автоматически добавлены к медиа-элементу HTML.',
            'CONFIGURATION' => 'Настройка',
            'CUSTOMIZATION' => 'Тонкая настройка',
            'EXTRAS' => 'Дополнительно',
            'BASICS' => 'Основное',
            'ADMIN_CACHING' => 'Включить кэширование админки',
            'ADMIN_CACHING_HELP' => 'Кэшированием в админке можно управлять независимо от основного сайта',
            'ADMIN_PATH' => 'Относительный путь админки',
            'ADMIN_PATH_PLACEHOLDER' => 'Маршрут по умолчанию для доступа к админке.',
            'ADMIN_PATH_HELP' => 'Если хотите изменить адрес админки, укажите свой путь.',
            'LOGO_TEXT' => 'Текст логотипа',
            'LOGO_TEXT_HELP' => 'Текст для отображения вместо логотипа Grav по умолчанию',
            'CONTENT_PADDING' => 'Отступы для содержимого',
            'CONTENT_PADDING_HELP' => 'Включить/отключить добавление контента вокруг области содержимого, чтобы обеспечить больше места.',
            'BODY_CLASSES' => 'Классы страницы',
            'BODY_CLASSES_HELP' => 'Добавьте свои классы для тега body, через пробел.',
            'SIDEBAR_ACTIVATION' => 'Активация боковой панели',
            'SIDEBAR_ACTIVATION_HELP' => 'Укажите, как будет включаться боковая панель.',
            'SIDEBAR_HOVER_DELAY' => 'Задержка при наведении',
            'SIDEBAR_HOVER_DELAY_APPEND' => 'миллисекунд',
            'SIDEBAR_ACTIVATION_TAB' => 'Видима всегда',
            'SIDEBAR_ACTIVATION_HOVER' => 'При наведении',
            'SIDEBAR_SIZE' => 'Размер боковой панели',
            'SIDEBAR_SIZE_HELP' => 'Укажите ширину боковой панели.',
            'SIDEBAR_SIZE_AUTO' => 'Автоматическая ширина',
            'SIDEBAR_SIZE_SMALL' => 'Маленькая ширина',
            'EDIT_MODE' => 'Режим редактирования',
            'EDIT_MODE_HELP' => 'По умолчанию будет использоваться план (если доступнен). Если план отсутствует, будет использоваться режим «Эксперт».',
            'FRONTEND_PREVIEW_TARGET' => 'Предварительный просмотр страниц',
            'FRONTEND_PREVIEW_TARGET_INLINE' => 'Внутри админки',
            'FRONTEND_PREVIEW_TARGET_NEW' => 'В новой вкладке',
            'FRONTEND_PREVIEW_TARGET_CURRENT' => 'В текущей вкладке',
            'PARENT_DROPDOWN' => 'Родительский раскрывающийся список',
            'PARENT_DROPDOWN_BOTH' => 'Показывать строку и папку',
            'PARENT_DROPDOWN_FOLDER' => 'Показывать папку',
            'PARENT_DROPDOWN_FULLPATH' => 'Показывать полный путь',
            'PARENTS_LEVELS' => 'Родительские уровни',
            'PARENTS_LEVELS_HELP' => 'Количество уровней, отображаемых в родительском списке',
            'MODULAR_PARENTS' => 'Модульные родители',
            'MODULAR_PARENTS_HELP' => 'Показывать модульные страницы в списке выбора у родителя',
            'SHOW_GITHUB_LINK' => 'Показывать ссылку на GitHub',
            'SHOW_GITHUB_LINK_HELP' => 'Показывать сообщение «Нашли ошибку? Пожалуйста, сообщите об этом на GitHub.»',
            'PAGES_LIST_DISPLAY_FIELD' => 'Отображаемое поле списка страниц',
            'PAGES_LIST_DISPLAY_FIELD_HELP' => 'Поле страницы для использования в списке страниц. По умолчанию/откат к заголовку.',
            'AUTO_UPDATES' => 'Автоматически проверять наличие обновлений',
            'AUTO_UPDATES_HELP' => 'Отображает информативное сообщение в админ-панели, если доступно обновление.',
            'TIMEOUT' => 'Время ожидания',
            'TIMEOUT_HELP' => 'Устанавливает тайм-аут сеанса в секундах',
            'HIDE_PAGE_TYPES' => 'Скрываемые типы страниц в админке',
            'HIDE_MODULAR_PAGE_TYPES' => 'Скрываемые типы модульных страниц в админке',
            'DASHBOARD' => 'Панель управления',
            'WIDGETS_DISPLAY' => 'Статус отображения виджета',
            'NOTIFICATIONS' => 'Уведомления',
            'FEED_NOTIFICATIONS' => 'Уведомления ленты новостей',
            'FEED_NOTIFICATIONS_HELP' => 'Отображать уведомления ленты',
            'DASHBOARD_NOTIFICATIONS' => 'Уведомления дашборда',
            'DASHBOARD_NOTIFICATIONS_HELP' => 'Отображать уведомления на дашборде',
            'PLUGINS_NOTIFICATIONS' => 'Уведомления плагинов',
            'PLUGINS_NOTIFICATIONS_HELP' => 'Отображать уведомления плагинов',
            'THEMES_NOTIFICATIONS' => 'Уведомления тем',
            'THEMES_NOTIFICATIONS_HELP' => 'Отображать уведомления тем',
            'LOGO_BG_HELP' => 'Фон логотипа',
            'LOGO_LINK_HELP' => 'Ссылка на логотип',
            'NAV_BG_HELP' => 'Фон навигации',
            'NAV_TEXT_HELP' => 'Текст навигации',
            'NAV_LINK_HELP' => 'Ссылка',
            'NAV_SELECTED_BG_HELP' => 'Фон выбранной ссылки',
            'NAV_SELECTED_LINK_HELP' => 'Выбранная ссылка',
            'NAV_HOVER_BG_HELP' => 'Фон ссылки при наведении',
            'NAV_HOVER_LINK_HELP' => 'Ссылка при наведении',
            'TOOLBAR_BG_HELP' => 'Фон панели инструментов',
            'TOOLBAR_TEXT_HELP' => 'Текст панели инструментов',
            'PAGE_BG_HELP' => 'Фон страницы',
            'PAGE_TEXT_HELP' => 'Текст страницы',
            'PAGE_LINK_HELP' => 'Ссылка на страницу',
            'CONTENT_BG_HELP' => 'Фоновый цвет содержимого',
            'CONTENT_TEXT_HELP' => 'Текст содержимого',
            'CONTENT_LINK_HELP' => 'Ссылка на контент',
            'CONTENT_LINK2_HELP' => 'Ссылка на контент 2',
            'CONTENT_HEADER_HELP' => 'Заголовок содержимого',
            'CONTENT_TABS_BG_HELP' => 'Фоновый цвет вкладок содержимого',
            'CONTENT_TABS_TEXT_HELP' => 'Текст вкладок содержимого',
            'CONTENT_HIGHLIGHT_HELP' => 'Подсветка содержимого',
            'BUTTON_BG_HELP' => 'Фон кнопки',
            'BUTTON_TEXT_HELP' => 'Текст кнопки',
            'NOTICE_BG_HELP' => 'Фон уведомления',
            'NOTICE_TEXT_HELP' => 'Текст уведомления',
            'UPDATES_BG_HELP' => 'Фон сообщения об обновлениях',
            'UPDATES_TEXT_HELP' => 'Текст сообщения об обновлениях',
            'CRITICAL_BG_HELP' => 'Фон критического уведомления',
            'CRITICAL_TEXT_HELP' => 'Текст критического уведомления',
            'BUTTON_COLORS' => 'Цвет кнопок',
            'CONTENT_COLORS' => 'Цвета содержимого',
            'TABS_COLORS' => 'Цвета вкладок',
            'CRITICAL_COLORS' => 'Цвета критических уведомлений',
            'LOGO_COLORS' => 'Цвета логотипа',
            'NAV_COLORS' => 'Цвета навигации',
            'NOTICE_COLORS' => 'Цвета уведомлений',
            'PAGE_COLORS' => 'Цвета страницы',
            'TOOLBAR_COLORS' => 'Цвета панели инструментов',
            'UPDATE_COLORS' => 'Цвета обновлений',
            'POPULARITY' => 'Популярность',
            'VISITOR_TRACKING' => 'Отслеживание посетителей',
            'VISITOR_TRACKING_HELP' => 'Включить функцию сбора статистики посетителей',
            'DAYS_OF_STATS' => 'Дней статистики',
            'DAYS_OF_STATS_HELP' => 'Сохранять статистику для указанного количества дней, а затем отбрасывать ее',
            'IGNORE_URLS' => 'Игнорировать',
            'IGNORE_URLS_HELP' => 'Перечислите игнорируемые URL-адреса.',
            'DAILY_HISTORY' => 'История за день',
            'MONTHLY_HISTORY' => 'История за месяц',
            'VISITORS_HISTORY' => 'История посещений',
            'MEDIA_RESIZE' => 'Изменитель размеров изображений страницы',
            'PAGEMEDIA_RESIZER' => '> Следующие настройки применяются к изображениям, загруженным через страницу мультимедиа. Изменение размера по ширине/высоте автоматически изменит размеры и пропорционально уменьшит изображение, если оно превысит установленные лимиты. Минимальное и максимальное значения разрешения определяют диапазоны размеров для загружаемых изображений. Установите поля в 0, чтобы предотвратить любое изменение.',
            'RESIZE_WIDTH' => 'Изменить ширину',
            'RESIZE_WIDTH_HELP' => 'Изменить размер широкого изображения вниз до заданного значения',
            'RESIZE_HEIGHT' => 'Изменить высоту',
            'RESIZE_HEIGHT_HELP' => 'Изменить размер высокого изображения вниз до заданного значения',
            'RES_MIN_WIDTH' => 'Минимальная ширина разрешения',
            'RES_MIN_WIDTH_HELP' => 'Минимальная ширина, разрешенная для добавления изображения',
            'RES_MIN_HEIGHT' => 'Минимальная высота разрешения',
            'RES_MIN_HEIGHT_HELP' => 'Минимальная высота, разрешенная для добавления изображения',
            'RES_MAX_WIDTH' => 'Максимальная ширина разрешения',
            'RES_MAX_WIDTH_HELP' => 'Максимальная ширина, разрешенная для добавления изображения',
            'RES_MAX_HEIGHT' => 'Максимальная высота разрешения',
            'RES_MAX_HEIGHT_HELP' => 'Максимальная высота, разрешенная для добавления изображения',
            'RESIZE_QUALITY' => 'Качество изменения размера',
            'RESIZE_QUALITY_HELP' => 'Используемое качество при изменении размера изображения. Значения между 0 и 1.',
            'PIXELS' => 'пикселей',
            'ACCESS_ADMIN_CONFIGURATION_SECURITY' => 'Управление конфигурацией безопасности',
            'SESSION_DOMAIN' => 'Домен сессии',
            'SESSION_DOMAIN_HELP' => 'Используйте только если вы переписываете домен сайта, например в контейнере Docker.',
            'SESSION_PATH' => 'Путь к сессии',
            'SESSION_PATH_HELP' => 'Используйте только если вы перепишете путь к сайту, например, в контейнере Docker.',
            'REDIRECT_OPTION_NO_REDIRECT' => 'Без перенаправления',
            'REDIRECT_OPTION_DEFAULT_REDIRECT' => 'Использовать код перенаправления по умолчанию',
            'REDIRECT_OPTION_301' => '301 - Перемещено навсегда',
            'REDIRECT_OPTION_302' => '302 - Перемещено временно',
            'REDIRECT_OPTION_303' => '303 - Посмотреть другие',
            'IMAGES_CLS_TITLE' => 'Совокупное смещение макета (CLS)',
            'IMAGES_CLS_AUTO_SIZES' => 'Включить авторазмеры',
            'IMAGES_CLS_AUTO_SIZES_HELP' => 'Автоматическое добавление атрибутов \'width\' и \'height\' к изображениям для решения проблемы CLS',
            'IMAGES_CLS_ASPECT_RATIO' => 'Включить соотношение сторон',
            'IMAGES_CLS_ASPECT_RATIO_HELP' => 'Необязательная переменная CSS, которая применяется через атрибут \'style\' и может быть использована в CSS для создания индивидуального стиля',
            'IMAGES_CLS_RETINA_SCALE' => 'Масштаб изображений с высоким разрешением',
            'IMAGES_CLS_RETINA_SCALE_HELP' => 'Возьмет расчетный размер и разделит на масштабный коэффициент, чтобы отобразить изображение с большим разрешением при меньшем размере пикселя для лучшей работы с разрешениями HiDPI',
            'AUTOREGENERATE_FOLDER_SLUG' => 'Авторегенерация на основе заголовка страницы',
            'ENABLE' => 'Включить',
            'PLUGINS_MUST_BE_ENABLED' => 'Плагин должен быть включен для настройки',
            'ACTIVATION_REQUIRED' => 'Для настройки требуется активация',
            'SESSION_SECURE_HTTPS' => 'Безопасный (HTTPS)',
            'SESSION_SECURE_HTTPS_HELP' => 'Установите защиту сеанса по протоколу HTTPS. Не работает, если настройка Безопасность установлена во включено. Установите выключено, если используется оба протокола.',
            'AVATAR' => 'Генератор аватаров',
            'AVATAR_HELP' => 'Мультиаватар — это локально сгенерированный аватар. Gravatar — это внешний сервис, который использует ваш адрес электронной почты для получения предопределенного аватара удаленно',
            'AVATAR_HASH' => 'ПРИМЕЧАНИЕ: Необязательная строка пользовательского аватара \'hash\'',
            'IMAGES_TITLE' => 'Изображения',
            'LEGACY_MEDIA_MUTATION_HELP' => 'Включите эту настройку, только если работа с изображениями сломалась после обновления Grav.',
            'BACKWARD_COMPATIBILITY' => 'Обратная совместимость'
        ]
    ]
];
