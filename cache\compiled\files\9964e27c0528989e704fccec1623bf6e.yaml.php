<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/ai-draft-assistant/ai-draft-assistant.yaml',
    'modified' => 1747168125,
    'size' => 768,
    'data' => [
        'enabled' => true,
        'api_key' => '',
        'model' => 'gpt-3.5-turbo',
        'temperature' => 0.7,
        'max_tokens' => 500,
        'debug' => true,
        'target_forms' => [
            0 => 'get-a-quote'
        ],
        'auto_append_suggestions' => false,
        'prompt_template' => 'You are an assistant for a plumbing company. Create a professional response to the customer inquiry below.
Consider the following details:
- Be polite and professional
- Address the specific plumbing issue mentioned
- If it\'s an emergency, emphasize quick response
- Include relevant information about the service

Customer information:
Name: {{name}}
Email: {{email}}
Phone: {{phone}}
Address: {{address}}
Message: {{message}}
Emergency Service Requested: {{emergency}}

Create a draft response that a plumbing company representative could send to this customer.
'
    ]
];
