<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/admin/languages/cs.yaml',
    'modified' => 1730089692,
    'size' => 71587,
    'data' => [
        'PLUGIN_ADMIN' => [
            'ADMIN_NOSCRIPT_MSG' => 'Prosím povolte JavaScript ve vašem prohlížeči.',
            'ADMIN_BETA_MSG' => 'Jedná se o beta verzi! V ostrém provozu používejte pouze na vlastní nebezpečí...',
            'ADMIN_REPORT_ISSUE' => 'Objevili jste problém? Nahlaste ho, prosím, na GitHub.',
            'EMAIL_FOOTER' => '<a href="https://getgrav.org">Používá Grav</a> - Moderní systém správy obsahu pomocí souborů prostých textů',
            'LOGIN_BTN' => 'Přihlásit',
            'LOGIN_BTN_FORGOT' => 'Obnovit heslo',
            'LOGIN_BTN_RESET' => 'Obnovit heslo',
            'LOGIN_BTN_SEND_INSTRUCTIONS' => 'Odeslat pokyny pro obnovu hesla',
            'LOGIN_BTN_CLEAR' => 'Vymazat formulář',
            'LOGIN_BTN_CREATE_USER' => 'Vytvořit uživatele',
            'LOGIN_LOGGED_IN' => 'Byli jste úspěšně přihlášeni',
            'LOGIN_FAILED' => 'Přihlášení se nezdařilo',
            'LOGGED_OUT' => 'Byli jste odhlášeni',
            'RESET_NEW_PASSWORD' => 'Prosím, zadejte nové heslo &hellip;',
            'RESET_LINK_EXPIRED' => 'Odkaz pro obnovení hesla vypršel, zkuste to, prosím, znovu',
            'RESET_PASSWORD_RESET' => 'Heslo bylo změněno',
            'RESET_INVALID_LINK' => 'Použit neplatný odkaz pro obnovu hesla, zkuste to, prosím, znovu',
            'FORGOT_INSTRUCTIONS_SENT_VIA_EMAIL' => 'Pokyny pro obnovení hesla byly odeslány na vaši e-mailovou adresu',
            'FORGOT_FAILED_TO_EMAIL' => 'Nepodařilo se odeslat instrukce pro obnovu hesla, zkuste to, prosím, později',
            'FORGOT_CANNOT_RESET_EMAIL_NO_EMAIL' => 'Heslo pro %s nelze obnovit, není zadána e-mailová adresa',
            'FORGOT_USERNAME_DOES_NOT_EXIST' => 'Uživatel <b>%s</b> neexistuje',
            'FORGOT_EMAIL_NOT_CONFIGURED' => 'Heslo nelze obnovit. Tento web nemá nastaveno odesílání e-mailů',
            'FORGOT_EMAIL_SUBJECT' => '%s Požadavek na obnovení hesla',
            'FORGOT_EMAIL_BODY' => '<h1>Obnovení hesla</h1><p>Vážený %1$s,</p><p>požadavek na obnovu hesla byl zadán na <b>%4$s</b>.</p><p><br /><a href="%2$s" class="btn-primary">Klikněte zde pro nastavení nového hesla</a><br /><br /></p><p>Případně, zkopírujte následující adresu URL do adresního řádku vašeho prohlížeče:</p> <p>%2$s</p><p><br />Děkujeme,<br /><br />%3$s</p>',
            'MANAGE_PAGES' => 'Spravovat stránky',
            'PAGES' => 'Stránky',
            'PLUGINS' => 'Doplňky',
            'PLUGIN' => 'Doplněk',
            'THEMES' => 'Šablony',
            'LOGOUT' => 'Odhlásit',
            'BACK' => 'Zpět',
            'NEXT' => 'Následující',
            'PREVIOUS' => 'Předchozí',
            'ADD_PAGE' => 'Přidat stránku',
            'MOVE' => 'Přesunout',
            'DELETE' => 'Smazat',
            'UNSET' => 'Zrušit nastavení',
            'VIEW' => 'Zobrazit',
            'SAVE' => 'Uložit',
            'NORMAL' => 'Normální',
            'EXPERT' => 'Expertní',
            'EXPAND_ALL' => 'Rozbalit vše',
            'COLLAPSE_ALL' => 'Sbalit vše',
            'ERROR' => 'Chyba',
            'CLOSE' => 'Zavřít',
            'CANCEL' => 'Zrušit',
            'CONTINUE' => 'Pokračovat',
            'CONFIRM' => 'Potvrdit',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_TITLE' => 'Vyžadováno potvrzení',
            'MODAL_CHANGED_DETECTED_TITLE' => 'Zjištěny změny',
            'MODAL_CHANGED_DETECTED_DESC' => 'Máte neuložené změny. Jste si jisti, že chcete odejít bez uložení?',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_TITLE' => 'Vyžadováno potvrzení',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_DESC' => 'Opravdu chcete tento soubor smazat? Tato akce je nevratná.',
            'MODAL_UPDATE_GRAV_CONFIRMATION_REQUIRED_DESC' => 'Chystáte se aktualizovat Grav na nejnovější dostupnou verzi. Chcete pokračovat?',
            'ADD_FILTERS' => 'Přidat filtry',
            'SEARCH_PAGES' => 'Hledat stránky',
            'VERSION' => 'Verze',
            'WAS_MADE_WITH' => 'Byl vytvořen s',
            'BY' => 'Od',
            'UPDATE_THEME' => 'Aktualizovat šablonu',
            'UPDATE_PLUGIN' => 'Aktualizovat doplněk',
            'OF_THIS_THEME_IS_NOW_AVAILABLE' => 'této šablony je nyní dostupná',
            'OF_THIS_PLUGIN_IS_NOW_AVAILABLE' => 'tohoto doplňku je nyní dostupná',
            'AUTHOR' => 'Autor',
            'HOMEPAGE' => 'Domovská stránka',
            'DEMO' => 'Demo',
            'BUG_TRACKER' => 'Správa chyb',
            'KEYWORDS' => 'Klíčová slova',
            'LICENSE' => 'Licence',
            'DESCRIPTION' => 'Popis',
            'README' => 'Soubor README',
            'DOCS' => 'Dokumenty',
            'REMOVE_THEME' => 'Odstranit šablonu',
            'INSTALL_THEME' => 'Nainstalovat šablonu',
            'THEME' => 'Šablona',
            'BACK_TO_THEMES' => 'Zpět na šablony',
            'BACK_TO_PLUGINS' => 'Zpět na doplňky',
            'CHECK_FOR_UPDATES' => 'Zkontrolovat aktualizace',
            'ADD' => 'Přidat',
            'CLEAR_CACHE' => 'Vyprázdnit mezipaměť',
            'CLEAR_CACHE_ALL_CACHE' => 'Všechny mezipaměti',
            'CLEAR_CACHE_ASSETS_ONLY' => 'Pouze zdroje',
            'CLEAR_CACHE_IMAGES_ONLY' => 'Pouze obrázky',
            'CLEAR_CACHE_CACHE_ONLY' => 'Pouze mezipaměť',
            'CLEAR_CACHE_TMP_ONLY' => 'Pouze dočasné soubory',
            'UPDATES_AVAILABLE' => 'Dostupné aktualizace',
            'DAYS' => 'Dny',
            'UPDATE' => 'Aktualizovat',
            'BACKUP' => 'Zálohování',
            'BACKUPS' => 'Zálohování',
            'BACKUP_NOW' => 'Zálohovat nyní',
            'BACKUPS_STATS' => 'Statistiky zálohování',
            'BACKUPS_HISTORY' => 'Historie zálohování',
            'BACKUPS_PURGE_CONFIG' => 'Konfigurace vyčištění záloh',
            'BACKUPS_PROFILES' => 'Profily zálohování',
            'BACKUPS_COUNT' => 'Počet záloh',
            'BACKUPS_PROFILES_COUNT' => 'Počet profilů',
            'BACKUPS_TOTAL_SIZE' => 'Využité místo',
            'BACKUPS_NEWEST' => 'Nejnovější záloha',
            'BACKUPS_OLDEST' => 'Nejstarší záloha',
            'BACKUPS_PURGE' => 'Vyčistit',
            'BACKUPS_NOT_GENERATED' => 'Zatím nebyly vytvořeny žádné zálohy...',
            'BACKUPS_PURGE_NUMBER' => 'Použito %s z %s záložního místa',
            'BACKUPS_PURGE_TIME' => '%s dnů záloh zbývá',
            'BACKUPS_PURGE_SPACE' => 'Použito %s z %s',
            'BACKUP_DELETED' => 'Záloha úspěšně smazána',
            'BACKUP_NOT_FOUND' => 'Záloha nalezena',
            'BACKUP_DATE' => 'Datum zálohy',
            'STATISTICS' => 'Statistiky',
            'VIEWS_STATISTICS' => 'Statistiky stránky',
            'TODAY' => 'Dnes',
            'WEEK' => 'Týden',
            'MONTH' => 'Měsíc',
            'LATEST_PAGE_UPDATES' => 'Poslední aktualizace stránky',
            'MAINTENANCE' => 'Údržba',
            'UPDATED' => 'Aktualizováno',
            'MON' => 'Po',
            'TUE' => 'Út',
            'WED' => 'St',
            'THU' => 'Čt',
            'FRI' => 'Pá',
            'SAT' => 'So',
            'SUN' => 'Ne',
            'COPY' => 'Kopírovat',
            'EDIT' => 'Upravit',
            'CREATE' => 'Vytvořit',
            'GRAV_ADMIN' => 'Správa Grav',
            'GRAV_OFFICIAL_PLUGIN' => 'Oficiální doplněk Gravu',
            'GRAV_OFFICIAL_THEME' => 'Oficiální šablona Gravu',
            'PLUGIN_SYMBOLICALLY_LINKED' => 'Tento zásuvný modul je nainstalován pomocí symbolického linku. Změny v něm nebudou zjištěny.',
            'THEME_SYMBOLICALLY_LINKED' => 'Tato šablona je nainstalována pomocí symbolického linku. Změny v ní nebudou zjištěny',
            'REMOVE_PLUGIN' => 'Odstranit doplněk',
            'INSTALL_PLUGIN' => 'Nainstalovat doplněk',
            'AVAILABLE' => 'K dispozici',
            'INSTALLED' => 'Instalováno',
            'INSTALL' => 'Nainstalovat',
            'ACTIVE_THEME' => 'Aktivní šablona',
            'SWITCHING_TO' => 'Přepnout na',
            'SWITCHING_TO_DESCRIPTION' => 'Přepnutím na jinou šablonu nelze garantovat, že všechny vzhledy stránek budou podporované, což může způsobit jejich nedostupnost.',
            'SWITCHING_TO_CONFIRMATION' => 'Chcete pokračovat a přepnout na jinou šablonu',
            'CREATE_NEW_USER' => 'Vytvořit nového uživatele',
            'REMOVE_USER' => 'Odstranit uživatele',
            'ACCESS_DENIED' => 'Přístup byl odepřen',
            'ACCOUNT_NOT_ADMIN' => 'váš účet nemá administrátorská oprávnění',
            'PHP_INFO' => 'PHP Info',
            'INSTALLER' => 'Instalátor',
            'AVAILABLE_THEMES' => 'Dostupné šablony',
            'AVAILABLE_PLUGINS' => 'Dostupné doplňky',
            'INSTALLED_THEMES' => 'Nainstalované šablony',
            'INSTALLED_PLUGINS' => 'Nainstalované doplňky',
            'BROWSE_ERROR_LOGS' => 'Prohlédnout systémové záznamy',
            'SITE' => 'Web',
            'INFO' => 'Info',
            'SYSTEM' => 'Systém',
            'USER' => 'Uživatel',
            'ADD_ACCOUNT' => 'Přidat účet',
            'SWITCH_LANGUAGE' => 'Přepnout jazyk',
            'SUCCESSFULLY_ENABLED_PLUGIN' => 'Zásuvný modul byl úspěšně povolen',
            'SUCCESSFULLY_DISABLED_PLUGIN' => 'Zásuvný modul byl úspěšně zakázán',
            'SUCCESSFULLY_CHANGED_THEME' => 'Úspěšně změněna výchozí šablona',
            'INSTALLATION_FAILED' => 'Instalace se nezdařila',
            'INSTALLATION_SUCCESSFUL' => 'Instalace byla úspěšná',
            'UNINSTALL_FAILED' => 'Odinstalace se nezdařila',
            'UNINSTALL_SUCCESSFUL' => 'Odinstalace byla úspěšná',
            'SUCCESSFULLY_SAVED' => 'Úspěšně uloženo',
            'SUCCESSFULLY_COPIED' => 'Úspěšně zkopírováno',
            'REORDERING_WAS_SUCCESSFUL' => 'Změna pořadí byla úspěšná',
            'SUCCESSFULLY_DELETED' => 'Úspěšně smazáno',
            'SUCCESSFULLY_SWITCHED_LANGUAGE' => 'Jazyk úspěšně změněn',
            'INSUFFICIENT_PERMISSIONS_FOR_TASK' => 'Nemáte dostatečná oprávnění pro úlohu',
            'CACHE_CLEARED' => 'Mezipaměť smazána',
            'METHOD' => 'Metoda',
            'ERROR_CLEARING_CACHE' => 'Nepodařilo se smazat mezipaměť',
            'AN_ERROR_OCCURRED' => 'Došlo k chybě',
            'YOUR_BACKUP_IS_READY_FOR_DOWNLOAD' => 'Vaše záloha je připravena ke stažení',
            'DOWNLOAD_BACKUP' => 'Stáhnout zálohu',
            'PAGES_FILTERED' => 'Stránky vyfiltrovány',
            'NO_PAGE_FOUND' => 'Stránka nenalezena',
            'INVALID_PARAMETERS' => 'Neplatné parametry',
            'NO_FILES_SENT' => 'Žádné soubory nebyly odeslány',
            'EXCEEDED_FILESIZE_LIMIT' => 'Překročen limit velikosti souboru nastaveného konfiguračním souborem PHP',
            'EXCEEDED_POSTMAX_LIMIT' => 'Byl překročen post_max_size konfigurace PHP',
            'UNKNOWN_ERRORS' => 'Neznámé chyby',
            'EXCEEDED_GRAV_FILESIZE_LIMIT' => 'Překročen limit velikosti souboru nastaveného konfiguračním souborem Grav',
            'UNSUPPORTED_FILE_TYPE' => 'Nepodporovaný typ souboru',
            'FAILED_TO_MOVE_UPLOADED_FILE' => 'Přesunutí nahraného souboru se nezdařilo.',
            'FILE_UPLOADED_SUCCESSFULLY' => 'Soubor úspěšně nahrán',
            'FILE_DELETED' => 'Soubor smazán',
            'FILE_COULD_NOT_BE_DELETED' => 'Soubor nebylo možné odstranit',
            'FILE_NOT_FOUND' => 'Soubor nenalezen',
            'NO_FILE_FOUND' => 'Nebyl nalezen žádný soubor',
            'FIELD_REORDER_SUCCESSFUL' => 'Pořadí médií aktualizováno pro pole \'%s\'',
            'FIELD_REORDER_FAILED' => 'Při ukládání pořadí médií pro pole \'%s\' došlo k chybě',
            'GRAV_WAS_SUCCESSFULLY_UPDATED_TO' => 'Grav byl úspěšně aktualizován na verzi',
            'GRAV_UPDATE_FAILED' => 'Aktualizace Grav se nezdařila',
            'EVERYTHING_UPDATED' => 'Vše bylo aktulizováno',
            'UPDATES_FAILED' => 'Aktualizace se nezdařily',
            'AVATAR_BY' => 'Ikona uživatele od',
            'AVATAR_UPLOAD_OWN' => 'Nebo si nahrajte vlastní...',
            'LAST_BACKUP' => 'Poslední záloha',
            'FULL_NAME' => 'Jméno a příjmení',
            'USERNAME' => 'Uživatelské jméno',
            'EMAIL' => 'E-mailová adresa',
            'USERNAME_EMAIL' => 'Uživatelské jméno nebo e-mailová adresa',
            'PASSWORD' => 'Heslo',
            'PASSWORD_CONFIRM' => 'Potvrzení hesla',
            'TITLE' => 'Název',
            'ACCOUNT' => 'Účet',
            'EMAIL_VALIDATION_MESSAGE' => 'Musíte zadat platnou e-mailovou adresu',
            'PASSWORD_VALIDATION_MESSAGE' => 'Heslo musí obsahovat alespoň jedno číslo, jedno velké a malé písmeno a musí být alespoň 8 znaků dlouhé',
            'LANGUAGE' => 'Jazyk',
            'LANGUAGE_HELP' => 'Nastavte Váš preferovaný jazyk',
            'LANGUAGE_DEBUG' => 'Ladit jazyk',
            'LANGUAGE_DEBUG_HELP' => 'Povolte ladění jazyků, které používají twig filtr |t, přidáním rozsahu kolem nich, který lze upravit tak, aby pomohl diagnostikovat problémy',
            'MEDIA' => 'Média',
            'DEFAULTS' => 'Výchozí hodnoty',
            'SITE_TITLE' => 'Název webu',
            'SITE_TITLE_PLACEHOLDER' => 'Titulek napříč celým webem',
            'SITE_TITLE_HELP' => 'Výchozí titulek webu, většinou použit v šablonách',
            'SITE_DEFAULT_LANG' => 'Výchozí jazyk',
            'SITE_DEFAULT_LANG_PLACEHOLDER' => 'Výchozí jazyk použitý ve značce <HTML> šablony',
            'SITE_DEFAULT_LANG_HELP' => 'Výchozí jazyk použitý ve značce <HTML> šablony',
            'DEFAULT_AUTHOR' => 'Výchozí autor',
            'DEFAULT_AUTHOR_HELP' => 'Výchozí jméno autora, často použito v šablonách či obsazích stránek',
            'DEFAULT_EMAIL' => 'Výchozí e-mailová adresa',
            'DEFAULT_EMAIL_HELP' => 'Výchozí e-mailová adresa, na kterou je odkazováno v šablonách či stránkách',
            'TAXONOMY_TYPES' => 'Typy taxonomií',
            'TAXONOMY_TYPES_HELP' => 'Typy taxonomií musí být vytvořeny zde, aby bylo možné je použít v nastavení stránek',
            'PAGE_SUMMARY' => 'Souhrn stránky',
            'ENABLED' => 'Zapnuto',
            'ENABLED_HELP' => 'Zapnout souhrn stránky (souhrn je stejný jako obsah stránky)',
            'YES' => 'Ano',
            'NO' => 'Ne',
            'SUMMARY_SIZE' => 'Souhrnná velikost',
            'SUMMARY_SIZE_HELP' => 'Počet znaků stránky, který je použit jako souhrn',
            'FORMAT' => 'Formát',
            'FORMAT_HELP' => 'krátký = až po první výskyt oddělovače nebo do délky souhrnu; dlouhý = ignorovat oddělovač',
            'SHORT' => 'Krátký',
            'LONG' => 'Dlouhý',
            'DELIMITER' => 'Oddělovač',
            'DELIMITER_HELP' => 'Oddělovač souhrnu (výchozí \'===\')',
            'METADATA' => 'Metadata',
            'METADATA_HELP' => 'Výchozí metadata, která budou zobrazena na každé stránce pokud nejsou přepsána v nastavení stránky',
            'NAME' => 'Název',
            'CONTENT' => 'Obsah',
            'SIZE' => 'Velikost',
            'ACTION' => 'Akce',
            'REDIRECTS_AND_ROUTES' => 'Přesměrování a cesty',
            'CUSTOM_REDIRECTS' => 'Vlastní přesměrování',
            'CUSTOM_REDIRECTS_HELP' => 'Cesty jsou přesměrovány na jiné stránky. Použití regulárních výrazů je možné',
            'CUSTOM_REDIRECTS_PLACEHOLDER_KEY' => '/váš/alias',
            'CUSTOM_REDIRECTS_PLACEHOLDER_VALUE' => '/vaše/přesměrování',
            'CUSTOM_ROUTES' => 'Vlastní cesty',
            'CUSTOM_ROUTES_HELP' => 'Cesty jsou aliasy pro jiné stránky. Použití regulárních výrazů je možné',
            'CUSTOM_ROUTES_PLACEHOLDER_KEY' => '/váš/alias',
            'CUSTOM_ROUTES_PLACEHOLDER_VALUE' => '/vaše/cesta',
            'FILE_STREAMS' => 'Soubory',
            'DEFAULT' => 'Výchozí',
            'PAGE_MEDIA' => 'Média stránky',
            'OPTIONS' => 'Možnosti',
            'PUBLISHED' => 'Zveřejněná',
            'PUBLISHED_HELP' => 'Ve výchozím nastavení je stránka zveřejněná, pokud výslovně nenastavíte published: false nebo publish_date jako budoucí nebo unpublish_date jako minulé',
            'DATE' => 'Datum',
            'DATE_HELP' => 'Můžete nastavit konkrétní datum, které bude přiřazené k této stránce.',
            'PUBLISHED_DATE' => 'Datum zveřejnění',
            'PUBLISHED_DATE_HELP' => 'Můžete nastavit datum, kdy bude článek automaticky zveřejněn.',
            'UNPUBLISHED_DATE' => 'Datum skrytí',
            'UNPUBLISHED_DATE_HELP' => 'Můžete nastavit datum, kdy bude článek automaticky skryt.',
            'ROBOTS' => 'Roboti',
            'TAXONOMIES' => 'Taxonomie',
            'TAXONOMY' => 'Taxonomie',
            'ADVANCED' => 'Pokročilé',
            'SETTINGS' => 'Nastavení',
            'FOLDER_NUMERIC_PREFIX' => 'Číselná předpona složky',
            'FOLDER_NUMERIC_PREFIX_HELP' => 'Číselná předpona, která umožňuje manuální řazení a zajišťuje viditelnost',
            'FOLDER_NAME' => 'Název složky',
            'FOLDER_NAME_HELP' => 'Název složky, která bude vytvořena v souborovém systému pro tuto stránku',
            'PARENT' => 'Nadřazená stránka',
            'DEFAULT_OPTION_ROOT' => '- Root -',
            'DEFAULT_OPTION_SELECT' => '- Vyberte -',
            'DISPLAY_TEMPLATE' => 'Šablona pro zobrazení',
            'DISPLAY_TEMPLATE_HELP' => 'Název šablony, která bude použita pro zobrazení této stránky',
            'ORDERING' => 'Řazení',
            'PAGE_ORDER' => 'Pořadí stránek',
            'OVERRIDES' => 'Přepsat výchozí nastavení',
            'MENU' => 'Menu',
            'MENU_HELP' => 'Řetězec, který má být použit v menu. Pokud není nastaven, bude použit název.',
            'SLUG' => 'Slug',
            'SLUG_HELP' => 'Slug umožněje nastavit část URL pro tuto konkrétní stránku',
            'SLUG_VALIDATE_MESSAGE' => 'Slug může obsahovat pouze znaky malé abecedy, čísla a pomlčky.',
            'PROCESS' => 'Zpracování stránky',
            'PROCESS_HELP' => 'Nastavte jaký renderovací engine se má použít pro zpracování stránky',
            'DEFAULT_CHILD_TYPE' => 'Výchozí typ nové stránky',
            'USE_GLOBAL' => 'Použít globálně',
            'ROUTABLE' => 'Přístupná',
            'ROUTABLE_HELP' => 'Pokud je tato stránka dosažitelná prostřednictvím adresy URL',
            'CACHING' => 'Použít cache',
            'VISIBLE' => 'Viditelná',
            'VISIBLE_HELP' => 'Určuje, zda je stránka viditelná v navigaci.',
            'DISABLED' => 'Vypnuto',
            'ITEMS' => 'Položky',
            'ORDER_BY' => 'Řadit podle',
            'ORDER' => 'Pořadí',
            'FOLDER' => 'Složka',
            'ASCENDING' => 'Vzestupně',
            'DESCENDING' => 'Sestupně',
            'PAGE_TITLE' => 'Název stránky',
            'PAGE_TITLE_HELP' => 'Titulek stránky',
            'PAGE' => 'Stránka',
            'FRONTMATTER' => 'Hlavička',
            'FILENAME' => 'Název souboru',
            'PARENT_PAGE' => 'Nadřazená stránka',
            'HOME_PAGE' => 'Domovská stránka',
            'HOME_PAGE_HELP' => 'Stránka, kterou Grav použije jako výchozí při příchodu na web',
            'DEFAULT_THEME' => 'Výchozí šablona',
            'DEFAULT_THEME_HELP' => 'Nastaví výchozí šablonu kterou bude Grav používat (výchozí je Antimatter)',
            'TIMEZONE' => 'Časové pásmo',
            'TIMEZONE_HELP' => 'Přepíše výchozí časovou zónu serveru',
            'SHORT_DATE_FORMAT' => 'Krátký formát data',
            'SHORT_DATE_FORMAT_HELP' => 'Nastaví krátký formát data, který lze použít v šablonách',
            'LONG_DATE_FORMAT' => 'Dlouhý formát data',
            'LONG_DATE_FORMAT_HELP' => 'Nastaví dlouhý formát data, který lze použít v šablonách',
            'DEFAULT_ORDERING' => 'Výchozí řazení',
            'DEFAULT_ORDERING_HELP' => 'Stránky budou v seznamu zobrazeny v tomto pořadí, pokud nemá konkrétní stránka jiné nastavení',
            'DEFAULT_ORDERING_DEFAULT' => 'Výchozí - podle názvu složky',
            'DEFAULT_ORDERING_FOLDER' => 'Složka - podle názvu složky bez prefixu',
            'DEFAULT_ORDERING_TITLE' => 'Název - podle názvu nastaveného v hlavičce stránky',
            'DEFAULT_ORDERING_DATE' => 'Datum - podle data nastaveného v hlavičce stránky',
            'DEFAULT_ORDER_DIRECTION' => 'Výchozí směr řazení',
            'DEFAULT_ORDER_DIRECTION_HELP' => 'Směr řazení ve výpisu stránek',
            'DEFAULT_PAGE_COUNT' => 'Výchozí počet stránek',
            'DEFAULT_PAGE_COUNT_HELP' => 'Výchozí počet zobrazených stránek v seznamu',
            'DATE_BASED_PUBLISHING' => 'Zveřejnění podle data',
            'DATE_BASED_PUBLISHING_HELP' => 'Automaticky zveřejnit/skrýt příspěvky podle jejich data',
            'EVENTS' => 'Události',
            'EVENTS_HELP' => 'Povolit nebo zakázat konkrétní události. Zakázání některých událostí může mít za následek nefunkčnost některých doplňků',
            'REDIRECT_DEFAULT_ROUTE' => 'Přesměrovat výchozí cestu',
            'REDIRECT_DEFAULT_ROUTE_HELP' => 'Automatické přesměrování na výchozí cestu stránky',
            'LANGUAGES' => 'Jazyky',
            'SUPPORTED' => 'Podporováno',
            'SUPPORTED_HELP' => 'Dvouznakové jazykové kódy oddělené čárkou (například \'en,fr,de\')',
            'SUPPORTED_PLACEHOLDER' => 'např. en, fr',
            'TRANSLATIONS_FALLBACK' => 'Hledat i v ostatních jazycích',
            'TRANSLATIONS_FALLBACK_HELP' => 'Pokud překlad neexistuje v aktivním jazyce, prohledat i ostatní jazyky',
            'ACTIVE_LANGUAGE_IN_SESSION' => 'Aktivní jazyk v session',
            'ACTIVE_LANGUAGE_IN_SESSION_HELP' => 'Uložit aktivní jazyk do session',
            'HTTP_HEADERS' => 'HTTP Hlavičky',
            'EXPIRES' => 'Expires',
            'EXPIRES_HELP' => 'Nastaví \'expires\' záznam v hlavičce. Hodnota je v sekundách.',
            'CACHE_CONTROL' => 'HTTP kontrola mezipaměti',
            'CACHE_CONTROL_HELP' => 'Nastavte platnou hodnotu kontroly mezipaměti, jako např. `no-cache, no-store, must-revalidate`',
            'CACHE_CONTROL_PLACEHOLDER' => 'např. public, max-age=31536000',
            'LAST_MODIFIED' => 'Naposledy změněno',
            'LAST_MODIFIED_HELP' => 'Nastaví datum \'Last Modified\' v HTTP hlavičce, které může pomoci při optimalizaci cachování na straně proxy serveru a prohlížeče',
            'ETAG' => 'ETag',
            'ETAG_HELP' => 'Nastaví \'ETag\' záznam v HTTP hlavičce, který pomáhá rozeznat kdy byla stránka naposledy upravena',
            'VARY_ACCEPT_ENCODING' => 'Vary: Accept Encoding',
            'VARY_ACCEPT_ENCODING_HELP' => 'Nastaví \'Vary: Accept Encoding\' záznam v HTTP hlavičce, který pomáhá s cachováním na straně proxy a CDN',
            'MARKDOWN' => 'Markdown',
            'MARKDOWN_EXTRA' => 'Markdown extra',
            'MARKDOWN_EXTRA_HELP' => 'Povolit ve výchozím nastavení syntaxi Markdown Extra - https://michelf.ca/projects/php-markdown/extra/',
            'MARKDOWN_EXTRA_ESCAPE_FENCES' => 'Escapovat prvky HTML v markdown zvláštním značením',
            'MARKDOWN_EXTRA_ESCAPE_FENCES_HELP' => 'Escapování prvků HTML v markdown zvláštním značením',
            'AUTO_LINE_BREAKS' => 'Automatické zalamování řádků',
            'AUTO_LINE_BREAKS_HELP' => 'Povolit ve výchozím nastavení automatické zalamování řádků v Markdownu',
            'AUTO_URL_LINKS' => 'Převádět URL na odkazy',
            'AUTO_URL_LINKS_HELP' => 'Povolit automatické převádění URL na odkazy pomocí <a> elementu',
            'ESCAPE_MARKUP' => 'Escapovat HTML specifické znaky',
            'ESCAPE_MARKUP_HELP' => 'Automaticky escapovat HTML specifické znaky na jejich bezpečné ekvivalenty',
            'CACHING_HELP' => 'Globální vypnutí/zapnutí cachování v Gravu',
            'CACHE_CHECK_METHOD' => 'Metoda kontroly cache stránek',
            'CACHE_CHECK_METHOD_HELP' => 'Vyberte metodu, pomocí které Grav může zkontrolovat jestli byly soubory stránky modifikovány',
            'CACHE_DRIVER' => 'Cache',
            'CACHE_DRIVER_HELP' => 'Vyberte jaký způsob cachování ma Grav používat. \'Auto Detect\' se pokusí najít ten nejlepší pro Vaši konfiguraci.',
            'CACHE_PREFIX' => 'Prefix cache',
            'CACHE_PREFIX_HELP' => 'Identifikátor pro klíče použité Gravem. Neupravujte pokud si nejste jistí co děláte.',
            'CACHE_PREFIX_PLACEHOLDER' => 'Odvozený od URL webu (přepište náhodným řetězcem)',
            'CACHE_PURGE_JOB' => 'Spustit plánovanou čistící úlohu',
            'CACHE_PURGE_JOB_HELP' => 'Pomocí plánovače můžete pravidelně vymazat starou mezipaměť prostřednictvím této úlohy',
            'CACHE_CLEAR_JOB' => 'Spustit plánovanou čistící úlohu',
            'CACHE_CLEAR_JOB_HELP' => 'Prostřednictvím služby Plánovač můžete pravidelně mazat mezipaměť Gravu',
            'CACHE_JOB_TYPE' => 'Typ úlohy mezipaměti',
            'CACHE_JOB_TYPE_HELP' => 'Buď vymazat "standardní" složky mezipaměti, nebo "všechny" složky',
            'CACHE_PURGE' => 'Vyčistit starou mezipaměť',
            'LIFETIME' => 'Platnost',
            'LIFETIME_HELP' => 'Délka platnosti záznamů v cachi. 0 = nekonečně.',
            'GZIP_COMPRESSION' => 'Komprese Gzip',
            'GZIP_COMPRESSION_HELP' => 'Zapnout GZip kompresi stránek pro rychlejší načítání.',
            'TWIG_TEMPLATING' => 'Twig šablony',
            'TWIG_CACHING' => 'Cachovat Twig šablony',
            'TWIG_CACHING_HELP' => 'Nastavuje zda se využite cachování Twig šablon. Nechejte zapnuté pro nějlepší výkon.',
            'TWIG_DEBUG' => 'Twig debug mód',
            'TWIG_DEBUG_HELP' => 'Umožňuje vypnout načítání rozšíření Twig Debugger',
            'DETECT_CHANGES' => 'Detekovat změny',
            'DETECT_CHANGES_HELP' => 'Twig bude automaticky aktualizovat cache pokud detekuje změnu v Twig šabloně',
            'AUTOESCAPE_VARIABLES' => 'Automaticky escapovat proměnné',
            'AUTOESCAPE_VARIABLES_HELP' => 'Escapování všech proměnných bude mít pravděpodobně za následek nefunkčnost webu',
            'ASSETS' => 'Zdroje',
            'CSS_ASSETS' => 'Zdroje CSS',
            'CSS_PIPELINE' => 'Kanál CSS',
            'CSS_PIPELINE_HELP' => 'Kanál CSS je sjednocení více zdrojů CSS do jednoho souboru',
            'CSS_PIPELINE_INCLUDE_EXTERNALS' => 'Zahrnout externí do kanálu CSS',
            'CSS_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'Externí URL adresy někdy mají relativní odkazy na soubory a neměly by být sjednoceny',
            'CSS_PIPELINE_BEFORE_EXCLUDES' => 'Nejprve vykreslit kanál CSS',
            'CSS_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Vykreslit kanál CSS před jinými odkazy CSS, které nejsou zahrnuty',
            'CSS_MINIFY' => 'Minifikovat CSS soubory',
            'CSS_MINIFY_HELP' => 'Minifikuje všechny CSS soubory',
            'CSS_MINIFY_WINDOWS_OVERRIDE' => 'Minifikace CSS na Windows',
            'CSS_MINIFY_WINDOWS_OVERRIDE_HELP' => 'Přepsat nastavení minifikace CSS pro Windows. Ve výchozím nastavení je vypnuto kvůli ThreadStackSize',
            'CSS_REWRITE' => 'Přepsat URL uvnitř CSS',
            'CSS_REWRITE_HELP' => 'Přepíše relativní URL uvnitř CSS souborů',
            'JS_ASSETS' => 'Zdroje JavaScriptu',
            'JAVASCRIPT_PIPELINE' => 'Kanál JavaScriptu',
            'JAVASCRIPT_PIPELINE_HELP' => 'Kanál JS je sjednocení více zdrojů JS do jednoho souboru',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS' => 'Zahrnout externí do kanálu JS',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'Externí URL adresy někdy mají relativní odkazy na soubory a neměly by být sjednoceny',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES' => 'Nejprve vykreslit kanál JS',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Vykreslit kanál JS před jinými odkazy JS, které nejsou zahrnuty',
            'JS_MODULE_ASSETS' => 'Zdroje modulu JavaScriptu',
            'JAVASCRIPT_MODULE_PIPELINE' => 'Kanál JavaScriptového modulu',
            'JAVASCRIPT_MODULE_PIPELINE_HELP' => 'Kanál JS modulu je sjednocení více zdrojů JS do jednoho souboru',
            'JAVASCRIPT_MODULE_PIPELINE_INCLUDE_EXTERNALS' => 'Zahrnout externí do kanálu JS modulu',
            'JAVASCRIPT_MODULE_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'Externí URL adresy někdy mají relativní odkazy na soubory a neměly by být sjednoceny',
            'JAVASCRIPT_MODULE_PIPELINE_BEFORE_EXCLUDES' => 'Nejprve vykreslit kanál JS modulu',
            'JAVASCRIPT_MODULE_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Vykreslit kanál JS modulu před jinými odkazy JS, které nejsou zahrnuty',
            'GENERAL_CONFIG' => 'Obecné nastavení zdroje',
            'JAVASCRIPT_MINIFY' => 'Minifikovat JavaScripty',
            'JAVASCRIPT_MINIFY_HELP' => 'Minifikuje všechny JavaScriptové soubory',
            'ENABLED_TIMESTAMPS_ON_ASSETS' => 'Povolit časová razítka na zdrojích',
            'ENABLED_TIMESTAMPS_ON_ASSETS_HELP' => 'Povolit zdroji časová razítka',
            'ENABLED_SRI_ON_ASSETS' => 'Povolit SRI na zdrojích',
            'ENABLED_SRI_ON_ASSETS_HELP' => 'Povolit zdroji SRI',
            'COLLECTIONS' => 'Kolekce',
            'ERROR_HANDLER' => 'Hlášení chyb',
            'DISPLAY_ERRORS' => 'Zobrazit chyby',
            'DISPLAY_ERRORS_HELP' => 'Zobrazit celou stránku s výpisem z backtrace',
            'LOG_ERRORS' => 'Logovat chyby',
            'LOG_ERRORS_HELP' => 'Logovat chyby do /logs adresáře',
            'LOG_HANDLER' => 'Správce logů',
            'LOG_HANDLER_HELP' => 'Kam ukládat výstup z logu',
            'SYSLOG_FACILITY' => 'Zařízení Syslogu',
            'SYSLOG_FACILITY_HELP' => 'Zařízení Syslogu pro výstup',
            'SYSLOG_TAG' => 'Štítek Syslogu',
            'SYSLOG_TAG_HELP' => 'Štítek Syslogu pro výstup',
            'DEBUGGER' => 'Debugger',
            'DEBUGGER_HELP' => 'Povolit Grav debugger a následující nastavení',
            'DEBUG_TWIG' => 'Debugovat Twig',
            'DEBUG_TWIG_HELP' => 'Povolit debugování Twig šablon',
            'SHUTDOWN_CLOSE_CONNECTION' => 'Uzavírat spojení',
            'SHUTDOWN_CLOSE_CONNECTION_HELP' => 'Uzavřít spojení před zavoláním \'onShutdown()\' události. Vypnout při povoleném debuggování',
            'DEFAULT_IMAGE_QUALITY' => 'Výchozí kvalita obrázků',
            'DEFAULT_IMAGE_QUALITY_HELP' => 'Výchozí kvalita obrázků při úpravách nebo cachování (85%)',
            'CACHE_ALL' => 'Cachovat všechny obrázky',
            'CACHE_ALL_HELP' => 'Zpracovat všechny obrázky pomocí cachovacího systému Gravu ikdyž nevyžadují žádné úpravy',
            'IMAGES_DEBUG' => 'Vodoznak pro lepší ladění',
            'IMAGES_DEBUG_HELP' => 'Zobrazit vrstvu přes obrázek znázorňující hustotu pixelů v obrázku například pro retina displaye',
            'IMAGES_LOADING' => 'Chování načítání obrázku',
            'IMAGES_LOADING_HELP' => 'Atribut načítání umožňuje prohlížeči oddálit načítání snímků mimo obrazovku a iframů, dokud se uživatelé neposunou poblíž. Načítání podporuje tři hodnoty: auto, lazy, eager',
            'IMAGES_DECODING' => 'Chování načítání obrázku',
            'IMAGES_DECODING_HELP' => 'Atribut načítání umožňuje prohlížeči odložit načítání obrázků mimo obrazovku, dokud se k nim uživatelé neposunou poblíž. Načítání podporuje tři hodnoty: auto, sync, async',
            'IMAGES_SEOFRIENDLY' => 'SEO-přívětivý název obrázku',
            'IMAGES_SEOFRIENDLY_HELP' => 'Je-li tato volba zapnuta, zobrazí se nejprve název obrázku a potom menší hodnota hash, která odráží zpracované operace',
            'UPLOAD_LIMIT' => 'Limit pro uploadované soubory',
            'UPLOAD_LIMIT_HELP' => 'Nastaví maximální povolenou velikost pro uploadované soubory v bytech (0 = neomezeně)',
            'ENABLE_MEDIA_TIMESTAMP' => 'Zapnout časové značky na médiích',
            'ENABLE_MEDIA_TIMESTAMP_HELP' => 'Přidá časovou značku podle poslední modifikace do každé URL odkazující na média',
            'SESSION' => 'Relace',
            'SESSION_ENABLED_HELP' => 'Povolit podporu relací v rámci Grav',
            'SESSION_NAME_HELP' => 'Identifikátor pro vytvoření názvu pro session cookie',
            'SESSION_UNIQUENESS' => 'Jedinečný řetězec',
            'SESSION_UNIQUENESS_HELP' => 'MD5 hash kořenové cesty Gravu, např. `GRAV_ROOT` (výchozí) nebo založený na náhodném `osoleném (security.salt)` řetězci.',
            'ABSOLUTE_URLS' => 'Absolutní URL',
            'ABSOLUTE_URLS_HELP' => 'Absolutní nebo relativní URL v proměnné \'base_url\'',
            'PARAMETER_SEPARATOR' => 'Oddělovač parametrů',
            'PARAMETER_SEPARATOR_HELP' => 'Oddělovač parametrů, který lze změnit pro Apache na Windows',
            'TASK_COMPLETED' => 'Úkol dokončen',
            'EVERYTHING_UP_TO_DATE' => 'Vše je aktualizováno',
            'UPDATES_ARE_AVAILABLE' => 'jsou dostupné nové aktualizace',
            'IS_AVAILABLE_FOR_UPDATE' => 'je možné aktualizovat',
            'IS_NOW_AVAILABLE' => 'je nyní k dispozici',
            'CURRENT' => 'Aktuální',
            'UPDATE_GRAV_NOW' => 'Aktualizovat nyní Grav',
            'GRAV_SYMBOLICALLY_LINKED' => 'Grav je symbolicky propojen, aktualizace nebude k dispozici',
            'UPDATING_PLEASE_WAIT' => 'Probíhá aktualizace... prosím čekejte na stáhnutí',
            'OF_THIS' => 'této',
            'OF_YOUR' => 'Vaší',
            'HAVE_AN_UPDATE_AVAILABLE' => 'má k dispozici novou verzi',
            'SAVE_AS' => 'Uložit jako',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_DESC' => 'Opravdu chcete smazat tuto stránku a všechny její podstránky? Překlady této stránky nebudou smazány společně s touto stránkou a musí být odstraněny samostatně. Adresář se stránkou bude smazán společně s jejími podstránkami. Tato akce je nevratná.',
            'AND' => 'a',
            'UPDATE_AVAILABLE' => 'Dostupná aktualizace',
            'METADATA_KEY' => 'Klíč (např. \'Keywords\')',
            'METADATA_VALUE' => 'Hodnota (např. \'Blog, Grav\')',
            'USERNAME_HELP' => 'Uživatelské jméno by mělo mít 3 až 16 znaků včetně malých písmen, čísel, podtržítka a pomlčky. Velká písmena, mezery a speciální znaky nejsou povoleny',
            'FULLY_UPDATED' => 'Úplně aktualizované',
            'SAVE_LOCATION' => 'Uložit umístění',
            'PAGE_FILE' => 'Šablona stránky',
            'PAGE_FILE_HELP' => 'Název souboru se stránkou. Ve výchozím nastavení je to také název šablony použitý pro tuto stránku.',
            'NO_USER_ACCOUNTS' => 'Nebyly nalezen žádný uživatelský účet, prosím, nejdříve si jeden vytvořte',
            'NO_USER_EXISTS' => 'Pro tento účet neexistuje žádný místní uživatel, nelze uložit...',
            'REDIRECT_TRAILING_SLASH' => 'Přesměrovat URL končící na \'/\'',
            'REDIRECT_TRAILING_SLASH_HELP' => 'Provést přesměrování pomocí kódu 301 namísto ignorování pokud URL končí lomítkem.',
            'DEFAULT_DATE_FORMAT' => 'Výchozí formát data',
            'DEFAULT_DATE_FORMAT_HELP' => 'Formát data stránky používaný Gravem. Ve výchozím nastavení se Grav pokusí uhodnout zadaný formát data, ale můžete též přesně určit formát data použitím PHP syntaxe (např.: Y-m-d H:i nebo d.m.Y H:i:s)',
            'DEFAULT_DATE_FORMAT_PLACEHOLDER' => 'Automaticky odhadnout',
            'IGNORE_FILES' => 'Ignorovat soubory',
            'IGNORE_FILES_HELP' => 'Ignorovat tyto soubory při zpracování stránky',
            'IGNORE_FOLDERS' => 'Ignorovat složky',
            'IGNORE_FOLDERS_HELP' => 'Ignorovat tyto adresáře při zpracování stránky',
            'HIDE_EMPTY_FOLDERS' => 'Skrýt prázdné složky',
            'HIDE_EMPTY_FOLDERS_HELP' => 'Pokud složka neobsahuje žádný soubor .md, měla by být skrytá v navigaci, stejně jako nepřístupná',
            'HTTP_ACCEPT_LANGUAGE' => 'Nastavit jazyk dle prohlížeče',
            'HTTP_ACCEPT_LANGUAGE_HELP' => 'Zkusí nastavit jazyk podle `http_accept_language` hlavičky v prohlížeči',
            'OVERRIDE_LOCALE' => 'Přepsat lokalitu',
            'OVERRIDE_LOCALE_HELP' => 'Přepíše lokalitu nastavenou v PHP podle aktuálního jazyku',
            'REDIRECT' => 'Přesměrování stránky',
            'REDIRECT_HELP' => 'Zadejte cestu stránky nebo externí URL, kam bude tato stránka přesměrována. Např. `/nějaká/cesta` nebo `http://nějakýweb.com`',
            'PLUGIN_STATUS' => 'Stav doplňku',
            'INCLUDE_DEFAULT_LANG' => 'Zahrnout výchozí jazyk',
            'INCLUDE_DEFAULT_LANG_HELP' => 'Předřadí všem adresám URL ve výchozím jazyce výchozí jazyk. Např.: `/en/blog/my-post`',
            'INCLUDE_DEFAULT_LANG_FILE_EXTENSION' => 'Zahrnout výchozí jazyk do přípony souboru',
            'INCLUDE_DEFAULT_LANG_HELP_FILE_EXTENSION' => 'Pokud je povoleno, přidá výchozí jazyk do přípony souboru (např. `.en.md`). Zakažte jej pro zachování výchozího přípony souboru `.md` bez vyjádření jazyka.',
            'PAGES_FALLBACK_ONLY' => 'Stránky jsou pouze záložní',
            'PAGES_FALLBACK_ONLY_HELP' => 'Pouze "záložní" pro vyhledání obsahu stránky pomocí podporovaných jazyků, výchozím chováním je zobrazení jakéhokoli nalezeného jazyka, pokud chybí aktivní jazyk',
            'ALLOW_URL_TAXONOMY_FILTERS' => 'Filtry taxonomie v URL',
            'ALLOW_URL_TAXONOMY_FILTERS_HELP' => 'Umožňuje filtrovat stránky pomocí taxonomií jako `/taxonomie:hodnota`.',
            'REDIRECT_DEFAULT_CODE' => 'Výchozí kód přesměrování',
            'REDIRECT_DEFAULT_CODE_HELP' => 'Nastaví HTTP status kód použitý pro všechna přesměrování',
            'IGNORE_HIDDEN' => 'Ignorovat skryté',
            'IGNORE_HIDDEN_HELP' => 'Ignorovat všechny soubory, které začínají tečkou',
            'WRAPPED_SITE' => 'Součást jiného webu',
            'WRAPPED_SITE_HELP' => 'Pro šablony/doplňky, aby věděly, zda je Grav zapouzdřen další platformou',
            'FALLBACK_TYPES' => 'Povolené typy souborů',
            'FALLBACK_TYPES_HELP' => 'Povolené typy souborů, které jsou přístupné prostřednictvím cesty stránky. Ve výchozím nastavení jsou podporovány všechny typy médií.',
            'INLINE_TYPES' => 'Typy souborů vložené přímo do stránky',
            'INLINE_TYPES_HELP' => 'Typy souborů, které mají být zobrazeny přímo ve stránce namísto stahování',
            'APPEND_URL_EXT' => 'Přidat za URL příponu',
            'APPEND_URL_EXT_HELP' => 'Přidá vlastní připonu do URL stránky. To znamená, že Grav bude hledat šablonu s názvem `<template>.<extension>.twig`',
            'PAGE_MODES' => 'Mód stránky',
            'PAGE_TYPES' => 'Typ stránky',
            'PAGE_TYPES_HELP' => 'Určuje typy stránek, které Grav podporuje, a pořadí určuje, na který typ se vrátí, pokud je požadavek nejednoznačný',
            'ACCESS_LEVELS' => 'Úrovně přístupu',
            'GROUPS' => 'Skupiny',
            'GROUPS_HELP' => 'Seznam skupin uživatele',
            'ADMIN_ACCESS' => 'Přístup správce',
            'SITE_ACCESS' => 'Přístup na web',
            'INVALID_SECURITY_TOKEN' => 'Neplatný bezpečnostní token',
            'ACTIVATE' => 'Povolit',
            'TWIG_UMASK_FIX' => 'Umask Fix',
            'TWIG_UMASK_FIX_HELP' => 'Ve výchozím nastavení Twig vytváří cachovací soubory s oprávněním 0755, po zapnutí bude nastavovat 0775',
            'CACHE_PERMS' => 'Oprávnění cache',
            'CACHE_PERMS_HELP' => 'Výchozí nastavení oprávnění adresářů. Většinou 0755 nebo 0775 podle konfigurace serveru',
            'REMOVE_SUCCESSFUL' => 'Úspěšně odstraněno',
            'REMOVE_FAILED' => 'Odstranění se nezdařilo',
            'HIDE_HOME_IN_URLS' => 'Skrýt cestu domovské stránky v URL',
            'HIDE_HOME_IN_URLS_HELP' => 'Zajistí, že výchozí cesty pro všechny stránky umístěné pod domovskou stránkou neobsahují cestu domovské stránky',
            'TWIG_FIRST' => 'Zpracovat nejdříve Twigem',
            'TWIG_FIRST_HELP' => 'Pokud povolíte zpracovávání stránek Twigem, můžete upravit stránku ještě před zpracování markdownem',
            'SESSION_SECURE' => 'Bezpečnost',
            'SESSION_SECURE_HELP' => 'Pokud zapnete, musí být veškerá komunikace identifikovaná touto cookie prováděna přes HTTPS. POZOR: Zapněte pouze pro weby, které běží pouze na HTTPS',
            'SESSION_HTTPONLY' => 'Pouze HTTP',
            'SESSION_HTTPONLY_HELP' => 'Pokud zapnete, cookie mohou být použity pouze přes HTTP a nemohou být upraveny pomocí JavaScriptu',
            'REVERSE_PROXY' => 'Reverzní proxy',
            'REVERSE_PROXY_HELP' => 'Zapněte, pokud jste za reverzní proxy and máte problémy s URL obsahující nesprávné porty',
            'INVALID_FRONTMATTER_COULD_NOT_SAVE' => 'Neplatná hlavička, nelze uložit',
            'ADD_FOLDER' => 'Přidat složku',
            'COPY_PAGE' => 'Kopírovat stránku',
            'PROXY_URL' => 'Adresa proxy serveru',
            'PROXY_URL_HELP' => 'Zadejte server proxy hostitele nebo adresu IP a PORT',
            'PROXY_CERT' => 'Cesta k proxy certifikátu',
            'PROXY_CERT_HELP' => 'Místní cesta ke složce obsahující soubor pem proxy certifikátu',
            'NOTHING_TO_SAVE' => 'Nic k uložení',
            'FILE_ERROR_ADD' => 'Došlo k chybě při pokusu o přidání souboru',
            'FILE_ERROR_UPLOAD' => 'Nastala chyba při nahrávání souborů',
            'FILE_UNSUPPORTED' => 'Nepodporovaný typ souboru',
            'ADD_ITEM' => 'Přidat položku',
            'FILE_TOO_LARGE' => 'Soubor je příliš velký. Maximální povolená velikost je %s.<br> Zvětšete prosím `post_max_size` v nastavení PHP',
            'INSTALLING' => 'Probíhá instalace',
            'LOADING' => 'Probíhá načítání..',
            'DEPENDENCIES_NOT_MET_MESSAGE' => 'Nejprve je třeba nainstalovat následující komponenty:',
            'ERROR_INSTALLING_PACKAGES' => 'Nastala chyba při instalování bálíčků',
            'INSTALLING_DEPENDENCIES' => 'Instalování komponentů...',
            'INSTALLING_PACKAGES' => 'Probíhá instalace balíčků..',
            'PACKAGES_SUCCESSFULLY_INSTALLED' => 'Balíčky byly úspěšně nainstalovány.',
            'READY_TO_INSTALL_PACKAGES' => 'Vše připraveno na instalaci balíčků',
            'PACKAGES_NOT_INSTALLED' => 'Balíčky nenainstalovány',
            'PACKAGES_NEED_UPDATE' => 'Již nainstalované balíčky, které jsou příliš staré',
            'PACKAGES_SUGGESTED_UPDATE' => 'Balíčky nainstalované ve verzi, která je v pořádku, budou aktualizovány na nejnovější verzi',
            'REMOVE_THE' => 'Odstraňte %s',
            'CONFIRM_REMOVAL' => 'Opravdu chcete smazat %s?',
            'REMOVED_SUCCESSFULLY' => '%s úspěšně odstraněn',
            'ERROR_REMOVING_THE' => 'Nastala chyba při odstraňování %s',
            'ADDITIONAL_DEPENDENCIES_CAN_BE_REMOVED' => '%s vyžadoval následující komponenty, které nejsou vyžadovány ostatními nainstalovanými balíčky. Pokud je nepoužíváte, můžete je přímo zde hned odstranit.',
            'READY_TO_UPDATE_PACKAGES' => 'Vše připraveno na aktualizaci balíčků',
            'ERROR_UPDATING_PACKAGES' => 'Nastala chyba při aktualizaci bálíčků',
            'UPDATING_PACKAGES' => 'Probíhá aktualizování balíčků..',
            'PACKAGES_SUCCESSFULLY_UPDATED' => 'Balíčky byly úspěšně aktualizovány.',
            'UPDATING' => 'Probíhá aktualizace',
            'GPM_SECTION' => 'Sekce GPM',
            'GPM_RELEASES' => 'Verze GPM',
            'GPM_RELEASES_HELP' => 'Zvolte \'Testovací\' pro instalaci beta nebo testovací verze',
            'GPM_METHOD' => 'Metoda Remote Fetch',
            'GPM_METHOD_HELP' => 'Pokud nastaveno na Auto, Grav zjistí, jestli je fopen povoleno a použije jej, jinak se vrátí zpět k používání cURL. K vynucení použití jednoho či druhého přepněte nastavení.',
            'HTTP_SECTION' => 'Sekce HTTP',
            'SSL_ENABLE_PROXY' => 'Povolit proxy server',
            'SSL_VERIFY_PEER' => 'Vzdálené ověření Peer (SSL)',
            'SSL_VERIFY_PEER_HELP' => 'Některé mohou selhat při ověřování SSL certifikátů',
            'SSL_VERIFY_HOST' => 'Vzdálené ověření hostitele',
            'SSL_VERIFY_HOST_HELP' => 'Některé mohou selhat při ověřování SSL certifikátů',
            'HTTP_CONNECTIONS' => 'HTTP připojení',
            'HTTP_CONNECTIONS_HELP' => 'Počet souběžných připojení HTTP během multiplexovaných požadavků',
            'MISC_SECTION' => 'Ostatní nastavení',
            'AUTO' => 'Auto',
            'FOPEN' => 'fopen',
            'CURL' => 'cURL',
            'STABLE' => 'Stabilní',
            'TESTING' => 'Testovací',
            'FRONTMATTER_PROCESS_TWIG' => 'Zpracovat Twig v hlavičce',
            'FRONTMATTER_PROCESS_TWIG_HELP' => 'Pokud je povoleno, můžete použít konfigurační proměnné Twigu v hlavičce stránky',
            'FRONTMATTER_IGNORE_FIELDS' => 'Ignorovat pole v hlavičce',
            'FRONTMATTER_IGNORE_FIELDS_HELP' => 'Některá pole v hlavičce mohou obsahovat Twig, ale neměla by být zpracována, například "formuláře"',
            'FRONTMATTER_IGNORE_FIELDS_PLACEHOLDER' => 'např. formuláře',
            'PACKAGE_X_INSTALLED_SUCCESSFULLY' => 'Balíček %s byl úspěšně nainstalován',
            'ORDERING_DISABLED_BECAUSE_PARENT_SETTING_ORDER' => 'Pořadí nadřazeného nastavení, řazení zakázáno',
            'ORDERING_DISABLED_BECAUSE_PAGE_NOT_VISIBLE' => 'Stránka není viditelná, řazení zakázáno',
            'ORDERING_DISABLED_BECAUSE_TOO_MANY_SIBLINGS' => 'Řazení přes admin není podporováno, protože existuje více než 200 sourozenců',
            'ORDERING_DISABLED_BECAUSE_PAGE_NO_PREFIX' => 'Řazení stránek je zakázáno pro tuto stánku, protože <strong>Číselná předpona složky</strong> není povolena',
            'CANNOT_ADD_MEDIA_FILES_PAGE_NOT_SAVED' => 'Poznámka: Nelze přidat mediální soubory, dokud neuložíte na stránku. Stačí kliknout na \'Uložit\' na horní části stránky',
            'CANNOT_ADD_FILES_PAGE_NOT_SAVED' => 'POZNÁMKA: Musíte stránku uložit před nahráním souborů.',
            'DROP_FILES_HERE_TO_UPLOAD' => 'Přetáhněte sem své soubory nebo <strong>klikněte na tuto plochu</strong>',
            'INSERT' => 'Vložit',
            'UNDO' => 'Odvolat',
            'REDO' => 'Opakovat',
            'HEADERS' => 'Nadpisy',
            'BOLD' => 'Tučně',
            'ITALIC' => 'Kurzíva',
            'STRIKETHROUGH' => 'Přeškrtnout',
            'SUMMARY_DELIMITER' => 'Oddělovač shnutí',
            'LINK' => 'Odkaz',
            'IMAGE' => 'Obrázek',
            'BLOCKQUOTE' => 'Citace',
            'UNORDERED_LIST' => 'Nečíslovaný seznam',
            'ORDERED_LIST' => 'Číslovaný seznam',
            'EDITOR' => 'Editor',
            'PREVIEW' => 'Náhled',
            'FULLSCREEN' => 'Na celou obrazovku',
            'NON_ROUTABLE' => 'Nepřístupná',
            'NON_VISIBLE' => 'Neviditelná',
            'NON_PUBLISHED' => 'Nezveřejněná',
            'CHARACTERS' => 'znaků',
            'PUBLISHING' => 'Zveřejnění',
            'MEDIA_TYPES' => 'Typy médií',
            'IMAGE_OPTIONS' => 'Možnosti obrázku',
            'MIME_TYPE' => 'MIME typ',
            'THUMB' => 'Náhled',
            'TYPE' => 'Typ',
            'FILE_EXTENSION' => 'Přípona souboru',
            'LEGEND' => 'Legenda stránky',
            'MEMCACHE_SERVER' => 'Server memcache',
            'MEMCACHE_SERVER_HELP' => 'Adresa Memcache serveru',
            'MEMCACHE_PORT' => 'Memcached port',
            'MEMCACHE_PORT_HELP' => 'Port serveru Memcached',
            'MEMCACHED_SERVER' => 'Memcached server',
            'MEMCACHED_SERVER_HELP' => 'Adresu serveru Memcached',
            'MEMCACHED_PORT' => 'Memcached port',
            'MEMCACHED_PORT_HELP' => 'Port serveru Memcached',
            'REDIS_SERVER' => 'Redis server',
            'REDIS_SERVER_HELP' => 'Adresa Redis serveru',
            'REDIS_PORT' => 'Redis port',
            'REDIS_PORT_HELP' => 'Port Redis serveru',
            'REDIS_PASSWORD' => 'Heslo a tajný kód pro Redis',
            'REDIS_DATABASE' => 'ID Redis databáze',
            'REDIS_DATABASE_HELP' => 'ID instance databáze Redis',
            'ALL' => 'Vše',
            'FROM' => 'od',
            'TO' => 'do',
            'RELEASE_DATE' => 'Datum vydání',
            'SORT_BY' => 'Seřadit podle',
            'RESOURCE_FILTER' => 'Filtr...',
            'FORCE_SSL' => 'Vynutit SSL',
            'FORCE_SSL_HELP' => 'Globálně vynutit SSL, je-li povoleno, web dosažený prostřednictvím protokolu HTTP, Grav přesměruje na HTTPS stránku',
            'NEWS_FEED' => 'Novinky',
            'EXTERNAL_URL' => 'Externí adresa URL',
            'SESSION_SAMESITE' => 'Atribut SameSite relace',
            'SESSION_SAMESITE_HELP' => 'Lax|Strict|None. Více informací naleznete na https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite',
            'CUSTOM_BASE_URL' => 'Vlastní základní adresa URL',
            'CUSTOM_BASE_URL_HELP' => 'Užívejte, chcete-li přepsat doménu stránky nebo užít jinou podsložku, než která je v Grav užívaná. Například http://localhost',
            'FILEUPLOAD_PREVENT_SELF' => 'Nelze použít "%s" mimo stránky.',
            'FILEUPLOAD_UNABLE_TO_UPLOAD' => 'Nepodařilo se nahrát soubor %s: %s',
            'FILEUPLOAD_UNABLE_TO_MOVE' => 'Nepodařilo přesunout soubor %s do "%s"',
            'DROPZONE_CANCEL_UPLOAD' => 'Zrušit nahrávání',
            'DROPZONE_CANCEL_UPLOAD_CONFIRMATION' => 'Opravdu si přejete toto nahrávání zrušit?',
            'DROPZONE_DEFAULT_MESSAGE' => 'Přetáhněte sem své soubory nebo <strong>klikněte na tuto plochu</strong>',
            'DROPZONE_FALLBACK_MESSAGE' => 'Váš prohlížeč nepodporuje "drag & drop" nahrávání souborů.',
            'DROPZONE_FALLBACK_TEXT' => 'Použijte, prosím, záložní formulář níže pro nahrávání souborů.',
            'DROPZONE_FILE_TOO_BIG' => 'Soubor je příliš velký ({{filesize}}MiB). Maximální velikost souboru: {{maxFilesize}}MiB.',
            'DROPZONE_INVALID_FILE_TYPE' => 'Nelze nahrát soubory tohoto typu.',
            'DROPZONE_MAX_FILES_EXCEEDED' => 'Nelze nahrát žádné další soubory.',
            'DROPZONE_REMOVE_FILE' => 'Odstranit soubor',
            'DROPZONE_RESPONSE_ERROR' => 'Server odpověděl kódem {{statusCode}}.',
            'PREMIUM_PRODUCT' => 'Prémium',
            'DESTINATION_NOT_SPECIFIED' => 'Cíl není zadán',
            'UPLOAD_ERR_NO_TMP_DIR' => 'Chybí dočasná složka',
            'SESSION_SPLIT' => 'Rozdělit relaci',
            'SESSION_SPLIT_HELP' => 'Nezávisle rozdělené relace mezi webem a dalšími doplňky (jako admin)',
            'ERROR_FULL_BACKTRACE' => 'Úplná chybová hlášení',
            'ERROR_SIMPLE' => 'Jednoduchá chyba',
            'ERROR_SYSTEM' => 'Systémová chyba',
            'IMAGES_AUTO_FIX_ORIENTATION' => 'Opravit orientaci automaticky',
            'IMAGES_AUTO_FIX_ORIENTATION_HELP' => 'Automaticky opravovat natočení obrázku podle EXIF data souboru',
            'REDIS_SOCKET' => 'Redis socket',
            'REDIS_SOCKET_HELP' => 'Redis socket',
            'NOT_SET' => 'Nenastaveno',
            'PERMISSIONS' => 'Přístupová práva',
            'NEVER_CACHE_TWIG' => 'Nikdy nacachovat Twig',
            'NEVER_CACHE_TWIG_HELP' => 'Do mezipaměti ukládat jen obsah a Zpracovat Twig při každém načtení. Bude ignorovat nastavení twig_first.',
            'ALLOW_WEBSERVER_GZIP' => 'Povolit WebServer Gzip',
            'ALLOW_WEBSERVER_GZIP_HELP' => 'Ve výchozím nastavení vypnuto. Je-li povolena, komprese Gzip/Deflate bude fungovat, ale připojení http nebudou uzavřeny před událostí onShutDown(), a tím způsobuje pomalejší načítání stránek',
            'OFFLINE_WARNING' => 'Nelze navázat spojení s GPM',
            'CLEAR_IMAGES_BY_DEFAULT' => 'Vyčistit mezipaměť obrázků jako výchozí',
            'CLEAR_IMAGES_BY_DEFAULT_HELP' => 'Ve výchozím nastavení jsou obrázky z mezipaměti odstraněny při jakémkoli její čištění. To může být vypnuto',
            'CLI_COMPATIBILITY' => 'CLI kompatibilita',
            'CLI_COMPATIBILITY_HELP' => 'Zajišťuje, že budou použity jen stabilní mezipamětní ovladače (soubor, redis, memcache, atd.)',
            'REINSTALL_PLUGIN' => 'Přeinstalovat doplněk',
            'REINSTALL_THEME' => 'Přeinstalovat šablonu',
            'REINSTALL_THE' => 'Reinstalovat %s',
            'CONFIRM_REINSTALL' => 'Jste si jistí, že chcete %s přeinstalovat?',
            'REINSTALLED_SUCCESSFULLY' => '%s byl úspěšně přeinstalován',
            'ERROR_REINSTALLING_THE' => 'Chyba při reinstalaci %s',
            'PACKAGE_X_REINSTALLED_SUCCESSFULLY' => 'Balíček %s byl úspěšně přeinstalován',
            'REINSTALLATION_FAILED' => 'Chyba při přeinstalaci',
            'WARNING_REINSTALL_NOT_LATEST_RELEASE' => 'Není nainstalovaná nejnovější verze. Klepnutím na Pokračovat odeberete současnou verzi a nainstalujete nejnověšjí dostupnou verzi',
            'TOOLS' => 'Nástroje',
            'DIRECT_INSTALL' => 'Přímá instalace',
            'NO_PACKAGE_NAME' => 'Nespecifikovaný název balíčku',
            'PACKAGE_EXTRACTION_FAILED' => 'Extrakce baličku se nezdařila',
            'NOT_VALID_GRAV_PACKAGE' => 'Není platným balíčkem Grav',
            'NAME_COULD_NOT_BE_DETERMINED' => 'Jméno nemohlo být určeno',
            'CANNOT_OVERWRITE_SYMLINKS' => 'Nelze přepsat symbolické odkazy',
            'ZIP_PACKAGE_NOT_FOUND' => 'ZIP soubor nebyl nalezen',
            'GPM_OFFICIAL_ONLY' => 'Pouze oficiální GPM',
            'GPM_OFFICIAL_ONLY_HELP' => 'Povolit pouze přímé instalace z oficiálního repozitáře GPM.',
            'NO_CHILD_TYPE' => 'Žádný odvozený typ pro zdrojové směrování',
            'SORTABLE_PAGES' => 'Tříditelné stánky:',
            'UNSORTABLE_PAGES' => 'Netříditelné stránky',
            'ADMIN_SPECIFIC_OVERRIDES' => 'Specifická přepsání admina',
            'ADMIN_CHILDREN_DISPLAY_ORDER' => 'Pořadí zobrazení dětí',
            'ADMIN_CHILDREN_DISPLAY_ORDER_HELP' => 'Pořadí v jakém bude odvozená stránka zobrazena v pohledu \'Strany\' správcovského zásuvného modulu',
            'PWD_PLACEHOLDER' => 'komplexní řetězec nejméně 8 znaků dlouhý',
            'PWD_REGEX' => 'Regex hesla',
            'PWD_REGEX_HELP' => 'Výchozí: Hesla musí obsahovat minimálně jedno číslo, jedno velké a malé písmeno a jejich délka musí být nejméně 8 znaků',
            'USERNAME_PLACEHOLDER' => 'jen malá písmena, např. \'admin\'',
            'USERNAME_REGEX' => 'Regex uživatelského jména',
            'USERNAME_REGEX_HELP' => 'Výchozí: jen malá písmena, znaky, číslice, pomlčky a podtržítka. 3 - 16 znaků',
            'ENABLE_AUTO_METADATA' => 'Automatická metadata z Exif',
            'ENABLE_AUTO_METADATA_HELP' => 'Automaticky generovat soubory matadat pro obrázky s Exif informacemi',
            '2FA_TITLE' => 'Dvoufaktorové ověření',
            '2FA_INSTRUCTIONS' => '##### Dvoufaktorové ověřování
Máte na svém účtu aktivováno **dvoufaktorové ověřování**. Pro dokončení přihlášení použijte svou aplikaci pro **dvoufaktorové ověřování ** a zadejte **šestimístný kód**.',
            '2FA_REGEN_HINT' => 'Obnovení tajného kódu bude vyžadovat aktualizaci vaší autentifikační aplikace',
            '2FA_LABEL' => 'Přístup správce',
            '2FA_FAILED' => 'Špatně zadaný kód dvoufaktorového ověření, zkuste to znovu.',
            '2FA_ENABLED' => 'Dvoufaktorové ověření povoleno',
            '2FA_CODE_INPUT' => '000000',
            '2FA_SECRET' => 'Tajný kód dvoufaktorového ověření',
            '2FA_SECRET_HELP' => 'Naskenujte tento QR kód do vaší [Autentifikační aplikace](https://learn.getgrav.org/admin-panel/2fa#apps). Důležité je také uložit tajný kód na bezpečném místě, pokud bude zapotřebí aplikaci přeinstalovat. Pro více informací se podívejte do [dokumentace Grav](https://learn.getgrav.org/admin-panel/2fa)',
            '2FA_REGENERATE' => 'Přegenerovat',
            'YUBIKEY_ID' => 'YubiKey ID',
            'YUBIKEY_OTP_INPUT' => 'YubiKey OTP',
            'YUBIKEY_HELP' => 'Vložte svůj YubiKey do počítače a klikněte na tlačítko pro vygenerování OTP. Prvních 12 znaků je vaše ID klienta a bude uloženo.',
            'FORCE_LOWERCASE_URLS' => 'Vynutit malá písmena v URL',
            'FORCE_LOWERCASE_URLS_HELP' => 'Ve výchozím nastavení Grav nastaví všechny slugy a cesty na malá písmena. Změnou nastavení na hodnotu false, mohou slugy a cesty obsahovat Velká Písmena',
            'INTL_ENABLED' => 'Integrace modulu Intl',
            'INTL_ENABLED_HELP' => 'Použijte modul Intl PHP a porovnávejte třídění s kolekcemi založenými na UTF8',
            'VIEW_SITE_TIP' => 'Zobrazit web',
            'TOOLS_DIRECT_INSTALL_TITLE' => 'Přímá instalace balíčků Grav',
            'TOOLS_DIRECT_INSTALL_UPLOAD_TITLE' => 'Instalovat balíček prostřednictvím staženého ZIP souboru',
            'TOOLS_DIRECT_INSTALL_UPLOAD_DESC' => 'Můžete snadno nainstalovat platnou Grav <strong>šablonu</strong>, platný <strong>doplněk</strong> nebo dokonce <strong>Grav</strong> aktualizační balíček Zip prostřednictvím této metody. Tento balíček nemusí být registrován prostřednictvím GPM a umožňuje vám snadno vrátit se k předchozí verzi nebo vytvořit instalaci pro testování.',
            'TOOLS_DIRECT_INSTALL_URL_TITLE' => 'Instalovat balíček prostřednictvím odkazu vzdálené adresy URL',
            'TOOLS_DIRECT_INSTALL_URL_DESC' => 'Případně můžete také odkázat na úplnou adresu URL k balíčku ZIP souboru a nainstalovat jej prostřednictvím této vzdálené adresy URL.',
            'TOOLS_DIRECT_INSTALL_UPLOAD_BUTTON' => 'Nahrát a instalovat',
            'ROUTE_OVERRIDES' => 'Přepsání cesty',
            'ROUTE_DEFAULT' => 'Výchozí cesta',
            'ROUTE_CANONICAL' => 'Kanonická cesta',
            'ROUTE_ALIASES' => 'Aliasy cesty',
            'OPEN_NEW_TAB' => 'Otevřít v nové záložce',
            'SESSION_INITIALIZE' => 'Inicializovat relaci',
            'SESSION_INITIALIZE_HELP' => 'Přinutí Grav k nastartování sezení. Tato funkce je zapotřebí pro jakoukoliv interakci s uživatelem jako je např. přihlášení, formuláře apod. Admin rozšíření není tímto nastavením ovlivněno.',
            'STRICT_YAML_COMPAT' => 'YAML kompatibilita',
            'STRICT_YAML_COMPAT_HELP' => 'Přejít zpět na Symfony 2.4 YAML parser, pokud nativní nebo 3.4 parser selžou',
            'STRICT_TWIG_COMPAT' => 'Twig kompatibilita',
            'STRICT_TWIG_COMPAT_HELP' => 'Povolí zastaralé nastavení Twig autoescape.  Pokud je zakázáno, filter |raw je požadován pro HTML výstup, protože Twig bude výstup automaticky ukončovat',
            'SCHEDULER' => 'Plánovač',
            'SCHEDULER_INSTALL_INSTRUCTIONS' => 'Návod k instalaci',
            'SCHEDULER_INSTALLED_READY' => 'Nainstalováno a připraveno',
            'SCHEDULER_CRON_NA' => 'Cron není k dispozici pro uživatele: <b>%s</b>',
            'SCHEDULER_NOT_ENABLED' => 'Není povoleno pro uživatele: <b>%s</b>',
            'SCHEDULER_SETUP' => 'Nastavení plánovače',
            'SCHEDULER_INSTRUCTIONS' => '<b>Grav plánovač</b> umožňuje vytvořit a naplánovat vlastní úlohy. Poskytuje také metodu pro Grav doplňky k programové integraci a dynamickému přidávaní úloh, které mají být spouštěny v pravidelných intervalech.',
            'SCHEDULER_POST_INSTRUCTIONS' => 'Pro povolení funkce plánovače, musíte přidat <b>Grav plánovač</b> do souboru crontab vašeho systému pro uživatele <b>%s</b> . Spusťte příkaz výše z terminálu a přidejte jej automaticky. Po uložení obnovte tuto stránku pro zobrazení stavu.',
            'SCHEDULER_JOBS' => 'Vlastní úlohy plánování',
            'SCHEDULER_STATUS' => 'Stav plánovače',
            'SCHEDULER_RUNAT' => 'Spustit v',
            'SCHEDULER_RUNAT_HELP' => 'Syntaxe \'at\' formátu plánovače. POZNÁMKA: Všechny časy jsou v UTC!',
            'SCHEDULER_OUTPUT' => 'Výstupní soubor',
            'SCHEDULER_OUTPUT_HELP' => 'Cesta/název výstupního souboru (z kořenové složky instalace Gravu)',
            'SCHEDULER_OUTPUT_TYPE' => 'Typ výstupu',
            'SCHEDULER_OUTPUT_TYPE_HELP' => 'Buď připojit ke stejnému souboru každé spuštění, nebo přepsat soubor s každým spuštěním',
            'SCHEDULER_EMAIL' => 'E-mail',
            'SCHEDULER_EMAIL_HELP' => 'E-mail pro odeslání výstupu. Poznámka: vyžaduje nastavit výstupní soubor',
            'SCHEDULER_WARNING' => 'Plánovač používá ke spuštění příkazů crontab vašeho systému. Měli byste toto použít pouze v případě, že jste pokročilý uživatel a víte, co děláte. Špatná konfigurace nebo zneužití mohou vést k bezpečnostním problémům.',
            'SECURITY' => 'Zabezpečení',
            'XSS_SECURITY' => 'XSS zabezpečení obsahu',
            'XSS_WHITELIST_PERMISSIONS' => 'Povolená oprávnění',
            'XSS_WHITELIST_PERMISSIONS_HELP' => 'Uživatelé s těmito oprávněními přeskočí XSS pravidla při ukládání obsahu',
            'XSS_ON_EVENTS' => 'Filtr událostí',
            'XSS_INVALID_PROTOCOLS' => 'Filtr neplatných protokolů',
            'XSS_INVALID_PROTOCOLS_LIST' => 'Seznam neplatných protokolů',
            'XSS_MOZ_BINDINGS' => 'Filtr Moz vazeb',
            'XSS_HTML_INLINE_STYLES' => 'Filtr HTML inline stylů',
            'XSS_DANGEROUS_TAGS' => 'Filtr nebezpečných značek HTML',
            'XSS_DANGEROUS_TAGS_LIST' => 'Seznam nebezpečných značek HTML',
            'XSS_ONSAVE_ISSUE' => 'Ukládání selhalo: zjištěn XSS problém...',
            'XSS_ISSUE' => '<strong>UPOZORNĚNÍ:</strong>Grav nalezl potenciální XSS problémy v <strong>%s</strong>',
            'UPLOADS_SECURITY' => 'Bezpečnost nahrávání',
            'UPLOADS_DANGEROUS_EXTENSIONS' => 'Nebezpečná rozšíření',
            'UPLOADS_DANGEROUS_EXTENSIONS_HELP' => 'Blokovat tato rozšíření při nahrávání navzdory povoleným MIME typům',
            'REPORTS' => 'Zprávy',
            'LOGS' => 'Protokoly',
            'LOG_VIEWER_FILES' => 'Prohlížeč souborů protokolu',
            'LOG_VIEWER_FILES_HELP' => 'Soubory v /logs/, které budou k dispozici pro zobrazení v nabídce Nástroje - Protokoly. Například "grav" = /logs/grav.log',
            'BACKUPS_STORAGE_PURGE_TRIGGER' => 'Spouštěč vymazání úložiště záloh',
            'BACKUPS_MAX_COUNT' => 'Maximální počet záloh',
            'BACKUPS_MAX_COUNT_HELP' => '0 je neomezené',
            'BACKUPS_MAX_SPACE' => 'Maximální prostor pro zálohy',
            'BACKUPS_MAX_RETENTION_TIME' => 'Maximální doba uchování',
            'BACKUPS_MAX_RETENTION_TIME_APPEND' => 've dnech',
            'BACKUPS_PROFILE_NAME' => 'Název zálohy',
            'BACKUPS_PROFILE_ROOT_FOLDER' => 'Kořenová složka',
            'BACKUPS_PROFILE_ROOT_FOLDER_HELP' => 'Může být absolutní cesta nebo proud',
            'BACKUPS_PROFILE_EXCLUDE_PATHS' => 'Vyloučit cesty',
            'BACKUPS_PROFILE_EXCLUDE_PATHS_HELP' => 'Absolutní cesty k vyloučení, jedna na řádek',
            'BACKUPS_PROFILE_EXCLUDE_FILES' => 'Vyloučit soubory',
            'BACKUPS_PROFILE_EXCLUDE_FILES_HELP' => 'Konkrétní soubory nebo složky k vyloučení, jeden/jedna na řádek',
            'BACKUPS_PROFILE_SCHEDULE' => 'Povolit naplánovanou úlohu',
            'BACKUPS_PROFILE_SCHEDULE_AT' => 'Spustit naplánovanou úlohu',
            'COMMAND' => 'Příkaz',
            'EXTRA_ARGUMENTS' => 'Další argumenty',
            'DEFAULT_LANG' => 'Přepsat výchozí jazyk',
            'DEFAULT_LANG_HELP' => 'Výchozí je první podporovaný jazyk. Toto může být přepsáno nastavením této možnosti, ale musí to být jeden z podporovaných jazyků',
            'DEBUGGER_PROVIDER' => 'Poskytovatel ladicího programu',
            'DEBUGGER_PROVIDER_HELP' => 'Výchozí je PHP Debug Bar, ale rozšíření prohlížeče Clockwork poskytuje méně rušivý přístup',
            'DEBUGGER_DEBUGBAR' => 'PHP Debug panel',
            'DEBUGGER_CLOCKWORK' => 'Rozšíření prohlížeče Clockwork',
            'PAGE_ROUTE_NOT_FOUND' => 'Cesta ke stránce nebyla nalezena',
            'PAGE_ROUTE_FOUND' => 'Cesta ke stránce nalezena',
            'NO_ROUTE_PROVIDED' => 'Nebyla zadána cesta',
            'CONTENT_LANGUAGE_FALLBACKS' => 'Záložní jazyk obsahu',
            'CONTENT_LANGUAGE_FALLBACKS_HELP' => 'Ve výchozím nastavení, pokud obsah není přeložen, Grav zobrazí obsah ve výchozím jazyce. Použijte toto nastavení pro přepsání chování podle jazyka.',
            'CONTENT_LANGUAGE_FALLBACK' => 'Záložní jazyky',
            'CONTENT_LANGUAGE_FALLBACK_HELP' => 'Zadejte prosím seznam kódů jazyka. Vezměte prosím na vědomí, že pokud vynecháte výchozí kód jazyka, nebude použit.',
            'CONTENT_FALLBACK_LANGUAGE_HELP' => 'Zadejte kód jazyka, který chcete upravit.',
            'EXPERIMENTAL' => 'Experimentální',
            'PAGES_TYPE' => 'Typ stránky veřejné části',
            'PAGES_TYPE_HELP' => 'Tato volba umožňuje využití objektů Flex Object na front-endu. Admin Flex stránky vyžadují plugin Flex objektů',
            'ACCOUNTS_TYPE' => 'Typy účtů',
            'ACCOUNTS_TYPE_HELP' => 'Sytém Flex Objects ukládá uživatelské účty',
            'ACCOUNTS_STORAGE' => 'Úložiště účtů',
            'ACCOUNTS_STORAGE_HELP' => 'Mechanismus pro ukládání, který se použije pro typ účtu objektu Flex. Soubory jsou tradiční přístup, kde je účet uložen v YAML souboru v jediné složce, zatímco Složka vytváří novou složku pro každý účet',
            'FLEX' => 'Objekt Flex (EXPERIMENTÁLNÍ)',
            'REGULAR' => 'Běžný',
            'FILE' => 'Soubor',
            'SANITIZE_SVG' => 'Vyčistit SVG',
            'SANITIZE_SVG_HELP' => 'Odstraní jakýkoliv XSS kód ze SVG',
            'ACCOUNTS' => 'Účty',
            'USER_ACCOUNTS' => 'Uživatelské účty',
            'USER_GROUPS' => 'Uživatelské skupiny',
            'GROUP_NAME' => 'Název skupiny',
            'DISPLAY_NAME' => 'Zobrazované jméno',
            'ICON' => 'Ikona',
            'ACCESS' => 'Přístup',
            'NO_ACCESS' => 'Bez přístupu',
            'SUPER_USER' => 'Super uživatel',
            'ALLOWED' => 'Povoleno',
            'DENIED' => 'Zakázáno',
            'MODULE' => 'Modulární',
            'NON_MODULE' => 'Nemodulární',
            'ADD_MODULE' => 'Přidat modul',
            'MODULE_SETUP' => 'Nastavení modulu',
            'MODULE_TEMPLATE' => 'Šablona modulu',
            'ADD_MODULE_CONTENT' => 'Přidat obsah modulu',
            'CHANGELOG' => 'Přehled změn',
            'PAGE_ACCESS' => 'Přístup ke stránce',
            'PAGE PERMISSIONS' => 'Oprávnění stránky',
            'PAGE_ACCESS_HELP' => 'Uživatel s následujícími přístupovými oprávněními může vstoupit na stránku.',
            'PAGE_VISIBILITY_REQUIRES_ACCESS' => 'Viditelnost nabídky vyžaduje přístup',
            'PAGE_VISIBILITY_REQUIRES_ACCESS_HELP' => 'Nastavte Ano pokud má být stránka zobrazena v nabídce pouze v případě, že k ní má uživatel přístup.',
            'PAGE_INHERIT_PERMISSIONS' => 'Zdědit oprávnění',
            'PAGE_INHERIT_PERMISSIONS_HELP' => 'Zdědit přístup z nadřazené stránky.',
            'PAGE_AUTHORS' => 'Autoři stránky',
            'PAGE_AUTHORS_HELP' => 'Členové skupiny Autoři stránek mají přístup na tuto stránku jako vlastník definovaný ve speciální skupině \'Autoři\'.',
            'PAGE_GROUPS' => 'Skupiny stránek',
            'PAGE_GROUPS_HELP' => 'Členové stránkových skupin mají zvláštní přístup k této stránce.',
            'READ' => 'Číst',
            'PUBLISH' => 'Zveřejnit',
            'LIST' => 'Seznam',
            'ACCESS_SITE' => 'Web',
            'ACCESS_SITE_LOGIN' => 'Přihlášení ke stránkám',
            'ACCESS_ADMIN' => 'Správce',
            'ACCESS_ADMIN_LOGIN' => 'Přihlášení ke správě stránek',
            'ACCESS_ADMIN_SUPER' => 'Super uživatel',
            'ACCESS_ADMIN_CACHE' => 'Vyprázdnit mezipaměť',
            'ACCESS_ADMIN_CONFIGURATION' => 'Nastavení',
            'ACCESS_ADMIN_CONFIGURATION_SYSTEM' => 'Nastavení systému',
            'ACCESS_ADMIN_CONFIGURATION_SITE' => 'Nastavení Webu',
            'ACCESS_ADMIN_CONFIGURATION_MEDIA' => 'Nastavení médií',
            'ACCESS_ADMIN_CONFIGURATION_INFO' => 'Zobrazit informace o serveru',
            'ACCESS_ADMIN_SETTINGS' => 'Nastavení',
            'ACCESS_ADMIN_PAGES' => 'Spravovat stránky',
            'ACCESS_ADMIN_MAINTENANCE' => 'Údržba webu',
            'ACCESS_ADMIN_STATISTICS' => 'Statistiky webu',
            'ACCESS_ADMIN_PLUGINS' => 'Správa rozšíření',
            'ACCESS_ADMIN_THEMES' => 'Správa šablon',
            'ACCESS_ADMIN_TOOLS' => 'Přístup k nástrojům',
            'ACCESS_ADMIN_USERS' => 'Správa uživatelů',
            'USERS' => 'Uživatelé',
            'ACL' => 'Správa přístupu',
            'FLEX_CACHING' => 'Mezipaměť Flex',
            'FLEX_INDEX_CACHE_ENABLED' => 'Povolit mezipaměť indexu',
            'FLEX_INDEX_CACHE_LIFETIME' => 'Životnost mezipaměti indexu (v sekundách)',
            'FLEX_OBJECT_CACHE_ENABLED' => 'Povolit mezipaměť objektů',
            'FLEX_OBJECT_CACHE_LIFETIME' => 'Životnost mezipaměti objektů (v sekundách)',
            'FLEX_RENDER_CACHE_ENABLED' => 'Povolit mezipaměť vykreslování',
            'FLEX_RENDER_CACHE_LIFETIME' => 'Životnost mezipaměti vykreslování (v sekundách)',
            'DEBUGGER_CENSORED' => 'Odstranit citlivá data',
            'DEBUGGER_CENSORED_HELP' => 'Pro Clockwork zprostředkovatele: Pokud je nastaveno Ano, odstraní se potenciálně citlivá data (parametry POST, cookies, soubory, nastavení a většina polí/objektových dat z logů)',
            'LANGUAGE_TRANSLATIONS' => 'Překlady',
            'LANGUAGE_TRANSLATIONS_HELP' => 'Pokud není povoleno, jsou použity překladové klíče namísto přeložených řetězců. Tato funkce může být použita k opravě špatných překladů nebo k nalezení hardkódovaných anglických řetězců.',
            'STRICT_BLUEPRINT_COMPAT' => 'Kompatibilita plánu',
            'STRICT_BLUEPRINT_COMPAT_HELP' => 'Umožňuje zpětně kompatibilní přísnou podporu pro plány. Pokud je vypnuto, nové chování provede ověření formuláře jako neúspěšné, pokud existují další data, která nejsou definována v plánu.',
            'RESET' => 'Obnovit',
            'LOGOS' => 'Loga',
            'PRESETS' => 'Předvolby',
            'COLOR_SCHEME_LABEL' => 'Barevné schéma',
            'COLOR_SCHEME_HELP' => 'Vyberte barevné schéma ze seznamu předdefinovaných kombinací, nebo přidejte svůj vlastní styl',
            'COLOR_SCHEME_NAME' => 'Název vlastního barevného schéma',
            'COLOR_SCHEME_NAME_HELP' => 'Dejte název vašemu vlastnímu motivu pro export a sdílení',
            'COLOR_SCHEME_NAME_PLACEHOLDER' => 'Odstíny modré',
            'PRIMARY_ACCENT_LABEL' => 'Primární zvýraznění',
            'PRIMARY_ACCENT_HELP' => 'Vyberte, která barva by měla být použita pro barevné schéma',
            'SECONDARY_ACCENT_LABEL' => 'Sekundární zvýraznění',
            'SECONDARY_ACCENT_HELP' => 'Vyberte, která barva by měla být použita pro barevné schéma jako sekundární',
            'TERTIARY_ACCENT_LABEL' => 'Terciární zvýraznění',
            'TERTIARY_ACCENT_HELP' => 'Vyberte, která barva by měla být použita pro barevné schéma jako terciární',
            'WEB_FONTS_LABEL' => 'Webová písma',
            'WEB_FONTS_HELP' => 'Použít vlastní webová písma',
            'HEADER_FONT_LABEL' => 'Písmo záhlaví',
            'HEADER_FONT_HELP' => 'Písmo použité pro záhlaví, boční navigaci a názvy sekcí',
            'BODY_FONT_LABEL' => 'Písmo textu',
            'BODY_FONT_HELP' => 'Primární písmo použité v těle motivu',
            'CUSTOM_CSS_LABEL' => 'Vlastní styly',
            'CUSTOM_CSS_PLACEHOLDER' => 'Zde vložte vlastní CSS...',
            'CUSTOM_CSS_HELP' => 'Vlastní CSS které budou přidány do každé admin stránky',
            'CUSTOM_FOOTER' => 'Vlastní zápatí',
            'CUSTOM_FOOTER_HELP' => 'Zde můžete použít HTML a/nebo Markdown syntaxi',
            'CUSTOM_FOOTER_PLACEHOLDER' => 'Zadejte HTML/Markdown pro přepsání výchozího zápatí',
            'LOGIN_SCREEN_CUSTOM_LOGO_LABEL' => 'Vlastní přihlašovací logo',
            'TOP_LEFT_CUSTOM_LOGO_LABEL' => 'Základní vlastní logo',
            'LOAD_PRESET' => 'Načíst předvolbu',
            'RECOMPILE' => 'Rekompilovat',
            'EXPORT' => 'Export',
            'QUICKTRAY_RECOMPILE' => 'Ikona Rychlé rekompilace',
            'QUICKTRAY_RECOMPILE_HELP' => 'Překompiluje přednastavený SCSS pro vyzvednutí jakýchkoli změn nebo nových pluginů',
            'CODEMIRROR' => 'Editor kódu CodeMirror',
            'CODEMIRROR_THEME' => 'Téma editoru',
            'CODEMIRROR_THEME_DESC' => '**POZNÁMKA:** Použít [Demo CodeMirror témata](https://codemirror.net/demo/theme.html?target=_blank) pro jejich náhled. **_Paper_** je výchozí Grav motiv.',
            'CODEMIRROR_FONTSIZE' => 'Velikost písma editoru',
            'CODEMIRROR_FONTSIZE_SM' => 'Malé písmo',
            'CODEMIRROR_FONTSIZE_MD' => 'Střední písmo',
            'CODEMIRROR_FONTSIZE_LG' => 'Velké písmo',
            'CODEMIRROR_MD_FONT' => 'Písmo editoru Markdown',
            'CODEMIRROR_MD_FONT_SANS' => 'Sans písmo',
            'CODEMIRROR_MD_FONT_MONO' => 'Písmo Mono/Pevné šířky',
            'CUSTOM_PRESETS' => 'Vlastní předvolby',
            'CUSTOM_PRESETS_HELP' => 'Přetáhněte zde soubor .yaml tématu nebo můžete vytvořit pole předvoleb s textovými klíči',
            'CUSTOM_PRESETS_PLACEHOLDER' => 'Sem zadejte své předvolby',
            'GENERAL' => 'Obecné',
            'CONTENT_EDITOR' => 'Editor obsahu',
            'CONTENT_EDITOR_HELP' => 'Vlastní editory mohou být preferovány pro úpravy obsahu',
            'BAD_FILENAME' => 'Špatný název souboru',
            'SHOW_SENSITIVE' => 'Zobrazit citlivá data',
            'SHOW_SENSITIVE_HELP' => 'POUZE pro poskytovatele Clockwork: Odstranění potenciálně citlivých informací (POST parametry, cookies, soubory, konfigurace a většina dat polí/objektů v log souborech)',
            'VALID_LINK_ATTRIBUTES' => 'Platné atributy odkazu',
            'VALID_LINK_ATTRIBUTES_HELP' => 'Atributy, které budou automaticky přidány do prvku HTML média',
            'CONFIGURATION' => 'Nastavení',
            'CUSTOMIZATION' => 'Přizpůsobení',
            'EXTRAS' => 'Doplňky',
            'BASICS' => 'Základy',
            'ADMIN_CACHING' => 'Povolit Admin Caching',
            'ADMIN_CACHING_HELP' => 'Ukládání do mezipaměti v administraci lze ovládat nezávisle z front-end webu',
            'ADMIN_PATH' => 'Cesta ke správcovské části',
            'ADMIN_PATH_PLACEHOLDER' => 'Výchozí cesta pro administrátora (relativně k základně)',
            'ADMIN_PATH_HELP' => 'Pokud chcete změnit URL adresu správcovské části, můžete zde zadat cestu',
            'LOGO_TEXT' => 'Text loga',
            'LOGO_TEXT_HELP' => 'Text zobrazený místo výchozího loga Grav',
            'CONTENT_PADDING' => 'Odsazení obsahu',
            'CONTENT_PADDING_HELP' => 'Povolit/zakázat obsah odsazení kolem oblasti obsahu pro poskytnutí více místa',
            'BODY_CLASSES' => 'Třídy pro <body> element',
            'BODY_CLASSES_HELP' => 'Přidat názvy vlastních tříd oddělené mezerami',
            'SIDEBAR_ACTIVATION' => 'Aktivace postranního panelu',
            'SIDEBAR_ACTIVATION_HELP' => 'Určuje, jak je boční panel aktivován',
            'SIDEBAR_HOVER_DELAY' => 'Zpoždění při přechodu',
            'SIDEBAR_HOVER_DELAY_APPEND' => 'milisekund',
            'SIDEBAR_ACTIVATION_TAB' => 'Záložka',
            'SIDEBAR_ACTIVATION_HOVER' => 'Hover',
            'SIDEBAR_SIZE' => 'Velikost postranního panelu',
            'SIDEBAR_SIZE_HELP' => 'Nastavuje šířku postranního panelu',
            'SIDEBAR_SIZE_AUTO' => 'Automatická šířka',
            'SIDEBAR_SIZE_SMALL' => 'Malá šířka',
            'EDIT_MODE' => 'Režim úprav',
            'EDIT_MODE_HELP' => 'Automaticky použije plán, pokud je k dispozici, pokud není nalezen žádný z nich, použije režim "Expert".',
            'FRONTEND_PREVIEW_TARGET' => 'Cíl náhledu stránek',
            'FRONTEND_PREVIEW_TARGET_INLINE' => 'Použít Inline ve správcovské části',
            'FRONTEND_PREVIEW_TARGET_NEW' => 'Nová záložka',
            'FRONTEND_PREVIEW_TARGET_CURRENT' => 'Aktuální záložka',
            'PARENT_DROPDOWN' => 'Nadřazený rozevírací seznam',
            'PARENT_DROPDOWN_BOTH' => 'Zobrazit URL adresu a složku',
            'PARENT_DROPDOWN_FOLDER' => 'Zobrazit složku',
            'PARENT_DROPDOWN_FULLPATH' => 'Zobrazit celou cestu',
            'PARENTS_LEVELS' => 'Rodičovské úrovně',
            'PARENTS_LEVELS_HELP' => 'Počet úrovní zobrazených v nadřazeném seznamu',
            'MODULAR_PARENTS' => 'Modulární rodiče',
            'MODULAR_PARENTS_HELP' => 'Zobrazit modulární stránky v nadřazeném seznamu',
            'SHOW_GITHUB_LINK' => 'Zobrazit GitHub odkaz',
            'SHOW_GITHUB_LINK_HELP' => 'Zobrazit "Nalezli jste problém? Nahlašte to prosím na GitHub."',
            'PAGES_LIST_DISPLAY_FIELD' => 'Zobrazovat pole seznamu stránek',
            'PAGES_LIST_DISPLAY_FIELD_HELP' => 'Pole stránky, které se mají použít v seznamu stránek, pokud jsou přítomny. Výchozí nastavení/Zpět do titulku.',
            'AUTO_UPDATES' => 'Vyhledávat aktualizace automaticky',
            'AUTO_UPDATES_HELP' => 'Zobrazí informativní zprávu v admin panelu, pokud je k dispozici aktualizace.',
            'TIMEOUT' => 'Časový limit',
            'TIMEOUT_HELP' => 'Nastaví platnost session v sekundách',
            'HIDE_PAGE_TYPES' => 'Skrýt typy stránek v administraci',
            'HIDE_MODULAR_PAGE_TYPES' => 'Skrýt modulární typy stránek v administraci',
            'DASHBOARD' => 'Přehled',
            'WIDGETS_DISPLAY' => 'Stav zobrazení widgetu',
            'NOTIFICATIONS' => 'Notifikace',
            'FEED_NOTIFICATIONS' => 'Oznámení',
            'FEED_NOTIFICATIONS_HELP' => 'Zobrazit oznámení na základě zdroje zpráv',
            'DASHBOARD_NOTIFICATIONS' => 'Oznámení hlavního panelu',
            'DASHBOARD_NOTIFICATIONS_HELP' => 'Zobrazit oznámení na nástěnce',
            'PLUGINS_NOTIFICATIONS' => 'Upozornění rozšíření',
            'PLUGINS_NOTIFICATIONS_HELP' => 'Zobrazit oznámení k rozšířením',
            'THEMES_NOTIFICATIONS' => 'Upozornění na šablony',
            'THEMES_NOTIFICATIONS_HELP' => 'Zobrazit oznámení zaměřená na témata',
            'LOGO_BG_HELP' => 'Pozadí loga',
            'LOGO_LINK_HELP' => 'Odkaz na logo',
            'NAV_BG_HELP' => 'Pozadí navigace',
            'NAV_TEXT_HELP' => 'Text navigace',
            'NAV_LINK_HELP' => 'Odkaz navigace ',
            'NAV_SELECTED_BG_HELP' => 'Pozadí zvolené navigace',
            'NAV_SELECTED_LINK_HELP' => 'Odkaz zvolené navigace',
            'NAV_HOVER_BG_HELP' => 'Pozadí přechodu navigace',
            'NAV_HOVER_LINK_HELP' => 'Odkaz přechodu navigace',
            'TOOLBAR_BG_HELP' => 'Pozadí nástrojové lišty',
            'TOOLBAR_TEXT_HELP' => 'Text nástrojové lišty',
            'PAGE_BG_HELP' => 'Pozadí stránky',
            'PAGE_TEXT_HELP' => 'Text stránky',
            'PAGE_LINK_HELP' => 'Odkaz na stránku',
            'CONTENT_BG_HELP' => 'Pozadí obsahu',
            'CONTENT_TEXT_HELP' => 'Text obsahu',
            'CONTENT_LINK_HELP' => 'Odkaz textu',
            'CONTENT_LINK2_HELP' => 'Odkaz 2 obsahu',
            'CONTENT_HEADER_HELP' => 'Hlavička obsahu',
            'CONTENT_TABS_BG_HELP' => 'Pozadí záložek obsahu',
            'CONTENT_TABS_TEXT_HELP' => 'Text záložek obsahu',
            'CONTENT_HIGHLIGHT_HELP' => 'Zvýraznění obsahu',
            'BUTTON_BG_HELP' => 'Pozadí tlačítek',
            'BUTTON_TEXT_HELP' => 'Text tlačítek',
            'NOTICE_BG_HELP' => 'Pozadí poznámek',
            'NOTICE_TEXT_HELP' => 'Text poznámek',
            'UPDATES_BG_HELP' => 'Pozadí aktualizací',
            'UPDATES_TEXT_HELP' => 'Text aktualizací',
            'CRITICAL_BG_HELP' => 'Pozadí kritických',
            'CRITICAL_TEXT_HELP' => 'Text kritický',
            'BUTTON_COLORS' => 'Barvy tlačítka',
            'CONTENT_COLORS' => 'Barvy obsahu',
            'TABS_COLORS' => 'Barvy záložek',
            'CRITICAL_COLORS' => 'Barvy kritických',
            'LOGO_COLORS' => 'Barvy loga',
            'NAV_COLORS' => 'Barvy navigace',
            'NOTICE_COLORS' => 'Barvy poznámek',
            'PAGE_COLORS' => 'Barvy stránek',
            'TOOLBAR_COLORS' => 'Barvy nástrojové lišty',
            'UPDATE_COLORS' => 'Barvy aktualizací',
            'POPULARITY' => 'Oblíbenost',
            'VISITOR_TRACKING' => 'Sledování návštěvníků',
            'VISITOR_TRACKING_HELP' => 'Povolit funkci shromažďování statistik návštěvníků',
            'DAYS_OF_STATS' => 'Počet dní statistiky',
            'DAYS_OF_STATS_HELP' => 'Zachovat statistiky pro zadaný počet dní, pak je vymazat',
            'IGNORE_URLS' => 'Ignorovat',
            'IGNORE_URLS_HELP' => 'URL pro ignorování',
            'DAILY_HISTORY' => 'Denní historie',
            'MONTHLY_HISTORY' => 'Měsíční historie',
            'VISITORS_HISTORY' => 'Historie návštěvníků',
            'MEDIA_RESIZE' => 'Změna velikosti obrázků stránky',
            'PAGEMEDIA_RESIZER' => '> Následující nastavení platí pro obrázky nahrané prostřednictvím stránky, sekce Média stránky. Velikost šířky / výšky obrázku se automaticky proporcionálně zmenší, pokud překročí limity nastavené na vstupu. Hodnoty min a max určují rozsahy velikosti nahraných obrázků. Nastavte pole na hodnotu 0, aby nedošlo k žádné manipulaci.',
            'RESIZE_WIDTH' => 'Změna šířky',
            'RESIZE_WIDTH_HELP' => 'Změnit velikost širokých obrázků na nastavenou hodnotu',
            'RESIZE_HEIGHT' => 'Změna výšky',
            'RESIZE_HEIGHT_HELP' => 'Změnit výšku vysokých obrázků na nastavenou hodnotu',
            'RES_MIN_WIDTH' => 'Minimální šířka rozlišení',
            'RES_MIN_WIDTH_HELP' => 'Minimální šířka pro přidání obrázku',
            'RES_MIN_HEIGHT' => 'Min. výška rozlišení',
            'RES_MIN_HEIGHT_HELP' => 'Minimální povolená výška pro přidání obrázku',
            'RES_MAX_WIDTH' => 'Maximální šířka rozlišení',
            'RES_MAX_WIDTH_HELP' => 'Maximální povolená šířka pro přidání obrázku',
            'RES_MAX_HEIGHT' => 'Maximální výška rozlišení',
            'RES_MAX_HEIGHT_HELP' => 'Maximální povolená výška pro přidání obrázku',
            'RESIZE_QUALITY' => 'Kvalita změny rozlišení',
            'RESIZE_QUALITY_HELP' => 'Kvalita použitá při změně velikosti obrázku. Hodnota mezi 0 a 1.',
            'PIXELS' => 'pixely',
            'ACCESS_ADMIN_CONFIGURATION_SECURITY' => 'Spravovat nastavení zabezpečení',
            'SESSION_DOMAIN' => 'Doména relace',
            'SESSION_DOMAIN_HELP' => 'Používejte pouze v případě, pokud přepíšete doménu webu, například v Docker Containeru.',
            'SESSION_PATH' => 'Cesta relace',
            'SESSION_PATH_HELP' => 'Používejte pouze v případě, pokud přepíšete cestu webu, například v Docker Containeru.',
            'REDIRECT_OPTION_NO_REDIRECT' => 'Bez přesměrování',
            'REDIRECT_OPTION_DEFAULT_REDIRECT' => 'Použít výchozí kód přesměrování',
            'REDIRECT_OPTION_301' => '301 - Přesunuto trvale',
            'REDIRECT_OPTION_302' => '302 - Přesunuto dočasně',
            'REDIRECT_OPTION_303' => '303 - Viz ostatní',
            'IMAGES_CLS_TITLE' => 'Kumulativní posun rozložení (CLS)',
            'IMAGES_CLS_AUTO_SIZES' => 'Povolit automatické velikosti',
            'IMAGES_CLS_AUTO_SIZES_HELP' => 'Automaticky přidat atributy "šířka" a "výška" k obrázkům do CLS adresy',
            'IMAGES_CLS_ASPECT_RATIO' => 'Povolit poměr stran',
            'IMAGES_CLS_ASPECT_RATIO_HELP' => 'Volitelná proměnná CSS, aplikovaná prostřednictvím atributu "style", kterou lze použít v CSS pro vlastní stylování',
            'IMAGES_CLS_RETINA_SCALE' => 'Faktor měřítka Retina',
            'IMAGES_CLS_RETINA_SCALE_HELP' => 'Vezme vypočítanou velikost a vydělí faktorem měřítka, aby zobrazil obrázek s vyšším rozlišením při menší velikosti pixelu pro lepší zpracování rozlišení HiDPI',
            'AUTOREGENERATE_FOLDER_SLUG' => 'Automaticky obnovit na základě názvu stránky',
            'ENABLE' => 'Povolit',
            'PLUGINS_MUST_BE_ENABLED' => 'Pro nastavení musí být doplněk povolen',
            'ACTIVATION_REQUIRED' => 'Pro nastavení je vyžadována aktivace',
            'SESSION_SECURE_HTTPS' => 'Bezpečné (HTTPS)',
            'SESSION_SECURE_HTTPS_HELP' => 'Nastavte relaci zabezpečenou na HTTPS, ale ne na HTTP. Nemá žádný účinek, pokud máte výše uvedené nastavení Secure nastaveno na hodnotu true. Nastavte na hodnotu false, pokud váš web přeskakuje mezi HTTP a HTTPS.',
            'AVATAR' => 'Generátor Avataru',
            'AVATAR_HELP' => 'Multiavatar je lokálně generovaný avatar. Gravatar je externí služba, která používá vaši e-mailovou adresu ke vzdálenému stažení předem nakonfigurovaného Avatara',
            'AVATAR_HASH' => 'POZNÁMKA: Volitelný vlastní "hash" řetězec Avatara',
            'IMAGES_TITLE' => 'Obrázky',
            'LEGACY_MEDIA_MUTATION' => 'Kompatibilita se staršími médii',
            'LEGACY_MEDIA_MUTATION_HELP' => 'Povolte toto nastavení pouze v případě, že po aktualizaci Grav došlo k přerušení manipulace s obrázkem.',
            'BACKWARD_COMPATIBILITY' => 'Zpětná kompatibilita'
        ]
    ]
];
