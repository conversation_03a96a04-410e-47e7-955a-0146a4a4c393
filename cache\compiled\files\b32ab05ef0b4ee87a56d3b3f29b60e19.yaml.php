<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/seo/languages/en.yaml',
    'modified' => 1711576872,
    'size' => 4586,
    'data' => [
        'PLUGIN_SEO' => [
            'ROBOTS' => 'Select robots meta value',
            'CANONICAL_URL' => 'Canonical URL',
            'PLUGIN_STATUS' => 'Plugin status',
            'STATUS_HELP' => 'Set to false to disable this plugin completely.',
            'GOOGLE_DESC' => 'Override Description',
            'GOOGLE_TITLE' => 'Override Title',
            'GOOGLE_PREVIEWDESC' => 'Here is a preview of your page on Google results',
            'TWITTER_ENABLE' => 'Activate twitter card',
            'TWITTER_CARD_SELECT' => 'Select twitter card type',
            'TWITTER_CARD_GALLERY' => 'Select ',
            'TWITTER_CARD_PHOTO' => 'Photo',
            'TWITTER_CARD_PRODUCT' => 'Product',
            'TWITTER_CARD_SUMMARY' => 'Summary',
            'TWITTER_CARD_PLAYER' => 'Player',
            'TWITTER_CARD_APP' => 'App',
            'TWITTER_CARD_SUMLARGIMG' => 'Summary Large Image',
            'TWITTER_CARD_SECTITLE' => 'Twitter Card',
            'TWITTER_SHAREIMG' => 'Twitter Image',
            'TWITTER_TITLE' => 'Twitter title',
            'TWITTER_DESCRIPTION' => 'Twitter Description',
            'TWITTER_PREVIEWDESC' => 'Here is a preview of your page as shared on twitter',
            'FACEBOOK_ENABLE' => 'Enable facebook',
            'FACEBOOK_TITLE' => 'Facebook Title',
            'FACEBOOK_DESC' => 'Facebook Description',
            'FACEBOOK_SHAREIMG' => 'Facebook Share Image',
            'FACEBOOK_PREVIEWDESC' => 'Here is a preview of your page as shared on Facebook',
            'IMAGE_HEIGHT_PX' => 'Image Height (px)',
            'IMAGE_WIDTH_PX' => 'Image Width (px)',
            'ARTICLE_IMAGE' => 'Article Image',
            'DATE_PUBLISHED' => 'Published date',
            'DATE_MODIFIED' => 'Modification date',
            'ARTICLE_DESCRIPTION' => 'Article Description',
            'HEADLINE' => 'Headline',
            'ADD_ARTICLE_MICRODATA' => 'Add Article Microdata',
            'ARTICLE_AUTHOR' => 'Article Author',
            'ARTICLE_PLACEHOLDER' => 'Will use page title if left empty',
            'ARTICLE_DESC_PLACEHOLDER' => 'Will use summary if left empty',
            'ARTICLE_PUBLISHER' => 'Article Publisher',
            'ENABLEMUSICEVENT' => 'Enable Music Event',
            'PUBLISHER_LOGO' => 'Publisher Logo',
            'PUBLISHER_NAME' => 'Publisher Name',
            'ARTICLE_BODY' => 'Full Article Body',
            'ADDMUSICEVENT' => 'Add Music Event Microdata',
            'LOCATION_NAME' => 'Location Name',
            'LOCATION_ADDRESS' => 'Location Address',
            'MUSICEVENT_NAME' => 'Music Event Name',
            'MUSICEVENT_DESCRIPTION' => 'Description',
            'MUSICEVENT_IMAGE' => 'Event Image',
            'MUSICEVENT_URL' => 'Music Event Url',
            'PREORDER' => 'Preorder',
            'OUTOFSTOCK' => 'Out of Stock',
            'PRESALE' => 'Pre Sale',
            'INSTOCK' => 'In Stock',
            'DISCONTINUED' => 'Discontinued',
            'INSTOREONLY' => 'In Store Only',
            'SOLDOUT' => 'Sold Out',
            'ONLINEONLY' => 'Online Only',
            'TICKET_URL' => 'Ticket URL',
            'VALID_FROM' => 'Valid From',
            'MUSICEVENT_PRICE' => 'Price',
            'PRICE_CURRENCY' => 'Price Currency',
            'ADD_PERFORMER' => 'Add Performer',
            'PERFORMER_TYPE' => 'Performer Type',
            'MUSICGROUP' => 'Music Group',
            'PERSON' => 'Person',
            'PERFORMER_NAME' => 'Performer Name',
            'MUSICEVENT_DATE_START' => 'Start Date',
            'MUSICEVENT_DATE_END' => 'End Date',
            'WORK_PERFORMED' => 'Works Performed',
            'WORK_PERFORMED_NAME' => 'Name of work',
            'ADD_PERSON' => 'Add Person Microdata',
            'PERSON_NAME' => 'Person Name',
            'PERSON_JOB_TITLE' => 'Job Title',
            'PERSON_ADDRESS_LOCALITY' => 'Address Locality',
            'PERSON_ADDRESS_REGION' => 'Address Region',
            'ADD_EVENT' => 'Add Event Microdata',
            'EVENT_NAME' => 'Event Name',
            'ENABLEEVENT' => 'Enable Event Microdata',
            'EVENT_START_DATE' => 'Event Start Date',
            'EVENT_END_DATE' => 'Event End Date',
            'EVENT_DESCRIPTION' => 'Event Description',
            'EVENT_LOCATION_NAME' => 'Event Location Name',
            'EVENT_LOCATION_STREET_ADDRESS' => 'Event Location Street Address',
            'EVENT_LOCATION_ADDRESS_LOCALITY' => 'Event Location Address Locality',
            'EVENT_LOCATION_ADDRESS_REGION' => 'Event Location Region',
            'EVENT_OFFER_PRICE' => 'Event Offer Price',
            'EVENT_OFFER_CURRENCY' => 'Event Offer Currency',
            'EVENT_OFFER_URL' => 'Event Offer Url',
            'ADD_MUSICALBUM' => 'Add Music Album Microdata',
            'MUSICALBUM_NAME' => 'Music Album Name',
            'MUSICALBUM_NAMEOFBAND' => 'Name of Band',
            'MUSICALBUM_IMAGE' => 'Image',
            'MUSICALBUM_URL' => 'Album URL',
            'MUSICALBUM_GENRE' => 'Genre',
            'MUSICALBUM_TRACK_NUMBER' => 'Track Number',
            'MUSICALBUM_ADD_TRACK' => 'Add Track',
            'MUSICALBUM_TRACK_POSITION' => 'Track Position',
            'MUSICALBUM_TRACK_NAME' => 'Track Name',
            'MUSICALBUM_TRACK_URL' => 'Track URL',
            'MUSICALBUM_TRACK_DURATION' => 'Track Duration',
            'LOCATION_URL' => 'Location Url',
            'ADD_RESTAURANT' => 'Add Restaurant Microdata',
            'RESTAURANT_NAME' => 'Restaurant Name',
            'RESTAURANT_ACCEPT_RESERVATION' => 'Accept Reservation',
            'RESTAURANT_MENU_URL' => 'Menu URL',
            'RESTAURANT_IMAGE' => 'Image',
            'RESTAURANT_CUISINE' => 'Cuisine',
            'RESTAURANT_PRICE_RANGE' => 'Price Range',
            'RESTAURANT_POSTAL_CODE' => 'Postal Code',
            'RESTAURANT_STREET' => 'Street',
            'RESTAURANT_LOCALITY' => 'Locality',
            'LOCATION_URL_PLACEHOLDER' => 'Url to the venue website',
            'RESTAURANT_REGION' => 'Region',
            'RESTAURANT_TELEPHONE' => 'Telephone',
            'ENABLE_PERSON' => 'Enable Person Microdata',
            'ENABLEORGA' => 'Enable Organization Microdata',
            'MUSICEVENT_URL_PLACEHOLDER' => 'Url to the official page of this music event'
        ]
    ]
];
