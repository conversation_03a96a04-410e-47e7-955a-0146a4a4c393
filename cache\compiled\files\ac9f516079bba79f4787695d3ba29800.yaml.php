<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/system/languages/it.yaml',
    'modified' => 1715789844,
    'size' => 3701,
    'data' => [
        'GRAV' => [
            'FRONTMATTER_ERROR_PAGE' => '---Titolo: %1$s---# Errore: Frontmatter non valido: \'%2$s\' * *%3$s * * \' \'%4$s \' \'',
            'INFLECTOR_PLURALS' => [
                '/(quiz)$/i' => '\\1',
                '/^(ox)$/i' => '\\1en',
                '/([m|l])ouse$/i' => '\\1ice',
                '/(matr|vert|ind)ix|ex$/i' => '\\1ices',
                '/(x|ch|ss|sh)$/i' => '\\1es',
                '/([^aeiouy]|qu)ies$/i' => '\\1y',
                '/([^aeiouy]|qu)y$/i' => '\\1ies',
                '/(hive)$/i' => '\\1s',
                '/(?:([^f])fe|([lr])f)$/i' => '\\1\\2ves',
                '/sis$/i' => 'ses',
                '/([ti])um$/i' => '\\1a',
                '/(buffal|tomat)o$/i' => '\\1oes',
                '/(bu)s$/i' => '\\1ses',
                '/(alias|status)/i' => '\\1es',
                '/(octop|vir)us$/i' => '\\1i',
                '/(ax|test)is$/i' => '\\1es',
                '/s$/i' => 's',
                '/$/' => 's'
            ],
            'INFLECTOR_SINGULAR' => [
                '/(quiz)zes$/i' => '\\1',
                '/(matr)ices$/i' => '\\1ix',
                '/(vert|ind)ices$/i' => '\\1ex',
                '/^(ox)en/i' => '\\1',
                '/(alias|status)es$/i' => '\\1',
                '/([octop|vir])i$/i' => '\\1us',
                '/(cris|ax|test)es$/i' => '\\1is',
                '/(shoe)s$/i' => '\\1',
                '/(o)es$/i' => '\\1',
                '/(bus)es$/i' => '\\1',
                '/([m|l])ice$/i' => '\\1ouse',
                '/(x|ch|ss|sh)es$/i' => '\\1',
                '/(m)ovies$/i' => '\\1ovie',
                '/(s)eries$/i' => '\\1eries',
                '/([^aeiouy]|qu)ies$/i' => '\\1y',
                '/([lr])ves$/i' => '\\1f',
                '/(tive)s$/i' => '\\1',
                '/(hive)s$/i' => '\\1',
                '/([^f])ves$/i' => '\\1fe',
                '/(^analy)ses$/i' => '\\1sis',
                '/((a)naly|(b)a|(d)iagno|(p)arenthe|(p)rogno|(s)ynop|(t)he)ses$/i' => '\\1\\2sis',
                '/([ti])a$/i' => '\\1um',
                '/(n)ews$/i' => '\\1ews'
            ],
            'INFLECTOR_UNCOUNTABLE' => [
                0 => 'dotazione',
                1 => 'informazione',
                2 => 'riso',
                3 => 'denaro',
                4 => 'specie',
                5 => 'serie',
                6 => 'pesce',
                7 => 'pecora'
            ],
            'INFLECTOR_IRREGULAR' => [
                'person' => 'persone',
                'man' => 'uomini',
                'child' => 'bambino',
                'sex' => 'sessi',
                'move' => 'sposta'
            ],
            'INFLECTOR_ORDINALS' => [
                'default' => '°',
                'first' => '°',
                'second' => 'o',
                'third' => 'o'
            ],
            'NICETIME' => [
                'NO_DATE_PROVIDED' => 'Nessuna data fornita',
                'BAD_DATE' => 'Data non valida',
                'AGO' => 'fa',
                'FROM_NOW' => 'da adesso',
                'JUST_NOW' => 'ora',
                'SECOND' => 'secondo',
                'MINUTE' => 'minuto',
                'HOUR' => 'ora',
                'DAY' => 'giorno',
                'WEEK' => 'settimana',
                'MONTH' => 'mese',
                'YEAR' => 'anno',
                'DECADE' => 'decennio',
                'SEC' => 'sec',
                'MIN' => 'min',
                'HR' => 'ora',
                'WK' => 'settimana',
                'MO' => 'mese',
                'YR' => 'anno',
                'DEC' => 'decennio',
                'SECOND_PLURAL' => 'secondi',
                'MINUTE_PLURAL' => 'minuti',
                'HOUR_PLURAL' => 'ore',
                'DAY_PLURAL' => 'giorni',
                'WEEK_PLURAL' => 'settimane',
                'MONTH_PLURAL' => 'mesi',
                'YEAR_PLURAL' => 'anni',
                'DECADE_PLURAL' => 'decadi',
                'SEC_PLURAL' => 'secondi',
                'MIN_PLURAL' => 'minuti',
                'HR_PLURAL' => 'ore',
                'WK_PLURAL' => 'settimane',
                'MO_PLURAL' => 'mesi',
                'YR_PLURAL' => 'anni',
                'DEC_PLURAL' => 'decenni'
            ],
            'FORM' => [
                'VALIDATION_FAIL' => '<b>Validazione fallita:</b>',
                'INVALID_INPUT' => 'Input non valido in',
                'MISSING_REQUIRED_FIELD' => 'Campo richiesto mancante:',
                'XSS_ISSUES' => 'Rilevati potenziali problemi di XSS nel campo \'%s\''
            ],
            'MONTHS_OF_THE_YEAR' => [
                0 => 'Gennaio',
                1 => 'Febbraio',
                2 => 'Marzo',
                3 => 'Aprile',
                4 => 'Maggio',
                5 => 'Giugno',
                6 => 'Luglio',
                7 => 'Agosto',
                8 => 'Settembre',
                9 => 'Ottobre',
                10 => 'Novembre',
                11 => 'Dicembre'
            ],
            'DAYS_OF_THE_WEEK' => [
                0 => 'Lunedì',
                1 => 'Martedì',
                2 => 'Mercoledì',
                3 => 'Giovedì',
                4 => 'Venerdì',
                5 => 'Sabato',
                6 => 'Domenica'
            ],
            'YES' => 'Sì',
            'NO' => 'No',
            'CRON' => [
                'EVERY' => 'ogni',
                'EVERY_HOUR' => 'ogni ora',
                'EVERY_MINUTE' => 'ogni minuto',
                'EVERY_DAY_OF_WEEK' => 'ogni giorno della settimana',
                'EVERY_DAY_OF_MONTH' => 'ogni giorno del mese',
                'EVERY_MONTH' => 'ogni mese',
                'TEXT_PERIOD' => 'Ogni <b />',
                'TEXT_MINS' => ' a <b /> minuto(i) dall\'inizio dell\'ora',
                'TEXT_TIME' => ' alle <b />:<b />',
                'TEXT_DOW' => ' su <b />',
                'TEXT_MONTH' => ' di <b />',
                'TEXT_DOM' => ' di <b />',
                'ERROR1' => 'Il tag %s non è supportato!',
                'ERROR2' => 'Numero di elementi non valido',
                'ERROR3' => 'Il jquery_element deve essere impostato nelle impostazioni di jqCron',
                'ERROR4' => 'Espressione non riconosciuta'
            ]
        ]
    ]
];
