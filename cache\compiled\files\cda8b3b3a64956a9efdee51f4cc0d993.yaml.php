<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/ai-draft-assistant/blueprints.yaml',
    'modified' => 1747168143,
    'size' => 3392,
    'data' => [
        'name' => 'AI Draft Assistant',
        'version' => '1.0.0',
        'description' => 'Provides AI-powered draft assistance for email responses',
        'icon' => 'comments',
        'author' => [
            'name' => '<PERSON><PERSON><PERSON>',
            'email' => '<EMAIL>'
        ],
        'homepage' => 'https://drain5etoiles.com',
        'keywords' => 'grav, plugin, ai, openai, chatgpt, email, draft',
        'bugs' => 'https://github.com/getgrav/grav-plugin-ai-draft-assistant/issues',
        'license' => 'MIT',
        'dependencies' => [
            0 => [
                'name' => 'grav',
                'version' => '>=1.6.0'
            ],
            1 => [
                'name' => 'form',
                'version' => '>=4.0.0'
            ],
            2 => [
                'name' => 'email',
                'version' => '>=3.0.0'
            ]
        ],
        'form' => [
            'validation' => 'loose',
            'fields' => [
                'enabled' => [
                    'type' => 'toggle',
                    'label' => 'Plugin Status',
                    'highlight' => 1,
                    'default' => 0,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ]
                ],
                'api_section' => [
                    'type' => 'section',
                    'title' => 'API Configuration',
                    'underline' => true
                ],
                'api_key' => [
                    'type' => 'text',
                    'label' => 'OpenAI API Key',
                    'help' => 'Your OpenAI API key from the OpenAI dashboard',
                    'placeholder' => 'sk-...'
                ],
                'model' => [
                    'type' => 'select',
                    'label' => 'AI Model',
                    'help' => 'Select which OpenAI model to use',
                    'default' => 'gpt-3.5-turbo',
                    'options' => [
                        'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
                        'gpt-4' => 'GPT-4 (if available)'
                    ]
                ],
                'temperature' => [
                    'type' => 'range',
                    'label' => 'Temperature',
                    'help' => 'Controls randomness. Lower values are more focused, higher values more creative.',
                    'default' => 0.7,
                    'validate' => [
                        'min' => 0,
                        'max' => 1,
                        'step' => 0.1
                    ]
                ],
                'max_tokens' => [
                    'type' => 'number',
                    'label' => 'Max Tokens',
                    'help' => 'Maximum length of generated responses',
                    'default' => 500,
                    'validate' => [
                        'min' => 100,
                        'max' => 2000
                    ]
                ],
                'behavior_section' => [
                    'type' => 'section',
                    'title' => 'Behavior Settings',
                    'underline' => true
                ],
                'target_forms' => [
                    'type' => 'selectize',
                    'label' => 'Target Forms',
                    'help' => 'Select which forms should use AI draft assistance',
                    'placeholder' => 'Select forms',
                    'default' => [
                        0 => 'get-a-quote'
                    ]
                ],
                'auto_append_suggestions' => [
                    'type' => 'toggle',
                    'label' => 'Auto-append Suggestions',
                    'help' => 'Automatically append AI suggestions to emails',
                    'highlight' => 0,
                    'default' => 0,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ]
                ],
                'debug' => [
                    'type' => 'toggle',
                    'label' => 'Debug Mode',
                    'help' => 'Enable debug features and additional logging',
                    'highlight' => 0,
                    'default' => 0,
                    'options' => [
                        1 => 'Enabled',
                        0 => 'Disabled'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ]
                ],
                'prompt_section' => [
                    'type' => 'section',
                    'title' => 'Prompt Configuration',
                    'underline' => true
                ],
                'prompt_template' => [
                    'type' => 'textarea',
                    'label' => 'Prompt Template',
                    'help' => 'Template for generating AI prompts. Use {{variables}} for form fields.',
                    'placeholder' => 'Enter your prompt template here',
                    'default' => 'You are an assistant for a plumbing company. Create a professional response to the customer inquiry below.
Consider the following details:
- Be polite and professional
- Address the specific plumbing issue mentioned
- If it\'s an emergency, emphasize quick response
- Include relevant information about the service

Customer information:
Name: {{name}}
Email: {{email}}
Phone: {{phone}}
Address: {{address}}
Message: {{message}}
Emergency Service Requested: {{emergency}}

Create a draft response that a plumbing company representative could send to this customer.
'
                ]
            ]
        ]
    ]
];
