<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/mailchimp-form---/blueprints.yaml',
    'modified' => 1711576424,
    'size' => 1202,
    'data' => [
        'name' => 'Mailchimp Form',
        'slug' => 'mailchimp-form',
        'type' => 'plugin',
        'version' => '0.1.0',
        'description' => 'Mailchimp Form Plugin',
        'icon' => 'plug',
        'author' => [
            'name' => '<PERSON>',
            'email' => '<EMAIL>'
        ],
        'homepage' => 'https://github.com/georgespapas/grav-plugin-mailchimp-form',
        'demo' => 'http://demo.yoursite.com',
        'keywords' => 'grav, plugin, etc',
        'bugs' => 'https://github.com/georgespapas/grav-plugin-mailchimp-form/issues',
        'docs' => 'https://github.com/georgespapas/grav-plugin-mailchimp-form/blob/develop/README.md',
        'license' => 'MIT',
        'dependencies' => [
            0 => [
                'name' => 'grav',
                'version' => '>=1.6.0'
            ]
        ],
        'form' => [
            'validation' => 'loose',
            'fields' => [
                'enabled' => [
                    'type' => 'toggle',
                    'label' => 'PLUGIN_ADMIN.PLUGIN_STATUS',
                    'highlight' => 1,
                    'default' => 0,
                    'options' => [
                        1 => 'PLUGIN_ADMIN.ENABLED',
                        0 => 'PLUGIN_ADMIN.DISABLED'
                    ],
                    'validate' => [
                        'type' => 'bool'
                    ]
                ],
                'apikey' => [
                    'type' => 'text',
                    'label' => 'MailChimp API Key',
                    'size' => 'medium'
                ],
                'server' => [
                    'type' => 'text',
                    'label' => 'Mailchimp Server Name',
                    'help' => 'The sundomain when you log in to mailchimp.com (ex: us1 )'
                ],
                'list' => [
                    'type' => 'select',
                    'label' => 'Audience List',
                    'help' => 'Choose the audience where the contacts will be added',
                    'data-options@' => '\\Grav\\Plugin\\MailchimpFormPlugin::getMcLists'
                ]
            ]
        ]
    ]
];
