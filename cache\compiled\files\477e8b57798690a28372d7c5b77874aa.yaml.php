<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/admin/languages/my.yaml',
    'modified' => 1730089692,
    'size' => 66184,
    'data' => [
        'PLUGIN_ADMIN' => [
            'ADMIN_NOSCRIPT_MSG' => 'ကျေးဇူးပြုပြီးမင်းရဲ့ browser မှာ JavaScript ကိုဖွင့်ပေးပါ။ ',
            'ADMIN_BETA_MSG' => 'ဒါက Beta ဖြန့်ချိမှုပါ၊ ဒါကိုမင်းကိုယ်ပိုင်စွန့် ဦး တီထွင်ထုတ်လုပ်ရေးမှာသုံးပါ။ ',
            'ADMIN_REPORT_ISSUE' => 'ပြဿနာတစ်ခုတွေ့လား။ ကျေးဇူးပြုပြီးအဲဒါကို GitHub မှာသတင်းပို့ပါ။ ',
            'LOGIN_BTN' => 'လော့ဂ်အင်',
            'LOGIN_BTN_FORGOT' => 'မေ့သွားတယ်',
            'LOGIN_BTN_RESET' => 'စကားဝှက်ကိုပြန်လည်သတ်မှတ်ပါ',
            'LOGIN_BTN_SEND_INSTRUCTIONS' => 'လမ်းညွှန်ချက်ပြန်ညှိရန်',
            'LOGIN_BTN_CLEAR' => 'ရှင်းရန်ပုံစံ',
            'LOGIN_BTN_CREATE_USER' => 'အသုံးပြုသူကိုဖန်တီးပါ',
            'LOGIN_LOGGED_IN' => 'သင်အောင်မြင်စွာလော့ဂ်အင်ဝင်ပြီးပြီ',
            'LOGIN_FAILED' => 'Login မအောင်မြင်',
            'LOGGED_OUT' => 'သင်ထွက်လိုက်ပါပြီ',
            'RESET_NEW_PASSWORD' => 'ကျေးဇူးပြု၍ စကားဝှက်အသစ်ထည့်ပါ၊ hellip;',
            'RESET_LINK_EXPIRED' => 'လင့်ခ်ပြန်လည်ပြင်ဆင်မှုသက်တမ်းကုန်သွားပါပြီ၊ ကျေးဇူးပြု၍ ထပ်ကြိုးစားပါ',
            'RESET_PASSWORD_RESET' => 'စကားဝှက်ကိုပြန်လည်သတ်မှတ်လိုက်ပါပြီ',
            'RESET_INVALID_LINK' => 'ပြန်လည်ပြင်ဆင်ထားသောလင့်ခ်မမှန်ကန်ပါ၊ ကျေးဇူးပြု၍ ထပ်ကြိုးစားပါ',
            'FORGOT_INSTRUCTIONS_SENT_VIA_EMAIL' => 'သင်၏စကားဝှက်ကိုပြန်လည်သတ်မှတ်ရန်ညွှန်ကြားချက်ကိုသင်၏အီးမေးလ်လိပ်စာသို့ပို့ပြီးပါပြီ',
            'FORGOT_FAILED_TO_EMAIL' => 'ညွှန်ကြားချက်များကိုအီးမေးလ်ပို့။ မရခဲ့ပါ၊ ကျေးဇူးပြု၍ နောက်မှထပ်ကြိုးစားပါ',
            'FORGOT_CANNOT_RESET_EMAIL_NO_EMAIL' => ' %s အတွက်စကားဝှက်ကိုပြန်လည် သတ်မှတ်၍ မရပါ၊ အီးမေးလ်လိပ်စာသတ်မှတ်မထားပါ',
            'FORGOT_USERNAME_DOES_NOT_EXIST' => 'အသုံးပြုသူအမည် <b>%s </b> နှင့်အသုံးပြုသူမရှိပါ',
            'FORGOT_EMAIL_NOT_CONFIGURED' => 'စကားဝှက်ကိုပြန်လည် သတ်မှတ်၍ မရပါ။ ဤဆိုဒ်သည်အီးမေးလ်များပို့ရန်ပြင်ဆင်မထားပါ',
            'FORGOT_EMAIL_SUBJECT' => '%s စကားဝှက်ပြန်လည်သတ်မှတ်ရန်တောင်းဆိုခြင်း',
            'FORGOT_EMAIL_BODY' => '<h1> စကားဝှက်ပြန်လည်သတ်မှတ်ခြင်း </h1> <p> ချစ်ခင်ရသော %1 $ s </p> <p> သင်၏စကားဝှက်ကိုပြန်လည်သတ်မှတ်ရန် <b> %4 $ s </b> တွင်တောင်းဆိုချက်တစ်ခုပြုလုပ်ခဲ့သည်။ < /p> <p> <br /> <a href="%2$s" class="btn-primary"> သင်၏စကားဝှက်ကိုပြန်လည်သတ်မှတ်ရန်ဤကိုနှိပ်ပါ </a> <br /> <br /> </p> <p> တနည်းအားဖြင့်အောက်ပါ URL ကိုသင်၏ browser ၏ address bar သို့ကူးယူပါ။ </p> <p>%2 $ s </p> <p> <br /> လေးစားပါတယ် <br /> <br />%3 $ s </p> ',
            'MANAGE_PAGES' => 'စာမျက်နှာများကိုစီမံပါ',
            'PAGES' => 'စာမျက်နှာများ',
            'PLUGINS' => 'ပလပ်အင်များ',
            'PLUGIN' => 'ပလပ်အင်',
            'THEMES' => 'အပြင်အဆင်',
            'LOGOUT' => 'ထွက်ရန်',
            'BACK' => 'ပြန်',
            'NEXT' => 'နောက်',
            'PREVIOUS' => 'ယခင်',
            'ADD_PAGE' => 'စာမျက်နှာထည့်ရန်',
            'MOVE' => 'ရွှေ့ပါ',
            'DELETE' => 'ဖျက်ရန်',
            'UNSET' => 'သတ်မှတ်မထားပါ',
            'VIEW' => 'ကြည့်ရန်',
            'SAVE' => 'ကယ်ပါ',
            'NORMAL' => 'ပုံမှန်',
            'EXPERT' => 'ကျွမ်းကျင်သူ',
            'EXPAND_ALL' => 'အားလုံးကိုချဲ့ပါ',
            'COLLAPSE_ALL' => 'အားလုံးလျှော့ပြရန်',
            'ERROR' => 'အမှား',
            'CLOSE' => 'ပိတ်ပါ',
            'CANCEL' => 'ပယ်ဖျက်',
            'CONTINUE' => 'ဆက်လုပ်ပါ',
            'CONFIRM' => 'အတည်ပြု',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_TITLE' => 'အတည်ပြုချက်လိုအပ်သည်',
            'MODAL_CHANGED_DETECTED_TITLE' => 'အပြောင်းအလဲများကိုတွေ့ရှိထားသည်',
            'MODAL_CHANGED_DETECTED_DESC' => 'မင်းမှာမသိမ်းရသေးတဲ့အပြောင်းအလဲတွေရှိတယ်။ မင်းမသိမ်းဘဲထွက်သွားချင်တာသေချာလား။ ',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_TITLE' => 'အတည်ပြုချက်လိုအပ်သည်',
            'MODAL_DELETE_FILE_CONFIRMATION_REQUIRED_DESC' => 'သင်ဤဖိုင်ကိုဖျက်လိုသည်မှာသေချာသလား။ ဤလုပ်ဆောင်ချက်ကိုပြန် ပြင်၍ မရပါ။ ',
            'MODAL_UPDATE_GRAV_CONFIRMATION_REQUIRED_DESC' => 'သင် Grav ကိုနောက်ဆုံးရဗားရှင်းသို့အဆင့်မြှင့်တင်တော့မည်။ သင်ဆက်လက်လုပ်ဆောင်လိုပါသလား။ ',
            'ADD_FILTERS' => 'Filters များထည့်ပါ',
            'SEARCH_PAGES' => 'စာမျက်နှာများရှာရန်',
            'VERSION' => 'ဗားရှင်း',
            'WAS_MADE_WITH' => 'ဖြင့်ပြုလုပ်ထားသည်',
            'BY' => 'အားဖြင့်',
            'UPDATE_THEME' => 'အပြင်အဆင်ကိုအပ်ဒိတ်လုပ်ပါ',
            'UPDATE_PLUGIN' => 'ပလပ်အင် Update ကို',
            'OF_THIS_THEME_IS_NOW_AVAILABLE' => 'ဤအခင်းအကျင်းကိုယခုရနိုင်သည်',
            'OF_THIS_PLUGIN_IS_NOW_AVAILABLE' => 'ဒီပလပ်အင်၏ယခုရရှိနိုင်ပါပြီ',
            'AUTHOR' => 'စာရေးသူ',
            'HOMEPAGE' => 'ပင်မစာမျက်နှာ',
            'DEMO' => 'သရုပ်ပြ',
            'BUG_TRACKER' => 'Bug Tracker ကို',
            'KEYWORDS' => 'သော့ချက်စာလုံးများ',
            'LICENSE' => 'လိုင်စင်',
            'DESCRIPTION' => 'ဖော်ပြချက်',
            'README' => 'ဖတ်ကြည့်ပါ',
            'DOCS' => 'စာရွက်စာတမ်းများ',
            'REMOVE_THEME' => 'အပြင်အဆင်ကိုဖယ်ရှားပါ',
            'INSTALL_THEME' => 'Theme ထည့်သွင်းပါ',
            'THEME' => 'အပြင်အဆင်',
            'BACK_TO_THEMES' => 'Themes သို့ပြန်သွားရန်',
            'BACK_TO_PLUGINS' => 'Plugins သို့ပြန်သွားရန်',
            'CHECK_FOR_UPDATES' => 'နောက်ဆုံးသတင်းများကိုစစ်ဆေးပါ',
            'ADD' => 'ထည့်ပါ',
            'CLEAR_CACHE' => 'Cache ကိုရှင်းလင်းပါ',
            'CLEAR_CACHE_ALL_CACHE' => 'အားလုံး Cache',
            'CLEAR_CACHE_ASSETS_ONLY' => 'ပိုင်ဆိုင်မှုများသာ',
            'CLEAR_CACHE_IMAGES_ONLY' => 'ပုံများသာ',
            'CLEAR_CACHE_CACHE_ONLY' => 'Cache သာ',
            'CLEAR_CACHE_TMP_ONLY' => 'Tmp သီးသန့်',
            'UPDATES_AVAILABLE' => 'အပ်ဒိတ်များရနိုင်သည်',
            'DAYS' => 'နေ့ရက်များ',
            'UPDATE' => 'Update လုပ်ပါ',
            'BACKUP' => 'အရန်သိမ်းခြင်း',
            'BACKUPS' => 'အရန်သိမ်းခြင်း',
            'BACKUP_NOW' => 'ယခုအရန်သိမ်းပါ',
            'BACKUPS_STATS' => 'စာရင်းအင်း Backup',
            'BACKUPS_HISTORY' => 'အရံမှတ်တမ်း',
            'BACKUPS_PURGE_CONFIG' => 'Backup Purge Configuration ကို',
            'BACKUPS_PROFILES' => 'ပရိုဖိုင်များကို Backup လုပ်ပါ',
            'BACKUPS_COUNT' => 'Backups အရေအတွက်',
            'BACKUPS_PROFILES_COUNT' => 'ပရိုဖိုင်အရေအတွက်',
            'BACKUPS_TOTAL_SIZE' => 'Space သုံးသည်',
            'BACKUPS_NEWEST' => 'နောက်ဆုံးပေါ် Backup',
            'BACKUPS_OLDEST' => 'အသက်အကြီးဆုံး Backup',
            'BACKUPS_PURGE' => 'သုတ်သင်ရှင်းလင်းသည်',
            'BACKUPS_NOT_GENERATED' => 'အရန်သိမ်းဆည်းမှုများမပြုလုပ်ရသေးပါ ...',
            'BACKUPS_PURGE_NUMBER' => '%s အရံနေရာများ၏ %s ကိုသုံးနေသည်',
            'BACKUPS_PURGE_TIME' => 'မိတ္တူကူးရန် %s ရက်ကျန်သည်',
            'BACKUPS_PURGE_SPACE' => '%s ၏ %s ကိုသုံးနေသည်',
            'BACKUP_DELETED' => 'Backup ကိုအောင်မြင်စွာဖျက်လိုက်ပါပြီ',
            'BACKUP_NOT_FOUND' => 'Backup မတွေ့ပါ ',
            'BACKUP_DATE' => 'Backup Date',
            'STATISTICS' => 'စာရင်းအင်း',
            'VIEWS_STATISTICS' => 'စာရင်းဇယားကြည့်ရန်စာမျက်နှ',
            'TODAY' => 'ဒီနေ့',
            'WEEK' => 'အေးလေ',
            'MONTH' => 'လ',
            'LATEST_PAGE_UPDATES' => 'Latest Page Updates',
            'MAINTENANCE' => 'Maintenance',
            'UPDATED' => 'Updated',
            'MON' => 'Mon',
            'TUE' => 'Tue',
            'WED' => 'Wed',
            'THU' => 'Thu',
            'FRI' => 'Fri',
            'SAT' => 'Sat',
            'SUN' => 'Sun',
            'COPY' => 'Copy',
            'EDIT' => 'Edit',
            'CREATE' => 'Create',
            'GRAV_ADMIN' => 'Grav Admin',
            'GRAV_OFFICIAL_PLUGIN' => 'Grav Official Plugin',
            'GRAV_OFFICIAL_THEME' => 'Grav Official Theme',
            'PLUGIN_SYMBOLICALLY_LINKED' => 'This plugin is symbolically linked. Updates won\'t be detected.',
            'THEME_SYMBOLICALLY_LINKED' => 'This theme is symbolically linked. Updates won\'t be detected',
            'REMOVE_PLUGIN' => 'Remove Plugin',
            'INSTALL_PLUGIN' => 'Install Plugin',
            'AVAILABLE' => 'Available',
            'INSTALLED' => 'Installed',
            'INSTALL' => 'Install',
            'ACTIVE_THEME' => 'Active Theme',
            'SWITCHING_TO' => 'Switching to',
            'SWITCHING_TO_DESCRIPTION' => 'By switching to a different theme, there is no guarantee that all the layout pages are supported, potentially causing errors when trying to load said pages.',
            'SWITCHING_TO_CONFIRMATION' => 'Do you want to continue and switch to the theme',
            'CREATE_NEW_USER' => 'Create New User',
            'REMOVE_USER' => 'Remove User',
            'ACCESS_DENIED' => 'Access denied',
            'ACCOUNT_NOT_ADMIN' => 'your account does not have administrator permissions',
            'PHP_INFO' => 'PHP Info',
            'INSTALLER' => 'Installer',
            'AVAILABLE_THEMES' => 'Available Themes',
            'AVAILABLE_PLUGINS' => 'Available Plugins',
            'INSTALLED_THEMES' => 'Installed Themes',
            'INSTALLED_PLUGINS' => 'Installed Plugins',
            'BROWSE_ERROR_LOGS' => 'Browse Error Logs',
            'SITE' => 'Site',
            'INFO' => 'Info',
            'SYSTEM' => 'System',
            'USER' => 'User',
            'ADD_ACCOUNT' => 'Add Account',
            'SWITCH_LANGUAGE' => 'Switch Language',
            'SUCCESSFULLY_ENABLED_PLUGIN' => 'Successfully enabled plugin',
            'SUCCESSFULLY_DISABLED_PLUGIN' => 'Successfully disabled plugin',
            'SUCCESSFULLY_CHANGED_THEME' => 'Successfully changed default theme',
            'INSTALLATION_FAILED' => 'Installation failed',
            'INSTALLATION_SUCCESSFUL' => 'Installation successful',
            'UNINSTALL_FAILED' => 'Uninstall failed',
            'UNINSTALL_SUCCESSFUL' => 'Uninstall successful',
            'SUCCESSFULLY_SAVED' => 'Successfully saved',
            'SUCCESSFULLY_COPIED' => 'Successfully copied',
            'REORDERING_WAS_SUCCESSFUL' => 'Reordering was successful',
            'SUCCESSFULLY_DELETED' => 'Successfully deleted',
            'SUCCESSFULLY_SWITCHED_LANGUAGE' => 'Successfully switched language',
            'INSUFFICIENT_PERMISSIONS_FOR_TASK' => 'You have insufficient permissions for task',
            'CACHE_CLEARED' => 'Cache cleared',
            'METHOD' => 'Method',
            'ERROR_CLEARING_CACHE' => 'Error clearing cache',
            'AN_ERROR_OCCURRED' => 'An error occurred',
            'YOUR_BACKUP_IS_READY_FOR_DOWNLOAD' => 'Your backup is ready for download',
            'DOWNLOAD_BACKUP' => 'Download backup',
            'PAGES_FILTERED' => 'Pages filtered',
            'NO_PAGE_FOUND' => 'No Page found',
            'INVALID_PARAMETERS' => 'Invalid Parameters',
            'NO_FILES_SENT' => 'No files sent',
            'EXCEEDED_FILESIZE_LIMIT' => 'Exceeded PHP configuration upload_max_filesize',
            'EXCEEDED_POSTMAX_LIMIT' => 'Exceeded PHP configuration post_max_size',
            'UNKNOWN_ERRORS' => 'Unknown errors',
            'EXCEEDED_GRAV_FILESIZE_LIMIT' => 'Exceeded Grav configuration file size limit',
            'UNSUPPORTED_FILE_TYPE' => 'Unsupported file type',
            'FAILED_TO_MOVE_UPLOADED_FILE' => 'Failed to move uploaded file',
            'FILE_UPLOADED_SUCCESSFULLY' => 'File uploaded successfully',
            'FILE_DELETED' => 'File deleted',
            'FILE_COULD_NOT_BE_DELETED' => 'File could not be deleted',
            'FILE_NOT_FOUND' => 'File not found',
            'NO_FILE_FOUND' => 'No file found',
            'GRAV_WAS_SUCCESSFULLY_UPDATED_TO' => 'Grav was successfully updated to',
            'GRAV_UPDATE_FAILED' => 'Grav update failed',
            'EVERYTHING_UPDATED' => 'Everything updated',
            'UPDATES_FAILED' => 'Updates failed',
            'AVATAR_BY' => 'Avatar by',
            'AVATAR_UPLOAD_OWN' => 'Or upload your own...',
            'LAST_BACKUP' => 'Last Backup',
            'FULL_NAME' => 'Full name',
            'USERNAME' => 'Username',
            'EMAIL' => 'Email',
            'USERNAME_EMAIL' => 'Username or Email',
            'PASSWORD' => 'Password',
            'PASSWORD_CONFIRM' => 'Confirm Password',
            'TITLE' => 'Title',
            'ACCOUNT' => 'Account',
            'EMAIL_VALIDATION_MESSAGE' => 'Must be a valid email address',
            'PASSWORD_VALIDATION_MESSAGE' => 'Password must contain at least one number and one uppercase and lowercase letter, and at least 8 or more characters',
            'LANGUAGE' => 'Language',
            'LANGUAGE_HELP' => 'Set the favorite language',
            'MEDIA' => 'Media',
            'DEFAULTS' => 'Defaults',
            'SITE_TITLE' => 'Site Title',
            'SITE_TITLE_PLACEHOLDER' => 'Site wide title',
            'SITE_TITLE_HELP' => 'Default title for your site, often used in themes',
            'SITE_DEFAULT_LANG' => 'Default language',
            'SITE_DEFAULT_LANG_PLACEHOLDER' => 'Default language to be used by theme\'s <HTML> tag',
            'SITE_DEFAULT_LANG_HELP' => 'Default language to be used by theme\'s <HTML> tag',
            'DEFAULT_AUTHOR' => 'Default Author',
            'DEFAULT_AUTHOR_HELP' => 'A default author name, often used in themes or page content',
            'DEFAULT_EMAIL' => 'Default Email',
            'DEFAULT_EMAIL_HELP' => 'A default email to reference in themes or pages',
            'TAXONOMY_TYPES' => 'Taxonomy Types',
            'TAXONOMY_TYPES_HELP' => 'Taxonomy types must be defined here if you wish to use them in pages',
            'PAGE_SUMMARY' => 'Page Summary',
            'ENABLED' => 'Enabled',
            'ENABLED_HELP' => 'Enable page summary (the summary returns the same as the page content)',
            'YES' => 'Yes',
            'NO' => 'No',
            'SUMMARY_SIZE' => 'Summary Size',
            'SUMMARY_SIZE_HELP' => 'The amount of characters of a page to use as a content summary',
            'FORMAT' => 'Format',
            'FORMAT_HELP' => 'short = use the first occurrence of delimiter or size; long = summary delimiter will be ignored',
            'SHORT' => 'Short',
            'LONG' => 'Long',
            'DELIMITER' => 'Delimiter',
            'DELIMITER_HELP' => 'The summary delimiter (default \'===\')',
            'METADATA' => 'Metadata',
            'METADATA_HELP' => 'Default metadata values that will be displayed on every page unless overridden by the page',
            'NAME' => 'Name',
            'CONTENT' => 'Content',
            'SIZE' => 'Size',
            'ACTION' => 'Action',
            'REDIRECTS_AND_ROUTES' => 'Redirects & Routes',
            'CUSTOM_REDIRECTS' => 'Custom Redirects',
            'CUSTOM_REDIRECTS_HELP' => 'routes to redirect to other pages. Standard Regex replacement is valid',
            'CUSTOM_REDIRECTS_PLACEHOLDER_KEY' => '/your/alias',
            'CUSTOM_REDIRECTS_PLACEHOLDER_VALUE' => '/your/redirect',
            'CUSTOM_ROUTES' => 'Custom Routes',
            'CUSTOM_ROUTES_HELP' => 'routes to alias to other pages. Standard Regex replacement is valid',
            'CUSTOM_ROUTES_PLACEHOLDER_KEY' => '/your/alias',
            'CUSTOM_ROUTES_PLACEHOLDER_VALUE' => '/your/route',
            'FILE_STREAMS' => 'File Streams',
            'DEFAULT' => 'Default',
            'PAGE_MEDIA' => 'Page Media',
            'OPTIONS' => 'Options',
            'PUBLISHED' => 'Published',
            'PUBLISHED_HELP' => 'By default, a page is published unless you explicitly set published: false or via a publish_date being in the future, or unpublish_date in the past',
            'DATE' => 'Date',
            'DATE_HELP' => 'The date variable allows you to specifically set a date associated with this page.',
            'PUBLISHED_DATE' => 'Published Date',
            'PUBLISHED_DATE_HELP' => 'Can provide a date to automatically trigger publication.',
            'UNPUBLISHED_DATE' => 'Unpublished Date',
            'UNPUBLISHED_DATE_HELP' => 'Can provide a date to automatically trigger un-publication.',
            'ROBOTS' => 'Robots',
            'TAXONOMIES' => 'Taxonomies',
            'TAXONOMY' => 'Taxonomy',
            'ADVANCED' => 'Advanced',
            'SETTINGS' => 'Settings',
            'FOLDER_NUMERIC_PREFIX' => 'Folder Numeric Prefix',
            'FOLDER_NUMERIC_PREFIX_HELP' => 'Numeric prefix that provides manual ordering and implies visibility',
            'FOLDER_NAME' => 'Folder Name',
            'FOLDER_NAME_HELP' => 'The folder name that will be stored in the filesystem for this page',
            'PARENT' => 'Parent',
            'DEFAULT_OPTION_ROOT' => '- Root -',
            'DEFAULT_OPTION_SELECT' => '- Select -',
            'DISPLAY_TEMPLATE' => 'Display Template',
            'DISPLAY_TEMPLATE_HELP' => 'The page type that translates into which twig template renders the page',
            'ORDERING' => 'Ordering',
            'PAGE_ORDER' => 'Page Order',
            'OVERRIDES' => 'Overrides',
            'MENU' => 'Menu',
            'MENU_HELP' => 'The string to be used in a menu.  If not set, Title will be used.',
            'SLUG' => 'Slug',
            'SLUG_HELP' => 'The slug variable allows you to specifically set the page\'s portion of the URL',
            'SLUG_VALIDATE_MESSAGE' => 'A slug must contain only lowercase alphanumeric characters and dashes',
            'PROCESS' => 'Process',
            'PROCESS_HELP' => 'Control how pages are processed. Can be set per-page rather than globally',
            'DEFAULT_CHILD_TYPE' => 'Default Child Type',
            'USE_GLOBAL' => 'Use Global',
            'ROUTABLE' => 'Routable',
            'ROUTABLE_HELP' => 'If this page is reachable by a URL',
            'CACHING' => 'Caching',
            'VISIBLE' => 'Visible',
            'VISIBLE_HELP' => 'Determines if a page is visible in the navigation.',
            'DISABLED' => 'Disabled',
            'ITEMS' => 'Items',
            'ORDER_BY' => 'Order By',
            'ORDER' => 'Order',
            'FOLDER' => 'Folder',
            'ASCENDING' => 'Ascending',
            'DESCENDING' => 'Descending',
            'PAGE_TITLE' => 'Page Title',
            'PAGE_TITLE_HELP' => 'The title of the page',
            'PAGE' => 'Page',
            'FRONTMATTER' => 'Frontmatter',
            'FILENAME' => 'Filename',
            'PARENT_PAGE' => 'Parent Page',
            'HOME_PAGE' => 'Home page',
            'HOME_PAGE_HELP' => 'The page that Grav will use as the default landing page',
            'DEFAULT_THEME' => 'Default theme',
            'DEFAULT_THEME_HELP' => 'Set the default theme for Grav to use (default is Antimatter)',
            'TIMEZONE' => 'Timezone',
            'TIMEZONE_HELP' => 'Override the default timezone the server',
            'SHORT_DATE_FORMAT' => 'Short display date format',
            'SHORT_DATE_FORMAT_HELP' => 'Set the short date format that can be used by themes',
            'LONG_DATE_FORMAT' => 'Long display date format',
            'LONG_DATE_FORMAT_HELP' => 'Set the long date format that can be used by themes',
            'DEFAULT_ORDERING' => 'Default ordering',
            'DEFAULT_ORDERING_HELP' => 'Pages in a list will render using this order unless it is overridden',
            'DEFAULT_ORDERING_DEFAULT' => 'Default - based on folder name',
            'DEFAULT_ORDERING_FOLDER' => 'Folder - based on prefix-less folder name',
            'DEFAULT_ORDERING_TITLE' => 'Title - based on title field in header',
            'DEFAULT_ORDERING_DATE' => 'Date - based on date field in header',
            'DEFAULT_ORDER_DIRECTION' => 'Default order direction',
            'DEFAULT_ORDER_DIRECTION_HELP' => 'The direction of pages in a list',
            'DEFAULT_PAGE_COUNT' => 'Default page count',
            'DEFAULT_PAGE_COUNT_HELP' => 'Default maximum pages count in a list',
            'DATE_BASED_PUBLISHING' => 'Date-based publishing',
            'DATE_BASED_PUBLISHING_HELP' => 'Automatically (un)publish posts based on their date',
            'EVENTS' => 'Events',
            'EVENTS_HELP' => 'Enable or Disable specific events.  Disabling these can break plugins',
            'REDIRECT_DEFAULT_ROUTE' => 'Redirect default route',
            'REDIRECT_DEFAULT_ROUTE_HELP' => 'Automatically redirect to a page\'s default route',
            'LANGUAGES' => 'Languages',
            'SUPPORTED' => 'Supported',
            'SUPPORTED_HELP' => 'Comma separated list of 2 letter language codes (for example \'en,fr,de\')',
            'SUPPORTED_PLACEHOLDER' => 'e.g. en, fr',
            'TRANSLATIONS_FALLBACK' => 'Translations fallback',
            'TRANSLATIONS_FALLBACK_HELP' => 'Fallback through supported translations if active language doesn\'t exist',
            'ACTIVE_LANGUAGE_IN_SESSION' => 'Active language in session',
            'ACTIVE_LANGUAGE_IN_SESSION_HELP' => 'Store the active language in the session',
            'HTTP_HEADERS' => 'HTTP Headers',
            'EXPIRES' => 'Expires',
            'EXPIRES_HELP' => 'Sets the expires header. The value is in seconds.',
            'CACHE_CONTROL' => 'HTTP Cache-Control',
            'CACHE_CONTROL_HELP' => 'Set to a valid cache-control value such as `no-cache, no-store, must-revalidate`',
            'CACHE_CONTROL_PLACEHOLDER' => 'e.g. public, max-age=31536000',
            'LAST_MODIFIED' => 'Last modified',
            'LAST_MODIFIED_HELP' => 'Sets the last modified header that can help optimize proxy and browser caching',
            'ETAG' => 'ETag',
            'ETAG_HELP' => 'Sets the etag header to help identify when a page has been modified',
            'VARY_ACCEPT_ENCODING' => 'Vary accept encoding',
            'VARY_ACCEPT_ENCODING_HELP' => 'Sets the `Vary: Accept Encoding` header to help with proxy and CDN caching',
            'MARKDOWN' => 'Markdown',
            'MARKDOWN_EXTRA' => 'Markdown extra',
            'MARKDOWN_EXTRA_HELP' => 'Enable default support for Markdown Extra - https://michelf.ca/projects/php-markdown/extra/',
            'MARKDOWN_EXTRA_ESCAPE_FENCES' => 'Escape HTML elements in markdown extra fences',
            'MARKDOWN_EXTRA_ESCAPE_FENCES_HELP' => 'Escapes HTML elements in markdown extra fences',
            'AUTO_LINE_BREAKS' => 'Auto line breaks',
            'AUTO_LINE_BREAKS_HELP' => 'Enable support for automatic line breaks in markdown',
            'AUTO_URL_LINKS' => 'Auto URL links',
            'AUTO_URL_LINKS_HELP' => 'Enable automatic conversion of URLs into HTML hyperlinks',
            'ESCAPE_MARKUP' => 'Escape markup',
            'ESCAPE_MARKUP_HELP' => 'Escape markup tags into HTML entities',
            'CACHING_HELP' => 'Global ON/OFF switch to enable/disable Grav caching',
            'CACHE_CHECK_METHOD' => 'Cache check method',
            'CACHE_CHECK_METHOD_HELP' => 'Select the method that Grav uses to check if page files have been modified.',
            'CACHE_DRIVER' => 'Cache driver',
            'CACHE_DRIVER_HELP' => 'Choose which cache driver Grav should use. \'Auto Detect\' attempts to find the best for you',
            'CACHE_PREFIX' => 'Cache prefix',
            'CACHE_PREFIX_HELP' => 'An identifier for part of the Grav key.  Don\'t change unless you know what your doing.',
            'CACHE_PREFIX_PLACEHOLDER' => 'Derived from base URL (override by entering random string)',
            'CACHE_PURGE_JOB' => 'Run Scheduled Purge Job',
            'CACHE_PURGE_JOB_HELP' => 'With the scheduler you can periodically clear out old Doctrine file cache folders with this job',
            'CACHE_CLEAR_JOB' => 'Run Scheduled Clear Job',
            'CACHE_CLEAR_JOB_HELP' => 'With the scheduler you can periodically clear the Grav Cache',
            'CACHE_JOB_TYPE' => 'Cache Job Type',
            'CACHE_JOB_TYPE_HELP' => 'Either clear with the \'standard\' folders cache clear, or with \'all\' folders',
            'CACHE_PURGE' => 'Purge Old Cache',
            'LIFETIME' => 'Lifetime',
            'LIFETIME_HELP' => 'Sets the cache lifetime in seconds. 0 = infinite',
            'GZIP_COMPRESSION' => 'Gzip compression',
            'GZIP_COMPRESSION_HELP' => 'Enable GZip compression of the Grav page for increased performance.',
            'TWIG_TEMPLATING' => 'Twig Templating',
            'TWIG_CACHING' => 'Twig caching',
            'TWIG_CACHING_HELP' => 'Control the Twig caching mechanism. Leave this enabled for best performance.',
            'TWIG_DEBUG' => 'Twig debug',
            'TWIG_DEBUG_HELP' => 'Allows the option of not loading the Twig Debugger extension',
            'DETECT_CHANGES' => 'Detect changes',
            'DETECT_CHANGES_HELP' => 'Twig will automatically recompile the Twig cache if it detects any changes in Twig templates',
            'AUTOESCAPE_VARIABLES' => 'Autoescape variables',
            'AUTOESCAPE_VARIABLES_HELP' => 'Autoescapes all variables.  This will break your site most likely',
            'ASSETS' => 'Assets',
            'CSS_PIPELINE' => 'CSS pipeline',
            'CSS_PIPELINE_HELP' => 'The CSS pipeline is the unification of multiple CSS resources into one file',
            'CSS_PIPELINE_INCLUDE_EXTERNALS' => 'Include externals in CSS pipeline',
            'CSS_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'External URLs sometimes have relative file references and shouldn\'t be pipelined',
            'CSS_PIPELINE_BEFORE_EXCLUDES' => 'CSS pipeline render first',
            'CSS_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Render the CSS pipeline before any other CSS references that are not included',
            'CSS_MINIFY' => 'CSS minify',
            'CSS_MINIFY_HELP' => 'Minify the CSS during pipelining',
            'CSS_MINIFY_WINDOWS_OVERRIDE' => 'CSS minify Windows override',
            'CSS_MINIFY_WINDOWS_OVERRIDE_HELP' => 'Minify Override for Windows platforms. False by default due to ThreadStackSize',
            'CSS_REWRITE' => 'CSS rewrite',
            'CSS_REWRITE_HELP' => 'Rewrite any CSS relative URLs during pipelining',
            'JAVASCRIPT_PIPELINE' => 'JavaScript pipeline',
            'JAVASCRIPT_PIPELINE_HELP' => 'The JS pipeline is the unification of multiple JS resources into one file',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS' => 'Include externals in JS pipeline',
            'JAVASCRIPT_PIPELINE_INCLUDE_EXTERNALS_HELP' => 'External URLs sometimes have relative file references and shouldn\'t be pipelined',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES' => 'JS pipeline render first',
            'JAVASCRIPT_PIPELINE_BEFORE_EXCLUDES_HELP' => 'Render the JS pipeline before any other JS references that are not included',
            'JAVASCRIPT_MINIFY' => 'JavaScript minify',
            'JAVASCRIPT_MINIFY_HELP' => 'Minify the JS during pipelining',
            'ENABLED_TIMESTAMPS_ON_ASSETS' => 'Enable timestamps on assets',
            'ENABLED_TIMESTAMPS_ON_ASSETS_HELP' => 'Enable asset timestamps',
            'ENABLED_SRI_ON_ASSETS' => 'Enable SRI on assets',
            'ENABLED_SRI_ON_ASSETS_HELP' => 'Enable asset SRI',
            'COLLECTIONS' => 'Collections',
            'ERROR_HANDLER' => 'Error handler',
            'DISPLAY_ERRORS' => 'Display errors',
            'DISPLAY_ERRORS_HELP' => 'Display full backtrace-style error page',
            'LOG_ERRORS' => 'Log errors',
            'LOG_ERRORS_HELP' => 'Log errors to /logs folder',
            'LOG_HANDLER' => 'Log handler',
            'LOG_HANDLER_HELP' => 'Where to output the logs',
            'SYSLOG_FACILITY' => 'Syslog facility',
            'SYSLOG_FACILITY_HELP' => 'Syslog facility for output',
            'DEBUGGER' => 'Debugger',
            'DEBUGGER_HELP' => 'Enable Grav debugger and following settings',
            'DEBUG_TWIG' => 'Debug Twig',
            'DEBUG_TWIG_HELP' => 'Enable debugging of Twig templates',
            'SHUTDOWN_CLOSE_CONNECTION' => 'Shutdown close connection',
            'SHUTDOWN_CLOSE_CONNECTION_HELP' => 'Close the connection before calling onShutdown(). false for debugging',
            'DEFAULT_IMAGE_QUALITY' => 'Default image quality',
            'DEFAULT_IMAGE_QUALITY_HELP' => 'Default image quality to use when resampling or caching images (85%)',
            'CACHE_ALL' => 'Cache all images',
            'CACHE_ALL_HELP' => 'Run all images through Grav\'s cache system even if they have no media manipulations',
            'IMAGES_DEBUG' => 'Image debug watermark',
            'IMAGES_DEBUG_HELP' => 'Show an overlay over images indicating the pixel depth of the image when working with retina for example',
            'IMAGES_LOADING' => 'Image loading behavior',
            'IMAGES_LOADING_HELP' => 'The loading attribute allows a browser to defer loading offscreen images and iframes until users scroll near them. loading supports three values: auto, lazy, eager',
            'IMAGES_SEOFRIENDLY' => 'SEO-Friendly Image names',
            'IMAGES_SEOFRIENDLY_HELP' => 'When enabled, the image name is displayed first, then a smaller hash to reflect processed operations',
            'UPLOAD_LIMIT' => 'File upload limit',
            'UPLOAD_LIMIT_HELP' => 'Set maximum upload size in bytes (0 is unlimited)',
            'ENABLE_MEDIA_TIMESTAMP' => 'Enable timestamps on media',
            'ENABLE_MEDIA_TIMESTAMP_HELP' => 'Appends a timestamp based on last modified date to each media item',
            'SESSION' => 'Session',
            'SESSION_ENABLED_HELP' => 'Enable session support within Grav',
            'SESSION_NAME_HELP' => 'An identifier used to form the name of the session cookie',
            'SESSION_UNIQUENESS' => 'Unique string',
            'SESSION_UNIQUENESS_HELP' => 'MD5 hash of Grav\'s root path, ie `GRAV_ROOT` (default) or a based on the random `security.salt` string.',
            'ABSOLUTE_URLS' => 'Absolute URLs',
            'ABSOLUTE_URLS_HELP' => 'Absolute or relative URLs for `base_url`',
            'PARAMETER_SEPARATOR' => 'Parameter separator',
            'PARAMETER_SEPARATOR_HELP' => 'Separator for passed parameters that can be changed for Apache on Windows',
            'TASK_COMPLETED' => 'Task completed',
            'EVERYTHING_UP_TO_DATE' => 'Everything is up to date',
            'UPDATES_ARE_AVAILABLE' => 'update(s) are available',
            'IS_AVAILABLE_FOR_UPDATE' => 'is available for update',
            'IS_NOW_AVAILABLE' => 'is now available',
            'CURRENT' => 'Current',
            'UPDATE_GRAV_NOW' => 'Update Grav Now',
            'GRAV_SYMBOLICALLY_LINKED' => 'Grav is symbolically linked. Upgrade won\'t be available',
            'UPDATING_PLEASE_WAIT' => 'Updating... please wait, downloading',
            'OF_THIS' => 'of this',
            'OF_YOUR' => 'of your',
            'HAVE_AN_UPDATE_AVAILABLE' => 'have an update available',
            'SAVE_AS' => 'Save as',
            'MODAL_DELETE_PAGE_CONFIRMATION_REQUIRED_DESC' => 'Are you sure you want to delete this page and all its children? If the page is translated in other languages, those translations will be kept and must be deleted separately. Otherwise the page folder will be deleted along with its subpages. This action cannot be undone.',
            'AND' => 'and',
            'UPDATE_AVAILABLE' => 'Update available',
            'METADATA_KEY' => 'Key (e.g. \'Keywords\')',
            'METADATA_VALUE' => 'Value (e.g. \'Blog, Grav\')',
            'USERNAME_HELP' => 'Username should be between 3 and 16 characters, including lowercase letters, numbers, underscores, and hyphens. Uppercase letters, spaces, and special characters are not allowed',
            'FULLY_UPDATED' => 'Fully Updated',
            'SAVE_LOCATION' => 'Save location',
            'PAGE_FILE' => 'Page Template',
            'PAGE_FILE_HELP' => 'Page template file name, and by default the display template for this page',
            'NO_USER_ACCOUNTS' => 'No user accounts found, please create one first...',
            'NO_USER_EXISTS' => 'No local user exists for this account, cannot save...',
            'REDIRECT_TRAILING_SLASH' => 'Redirect trailing slash',
            'REDIRECT_TRAILING_SLASH_HELP' => 'Perform a 301 redirect rather than transparently handling trailing slash URIs.',
            'DEFAULT_DATE_FORMAT' => 'Page date format',
            'DEFAULT_DATE_FORMAT_HELP' => 'Page date format used by Grav. By default, Grav attempts to guess your date format, however you can specifiy a format using PHP\'s date syntax (e.g.: Y-m-d H:i)',
            'DEFAULT_DATE_FORMAT_PLACEHOLDER' => 'Guess automatically',
            'IGNORE_FILES' => 'Ignore files',
            'IGNORE_FILES_HELP' => 'Specific files to ignore when processing pages',
            'IGNORE_FOLDERS' => 'Ignore folders',
            'IGNORE_FOLDERS_HELP' => 'Specific folders to ignore when processing pages',
            'HIDE_EMPTY_FOLDERS' => 'Hide empty folders',
            'HIDE_EMPTY_FOLDERS_HELP' => 'If folder has no .md file, should it be hidden in navigation as well as being unroutable',
            'HTTP_ACCEPT_LANGUAGE' => 'Set language from browser',
            'HTTP_ACCEPT_LANGUAGE_HELP' => 'You can opt to try to set the language based on `http_accept_language` header tag in the browser',
            'OVERRIDE_LOCALE' => 'Override locale',
            'OVERRIDE_LOCALE_HELP' => 'Override the locale setting in PHP based on the current language',
            'REDIRECT' => 'Page redirect',
            'REDIRECT_HELP' => 'Enter a page route or external URL for this page to redirect to. e.g. `/some/route` or `http://somesite.com`',
            'PLUGIN_STATUS' => 'Plugin status',
            'INCLUDE_DEFAULT_LANG' => 'Include default language',
            'INCLUDE_DEFAULT_LANG_HELP' => 'This will prepend all URLs in the default language with the default language.  e.g. `/en/blog/my-post`',
            'INCLUDE_DEFAULT_LANG_FILE_EXTENSION' => 'Include default language in file extension',
            'INCLUDE_DEFAULT_LANG_HELP_FILE_EXTENSION' => 'If enabled, it will prepend the default language to the file extension (e.g. `.en.md`). Disable it to keep the default language using `.md` file extension.',
            'PAGES_FALLBACK_ONLY' => 'Pages fallback only',
            'PAGES_FALLBACK_ONLY_HELP' => 'Only \'fallback\' to find page content through supported languages, default behavior is to display any language found if active language is missing',
            'ALLOW_URL_TAXONOMY_FILTERS' => 'URL Taxonomy Filters',
            'ALLOW_URL_TAXONOMY_FILTERS_HELP' => 'Page-based collections allow you to filter via `/taxonomy:value`.',
            'REDIRECT_DEFAULT_CODE' => 'Default redirect code',
            'REDIRECT_DEFAULT_CODE_HELP' => 'The HTTP status code to use for redirects',
            'IGNORE_HIDDEN' => 'Ignore hidden',
            'IGNORE_HIDDEN_HELP' => 'Ignore all files and folders that begin with a DOT',
            'WRAPPED_SITE' => 'Wrapped site',
            'WRAPPED_SITE_HELP' => 'For themes/plugins to know if Grav is wrapped by another platform',
            'FALLBACK_TYPES' => 'Allowed fallback types',
            'FALLBACK_TYPES_HELP' => 'Allowed file types that can be found if accessed via Page route. Defaults to any supported media type.',
            'INLINE_TYPES' => 'Inline fallback types',
            'INLINE_TYPES_HELP' => 'A list of file types that should be displayed inline rather than downloaded',
            'APPEND_URL_EXT' => 'Append URL extension',
            'APPEND_URL_EXT_HELP' => 'Will add a custom extension to the Page\'s URL. Note, this will mean Grav will look for `<template>.<extension>.twig` template',
            'PAGE_MODES' => 'Page Modes',
            'PAGE_TYPES' => 'Page Types',
            'PAGE_TYPES_HELP' => 'Determines the page types that Grav supports and the order determines which type to fall back to if the request is ambiguous',
            'ACCESS_LEVELS' => 'Access Levels',
            'GROUPS' => 'Groups',
            'GROUPS_HELP' => 'List of groups the user is part of',
            'ADMIN_ACCESS' => 'Admin Access',
            'SITE_ACCESS' => 'Site Access',
            'INVALID_SECURITY_TOKEN' => 'Invalid Security Token',
            'ACTIVATE' => 'Activate',
            'TWIG_UMASK_FIX' => 'Umask Fix',
            'TWIG_UMASK_FIX_HELP' => 'By default Twig creates cached files as 0755, fix switches this to 0775',
            'CACHE_PERMS' => 'Cache Permissions',
            'CACHE_PERMS_HELP' => 'Default cache folder perms. Usually 0755 or 0775 depending on setup',
            'REMOVE_SUCCESSFUL' => 'Remove Successful',
            'REMOVE_FAILED' => 'Remove Failed',
            'HIDE_HOME_IN_URLS' => 'Hide home route in URLs',
            'HIDE_HOME_IN_URLS_HELP' => 'Will ensure the default routes for any pages under home do not reference home\'s regular route',
            'TWIG_FIRST' => 'Process Twig First',
            'TWIG_FIRST_HELP' => 'If you enabled Twig page processing, then you can configure Twig to process before or after markdown',
            'SESSION_SECURE' => 'Secure',
            'SESSION_SECURE_HELP' => 'If true, indicates that communication for this cookie must be over an encrypted transmission. WARNING: Enable this only on sites that run exclusively on HTTPS',
            'SESSION_HTTPONLY' => 'HTTP Only',
            'SESSION_HTTPONLY_HELP' => 'If true, indicates that cookies should be used only over HTTP, and JavaScript modification is not allowed',
            'REVERSE_PROXY' => 'Reverse Proxy',
            'REVERSE_PROXY_HELP' => 'Enable this if you are behind a reverse proxy and you are having trouble with URLs containing incorrect ports',
            'INVALID_FRONTMATTER_COULD_NOT_SAVE' => 'Invalid frontmatter, could not save',
            'ADD_FOLDER' => 'Add Folder',
            'COPY_PAGE' => 'Copy Page',
            'PROXY_URL' => 'Proxy URL',
            'PROXY_URL_HELP' => 'Enter the proxy HOST or IP and PORT',
            'NOTHING_TO_SAVE' => 'Nothing to Save',
            'FILE_ERROR_ADD' => 'An error occurred while trying to add the file',
            'FILE_ERROR_UPLOAD' => 'An error occurred while trying to upload the file',
            'FILE_UNSUPPORTED' => 'Unsupported file type',
            'ADD_ITEM' => 'Add item',
            'FILE_TOO_LARGE' => 'The file is too large to be uploaded, maximum allowed is %s according <br>to your PHP settings. Increase your `post_max_size` PHP setting',
            'INSTALLING' => 'Installing',
            'LOADING' => 'Loading..',
            'DEPENDENCIES_NOT_MET_MESSAGE' => 'The following dependencies need to be fulfilled first:',
            'ERROR_INSTALLING_PACKAGES' => 'Error while installing the package(s)',
            'INSTALLING_DEPENDENCIES' => 'Installing dependencies...',
            'INSTALLING_PACKAGES' => 'Installing package(s)..',
            'PACKAGES_SUCCESSFULLY_INSTALLED' => 'Package(s) successfully installed.',
            'READY_TO_INSTALL_PACKAGES' => 'Ready to install the package(s)',
            'PACKAGES_NOT_INSTALLED' => 'Packages not installed',
            'PACKAGES_NEED_UPDATE' => 'Packages already installed, but too old',
            'PACKAGES_SUGGESTED_UPDATE' => 'Packages already installed, and version is ok, but will be updated to keep you up to date',
            'REMOVE_THE' => 'Remove the %s',
            'CONFIRM_REMOVAL' => 'Are you sure you want to delete this %s?',
            'REMOVED_SUCCESSFULLY' => '%s removed successfully',
            'ERROR_REMOVING_THE' => 'Error removing the %s',
            'ADDITIONAL_DEPENDENCIES_CAN_BE_REMOVED' => 'The %s required the following dependencies, which are not required by other installed packages. If you are not using them, you can remove them directly from here.',
            'READY_TO_UPDATE_PACKAGES' => 'Ready to update the package(s)',
            'ERROR_UPDATING_PACKAGES' => 'Error while updating the package(s)',
            'UPDATING_PACKAGES' => 'Updating package(s)..',
            'PACKAGES_SUCCESSFULLY_UPDATED' => 'Package(s) successfully updated.',
            'UPDATING' => 'Updating',
            'GPM_RELEASES' => 'GPM Releases',
            'GPM_RELEASES_HELP' => 'Choose \'Testing\' to install beta or testing versions',
            'GPM_METHOD' => 'Remote Fetch Method',
            'GPM_METHOD_HELP' => 'When set to Auto, Grav will determine if fopen is available and use it, otherwise fall back to cURL. To force the use of one or the other switch the setting.',
            'AUTO' => 'Auto',
            'FOPEN' => 'fopen',
            'CURL' => 'cURL',
            'STABLE' => 'Stable',
            'TESTING' => 'Testing',
            'FRONTMATTER_PROCESS_TWIG' => 'Process frontmatter Twig',
            'FRONTMATTER_PROCESS_TWIG_HELP' => 'When enabled you can use Twig config variables in page front matter',
            'FRONTMATTER_IGNORE_FIELDS' => 'Ignore frontmatter fields',
            'FRONTMATTER_IGNORE_FIELDS_HELP' => 'Certain frontmatter fields may contain Twig but should not be processed, such as \'forms\'',
            'FRONTMATTER_IGNORE_FIELDS_PLACEHOLDER' => 'e.g. forms',
            'PACKAGE_X_INSTALLED_SUCCESSFULLY' => 'Package %s installed successfully',
            'ORDERING_DISABLED_BECAUSE_PARENT_SETTING_ORDER' => 'Parent setting order, ordering disabled',
            'ORDERING_DISABLED_BECAUSE_PAGE_NOT_VISIBLE' => 'Page is not visible, ordering disabled',
            'ORDERING_DISABLED_BECAUSE_TOO_MANY_SIBLINGS' => 'Ordering via the admin is unsupported because there are more than 200 siblings',
            'ORDERING_DISABLED_BECAUSE_PAGE_NO_PREFIX' => 'Page ordering is disabled for this page because <strong>Folder Numeric Prefix</strong> is not enabled',
            'CANNOT_ADD_MEDIA_FILES_PAGE_NOT_SAVED' => 'NOTE: You cannot add media files until you save the page. Just click \'Save\' on top',
            'CANNOT_ADD_FILES_PAGE_NOT_SAVED' => 'NOTE: Page must be saved before you can upload files to it.',
            'DROP_FILES_HERE_TO_UPLOAD' => 'Drop your files here or <strong>click in this area</strong>',
            'INSERT' => 'Insert',
            'UNDO' => 'Undo',
            'REDO' => 'Redo',
            'HEADERS' => 'Headers',
            'BOLD' => 'Bold',
            'ITALIC' => 'Italic',
            'STRIKETHROUGH' => 'Strikethrough',
            'SUMMARY_DELIMITER' => 'Summary Delimiter',
            'LINK' => 'Link',
            'IMAGE' => 'Image',
            'BLOCKQUOTE' => 'Blockquote',
            'UNORDERED_LIST' => 'Unordered List',
            'ORDERED_LIST' => 'Ordered List',
            'EDITOR' => 'Editor',
            'PREVIEW' => 'Preview',
            'FULLSCREEN' => 'Fullscreen',
            'NON_ROUTABLE' => 'Non-Routable',
            'NON_VISIBLE' => 'Non-Visible',
            'NON_PUBLISHED' => 'Non-Published',
            'CHARACTERS' => 'characters',
            'PUBLISHING' => 'Publishing',
            'MEDIA_TYPES' => 'Media Types',
            'IMAGE_OPTIONS' => 'Image options',
            'MIME_TYPE' => 'Mime Type',
            'THUMB' => 'Thumb',
            'TYPE' => 'Type',
            'FILE_EXTENSION' => 'File Extension',
            'LEGEND' => 'Page Legend',
            'MEMCACHE_SERVER' => 'Memcache server',
            'MEMCACHE_SERVER_HELP' => 'The Memcache server address',
            'MEMCACHE_PORT' => 'Memcache port',
            'MEMCACHE_PORT_HELP' => 'The Memcache server port',
            'MEMCACHED_SERVER' => 'Memcached server',
            'MEMCACHED_SERVER_HELP' => 'The Memcached server address',
            'MEMCACHED_PORT' => 'Memcached port',
            'MEMCACHED_PORT_HELP' => 'The Memcached server port',
            'REDIS_SERVER' => 'Redis server',
            'REDIS_SERVER_HELP' => 'The Redis server address',
            'REDIS_PORT' => 'Redis port',
            'REDIS_PORT_HELP' => 'The Redis server port',
            'REDIS_PASSWORD' => 'Redis password/secret',
            'REDIS_DATABASE' => 'Redis Database ID',
            'REDIS_DATABASE_HELP' => 'The Redis instance database ID',
            'ALL' => 'All',
            'FROM' => 'from',
            'TO' => 'to',
            'RELEASE_DATE' => 'Release Date',
            'SORT_BY' => 'Sort By',
            'RESOURCE_FILTER' => 'Filter...',
            'FORCE_SSL' => 'Force SSL',
            'FORCE_SSL_HELP' => 'Globally force SSL, if enabled when the site is reached through HTTP, Grav sends a redirect to the HTTPS page',
            'NEWS_FEED' => 'News Feed',
            'EXTERNAL_URL' => 'External URL',
            'SESSION_SAMESITE' => 'The session SameSite attribute',
            'SESSION_SAMESITE_HELP' => 'Lax|Strict|None. See https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite for more info',
            'CUSTOM_BASE_URL' => 'Custom base URL',
            'CUSTOM_BASE_URL_HELP' => 'Use if you want to rewrite the site domain or use a different subfolder than the one used by Grav. Example: http://localhost',
            'FILEUPLOAD_PREVENT_SELF' => 'Cannot use "%s" outside of pages.',
            'FILEUPLOAD_UNABLE_TO_UPLOAD' => 'Unable to upload file %s: %s',
            'FILEUPLOAD_UNABLE_TO_MOVE' => 'Unable to move file %s to "%s"',
            'DROPZONE_CANCEL_UPLOAD' => 'Cancel upload',
            'DROPZONE_CANCEL_UPLOAD_CONFIRMATION' => 'Are you sure you want to cancel this upload?',
            'DROPZONE_DEFAULT_MESSAGE' => 'Drop your files here or <strong>click in this area</strong>',
            'DROPZONE_FALLBACK_MESSAGE' => 'Your browser does not support drag and drop file uploads.',
            'DROPZONE_FALLBACK_TEXT' => 'Please use the fallback form below to upload your files like in the older days.',
            'DROPZONE_FILE_TOO_BIG' => 'File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.',
            'DROPZONE_INVALID_FILE_TYPE' => 'You can\'t upload files of this type.',
            'DROPZONE_MAX_FILES_EXCEEDED' => 'You can not upload any more files.',
            'DROPZONE_REMOVE_FILE' => 'Remove file',
            'DROPZONE_RESPONSE_ERROR' => 'Server responded with {{statusCode}} code.',
            'PREMIUM_PRODUCT' => 'Premium',
            'DESTINATION_NOT_SPECIFIED' => 'Destination not specified',
            'UPLOAD_ERR_NO_TMP_DIR' => 'Missing a temporary folder',
            'SESSION_SPLIT' => 'Session Split',
            'SESSION_SPLIT_HELP' => 'Independent split sessions between site and other plugins (such as admin)',
            'ERROR_FULL_BACKTRACE' => 'Full Backtrace Error',
            'ERROR_SIMPLE' => 'Simple Error',
            'ERROR_SYSTEM' => 'System Error',
            'IMAGES_AUTO_FIX_ORIENTATION' => 'Fix orientation automatically',
            'IMAGES_AUTO_FIX_ORIENTATION_HELP' => 'Automatically fix the image orientation based on the Exif data',
            'REDIS_SOCKET' => 'Redis socket',
            'REDIS_SOCKET_HELP' => 'The Redis socket',
            'NOT_SET' => 'Not set',
            'PERMISSIONS' => 'Permissions',
            'NEVER_CACHE_TWIG' => 'Never Cache Twig',
            'NEVER_CACHE_TWIG_HELP' => 'Only cache content and process Twig every time for pages. Ignores twig_first setting.',
            'ALLOW_WEBSERVER_GZIP' => 'Allow WebServer Gzip',
            'ALLOW_WEBSERVER_GZIP_HELP' => 'Off by default. When enabled, WebServer-configured Gzip/Deflate compression will work, but http connection will not be closed before onShutDown() event causing slower page loading',
            'OFFLINE_WARNING' => 'The connection to the GPM cannot be established',
            'CLEAR_IMAGES_BY_DEFAULT' => 'Clear image cache by default',
            'CLEAR_IMAGES_BY_DEFAULT_HELP' => 'By default processed images are cleared for all cache clears, this can be disabled',
            'CLI_COMPATIBILITY' => 'CLI Compatibility',
            'CLI_COMPATIBILITY_HELP' => 'Ensures that only non-volatile Cache drivers are used (file, redis, memcache, etc.)',
            'REINSTALL_PLUGIN' => 'Reinstall Plugin',
            'REINSTALL_THEME' => 'Reinstall Theme',
            'REINSTALL_THE' => 'Reinstall the %s',
            'CONFIRM_REINSTALL' => 'Are you sure you want to reinstall this %s?',
            'REINSTALLED_SUCCESSFULLY' => '%s reinstalled successfully',
            'ERROR_REINSTALLING_THE' => 'Error reinstalling the %s',
            'PACKAGE_X_REINSTALLED_SUCCESSFULLY' => 'Package %s reinstalled successfully',
            'REINSTALLATION_FAILED' => 'Reinstallation failed',
            'WARNING_REINSTALL_NOT_LATEST_RELEASE' => 'The installed version is not the latest release. By clicking Continue, you\'ll remove the current version and install the latest available release',
            'TOOLS' => 'Tools',
            'DIRECT_INSTALL' => 'Direct Install',
            'NO_PACKAGE_NAME' => 'Package name not specified',
            'PACKAGE_EXTRACTION_FAILED' => 'Package extraction failed',
            'NOT_VALID_GRAV_PACKAGE' => 'Not a valid Grav package',
            'NAME_COULD_NOT_BE_DETERMINED' => 'Name could not be determined',
            'CANNOT_OVERWRITE_SYMLINKS' => 'Cannot overwrite symlinks',
            'ZIP_PACKAGE_NOT_FOUND' => 'ZIP package could not be found',
            'GPM_OFFICIAL_ONLY' => 'Official GPM Only',
            'GPM_OFFICIAL_ONLY_HELP' => 'Only allow direct installs from the official GPM repository only.',
            'NO_CHILD_TYPE' => 'No child type for this rawroute',
            'SORTABLE_PAGES' => 'Sortable Pages:',
            'UNSORTABLE_PAGES' => 'Unsortable Pages',
            'ADMIN_SPECIFIC_OVERRIDES' => 'Admin Specific Overrides',
            'ADMIN_CHILDREN_DISPLAY_ORDER' => 'Children Display Order',
            'ADMIN_CHILDREN_DISPLAY_ORDER_HELP' => 'The order that children of this page should be displayed in the \'Pages\' view of Admin plugin',
            'PWD_PLACEHOLDER' => 'complex string at least 8 chars long',
            'PWD_REGEX' => 'Password Regex',
            'PWD_REGEX_HELP' => 'By default: Password must contain at least one number and one uppercase and lowercase letter, and at least 8 or more characters',
            'USERNAME_PLACEHOLDER' => 'lowercase chars only, e.g. \'admin\'',
            'USERNAME_REGEX' => 'Username Regex',
            'USERNAME_REGEX_HELP' => 'By default: Only lowercase chars, digits, dashes, and underscores. 3 - 16 chars',
            'ENABLE_AUTO_METADATA' => 'Auto metadata from Exif',
            'ENABLE_AUTO_METADATA_HELP' => 'Automatically generate metadata files for images with exif information',
            '2FA_TITLE' => '2-Factor Authentication',
            '2FA_INSTRUCTIONS' => '##### 2-Factor Authentication
You have **2FA** enabled on this account. Please use your **2FA** app to enter the current **6-digit code** to complete the login process.',
            '2FA_REGEN_HINT' => 'Regenerating the secret will require you to update your authenticator app',
            '2FA_LABEL' => 'Admin Access',
            '2FA_FAILED' => 'Invalid 2-Factor Authentication code, please try again...',
            '2FA_ENABLED' => '2FA Enabled',
            '2FA_CODE_INPUT' => '000000',
            '2FA_SECRET' => '2FA Secret',
            '2FA_SECRET_HELP' => 'Scan this QR code into your [Authenticator App](https://learn.getgrav.org/admin-panel/2fa#apps). Also it\'s a good idea to backup the secret in a safe location, in case you need to reinstall your app. Check the [Grav docs](https://learn.getgrav.org/admin-panel/2fa) for more information ',
            '2FA_REGENERATE' => 'Regenerate',
            'FORCE_LOWERCASE_URLS' => 'Force lowercase URLs',
            'FORCE_LOWERCASE_URLS_HELP' => 'By default Grav will set all slugs and routes to be lowercase. With this set to false, Uppercase slugs and routes can be used',
            'INTL_ENABLED' => 'Intl module integration',
            'INTL_ENABLED_HELP' => 'Use Intl PHP module and collate to sort UTF8 based collections',
            'VIEW_SITE_TIP' => 'View site',
            'TOOLS_DIRECT_INSTALL_TITLE' => 'Direct Install of Grav Packages',
            'TOOLS_DIRECT_INSTALL_UPLOAD_TITLE' => 'Install Package via Direct ZIP Upload',
            'TOOLS_DIRECT_INSTALL_UPLOAD_DESC' => 'You can easily install a valid Grav <strong>theme</strong>, <strong>plugin</strong>, or even <strong>Grav</strong> update Zip package via this method.  This package does not have to be registered via GPM and allows you to easily roll back to a prior version or install for testing.',
            'TOOLS_DIRECT_INSTALL_URL_TITLE' => 'Install Package via Remote URL Reference',
            'TOOLS_DIRECT_INSTALL_URL_DESC' => 'Alternatively, you can also reference a full URL to the package ZIP file and install it via this remote URL.',
            'TOOLS_DIRECT_INSTALL_UPLOAD_BUTTON' => 'Upload and install',
            'ROUTE_OVERRIDES' => 'Route Overrides',
            'ROUTE_DEFAULT' => 'Default Route',
            'ROUTE_CANONICAL' => 'Canonical Route',
            'ROUTE_ALIASES' => 'Route Aliases',
            'OPEN_NEW_TAB' => 'Open in new tab',
            'SESSION_INITIALIZE' => 'Initialize Session',
            'SESSION_INITIALIZE_HELP' => 'Makes Grav to start a session. This feature is needed to make any user interaction to work, such as login, forms etc. Admin plugin isn\'t affected by this setting.',
            'STRICT_YAML_COMPAT' => 'YAML Compatibility',
            'STRICT_YAML_COMPAT_HELP' => 'Falls back to Symfony 2.4 YAML parser if Native or 3.4 parser fails',
            'STRICT_TWIG_COMPAT' => 'Twig Compatibility',
            'STRICT_TWIG_COMPAT_HELP' => 'Enables deprecated Twig autoescape setting.  When disabled, |raw filter is required to output HTML as Twig will autoescape output',
            'SCHEDULER' => 'Scheduler',
            'SCHEDULER_INSTALL_INSTRUCTIONS' => 'Install Instructions',
            'SCHEDULER_INSTALLED_READY' => 'Installed and Ready',
            'SCHEDULER_CRON_NA' => 'Cron Not Available for user: <b>%s</b>',
            'SCHEDULER_NOT_ENABLED' => 'Not Enabled for user: <b>%s</b>',
            'SCHEDULER_SETUP' => 'Scheduler Setup',
            'SCHEDULER_INSTRUCTIONS' => 'The <b>Grav Scheduler</b> allows you to create and schedule custom jobs.  It also provides a method for Grav plugins to integrate programmatically and dynamically add jobs to be run periodically.',
            'SCHEDULER_POST_INSTRUCTIONS' => 'To enable the Scheduler\'s functionality, you must add the <b>Grav Scheduler</b> to your system\'s crontab file for the <b>%s</b> user.  Run the command above from the terminal to add it automatically. Once saved, refresh this page to see the status.',
            'SCHEDULER_JOBS' => 'Custom Scheduler Jobs',
            'SCHEDULER_STATUS' => 'Scheduler Status',
            'SCHEDULER_RUNAT' => 'Run At',
            'SCHEDULER_OUTPUT' => 'Output File',
            'SCHEDULER_OUTPUT_HELP' => 'The path/filename of the output file (from the root of the Grav installation)',
            'SCHEDULER_OUTPUT_TYPE' => 'Output Type',
            'SCHEDULER_OUTPUT_TYPE_HELP' => 'Either append to the same file each run, or overwrite the file with each run',
            'SCHEDULER_EMAIL' => 'Email',
            'SCHEDULER_EMAIL_HELP' => 'Email to send output to. NOTE: requires output file to be set',
            'SCHEDULER_WARNING' => 'The scheduler uses your system\'s crontab system to execute commands. You should use this only if you are an advanced user and know what you are doing. Misconfiguration or abuse can lead to security vulnerabilities.',
            'SECURITY' => 'Security',
            'XSS_SECURITY' => 'XSS Security for Content',
            'XSS_WHITELIST_PERMISSIONS' => 'Whitelist Permissions',
            'XSS_WHITELIST_PERMISSIONS_HELP' => 'Users with these permissions will skip the XSS rules when saving content',
            'XSS_ON_EVENTS' => 'Filter On-events',
            'XSS_INVALID_PROTOCOLS' => 'Filter Invalid protocols',
            'XSS_INVALID_PROTOCOLS_LIST' => 'Invalid protocols list',
            'XSS_MOZ_BINDINGS' => 'Filter Moz bindings',
            'XSS_HTML_INLINE_STYLES' => 'Filter HTML inline styles',
            'XSS_DANGEROUS_TAGS' => 'Filter Dangerous HTML tags',
            'XSS_DANGEROUS_TAGS_LIST' => 'Dangerous HTML tags list',
            'XSS_ONSAVE_ISSUE' => 'Save failed: XSS issue detected...',
            'XSS_ISSUE' => '<strong>NOTICE:</strong> Grav found potential XSS issues in <strong>%s</strong>',
            'UPLOADS_SECURITY' => 'Uploads Security',
            'UPLOADS_DANGEROUS_EXTENSIONS' => 'Dangerous Extensions',
            'UPLOADS_DANGEROUS_EXTENSIONS_HELP' => 'Block these extensions from being uploaded no matter the accepted MIME types',
            'REPORTS' => 'Reports',
            'LOGS' => 'Logs',
            'LOG_VIEWER_FILES' => 'Log Viewer Files',
            'LOG_VIEWER_FILES_HELP' => 'Files in /logs/ that will be available to view in Tools - Logs. e.g. \'grav\' = /logs/grav.log',
            'BACKUPS_STORAGE_PURGE_TRIGGER' => 'Backup Storage Purge Trigger',
            'BACKUPS_MAX_COUNT' => 'Maximum Number of Backups',
            'BACKUPS_MAX_COUNT_HELP' => '0 is unlimited',
            'BACKUPS_MAX_SPACE' => 'Maximum Backups Space',
            'BACKUPS_MAX_RETENTION_TIME' => 'Maximum Retention Time',
            'BACKUPS_MAX_RETENTION_TIME_APPEND' => 'in Days',
            'BACKUPS_PROFILE_NAME' => 'Backup Name',
            'BACKUPS_PROFILE_ROOT_FOLDER' => 'Root Folder',
            'BACKUPS_PROFILE_ROOT_FOLDER_HELP' => 'Can be an absolute path or a stream',
            'BACKUPS_PROFILE_EXCLUDE_PATHS' => 'Exclude Paths',
            'BACKUPS_PROFILE_EXCLUDE_PATHS_HELP' => 'Absolute paths to exclude, one per line',
            'BACKUPS_PROFILE_EXCLUDE_FILES' => 'Exclude Files',
            'BACKUPS_PROFILE_EXCLUDE_FILES_HELP' => 'Specific Files or Folders to exclude, one per line',
            'BACKUPS_PROFILE_SCHEDULE' => 'Enable Scheduled Job',
            'BACKUPS_PROFILE_SCHEDULE_AT' => 'Run Scheduled Job',
            'COMMAND' => 'Command',
            'EXTRA_ARGUMENTS' => 'Extra Arguments',
            'DEFAULT_LANG' => 'Override Default Language',
            'DEFAULT_LANG_HELP' => 'Default is the first supported language. This can be overridden by setting this option but it must be one of the supported languages',
            'DEBUGGER_PROVIDER' => 'Debugger Provider',
            'DEBUGGER_PROVIDER_HELP' => 'Default is PHP Debug Bar, but Clockwork browser extension provides for a less intrusive approach',
            'DEBUGGER_DEBUGBAR' => 'PHP Debug Bar',
            'DEBUGGER_CLOCKWORK' => 'Clockwork Browser Extension',
            'PAGE_ROUTE_NOT_FOUND' => 'Page route not found',
            'PAGE_ROUTE_FOUND' => 'Page route found',
            'NO_ROUTE_PROVIDED' => 'No route provided',
            'CONTENT_LANGUAGE_FALLBACKS' => 'Content Language Fallback',
            'CONTENT_LANGUAGE_FALLBACKS_HELP' => 'By default if the content isn\'t translated, Grav will display content in the default language. Use this setting to override that behavior per language basis.',
            'CONTENT_LANGUAGE_FALLBACK' => 'Fallback Languages',
            'CONTENT_LANGUAGE_FALLBACK_HELP' => 'Please enter a list of language codes. Note that if you omit default language code, it will not be used.',
            'CONTENT_FALLBACK_LANGUAGE_HELP' => 'Specify language code which you want to customize.',
            'EXPERIMENTAL' => 'Experimental',
            'PAGES_TYPE' => 'Frontend Page Type',
            'PAGES_TYPE_HELP' => 'This option enables Flex Object pages on the front-end. Admin Flex Pages requires Flex Objects plugin',
            'ACCOUNTS_TYPE' => 'Accounts Type',
            'ACCOUNTS_TYPE_HELP' => 'Flex Object system to store user accounts',
            'ACCOUNTS_STORAGE' => 'Account Storage',
            'ACCOUNTS_STORAGE_HELP' => 'The storage mechanism to be used for Flex Object Account type.  Files is the traditional approach where account are stored in a YAML file in a single folder, while Folder creates a new folder for each account',
            'FLEX' => 'Flex Object (EXPERIMENTAL)',
            'REGULAR' => 'Regular',
            'FILE' => 'File',
            'SANITIZE_SVG' => 'Sanitize SVG',
            'SANITIZE_SVG_HELP' => 'Removes any XSS code from SVG',
            'ACCOUNTS' => 'Accounts',
            'USER_ACCOUNTS' => 'User Accounts',
            'USER_GROUPS' => 'User Groups',
            'GROUP_NAME' => 'Group Name',
            'DISPLAY_NAME' => 'Display Name',
            'ICON' => 'Icon',
            'ACCESS' => 'Access',
            'NO_ACCESS' => 'No Access',
            'SUPER_USER' => 'Super User',
            'ALLOWED' => 'Allowed',
            'DENIED' => 'Denied',
            'MODULE' => 'Module',
            'NON_MODULE' => 'Non-Module',
            'ADD_MODULE' => 'Add Module',
            'MODULE_SETUP' => 'Module Setup',
            'MODULE_TEMPLATE' => 'Module Template',
            'ADD_MODULE_CONTENT' => 'Add Module Content',
            'CHANGELOG' => 'Changelog',
            'PAGE_ACCESS' => 'Page Access',
            'PAGE PERMISSIONS' => 'Page Permissions',
            'PAGE_ACCESS_HELP' => 'User with following access permissions can access the page.',
            'PAGE_VISIBILITY_REQUIRES_ACCESS' => 'Menu Visibility Requires Access',
            'PAGE_VISIBILITY_REQUIRES_ACCESS_HELP' => 'Set to Yes if page should be shown in menus only if user can access them.',
            'PAGE_INHERIT_PERMISSIONS' => 'Inherit Permissions',
            'PAGE_INHERIT_PERMISSIONS_HELP' => 'Inherit ACL from parent page.',
            'PAGE_AUTHORS' => 'Page Authors',
            'PAGE_AUTHORS_HELP' => 'Members of Page Authors have owner level access to this page defined in special \'Authors\' page group.',
            'PAGE_GROUPS' => 'Page Groups',
            'PAGE_GROUPS_HELP' => 'Members of Page Groups have special access to this page.',
            'READ' => 'Read',
            'PUBLISH' => 'Publish',
            'LIST' => 'List',
            'ACCESS_SITE' => 'Site',
            'ACCESS_SITE_LOGIN' => 'Login to Site',
            'ACCESS_ADMIN' => 'Admin',
            'ACCESS_ADMIN_LOGIN' => 'Login to Admin',
            'ACCESS_ADMIN_SUPER' => 'Super User',
            'ACCESS_ADMIN_CACHE' => 'Clear Cache',
            'ACCESS_ADMIN_CONFIGURATION' => 'Configuration',
            'ACCESS_ADMIN_CONFIGURATION_SYSTEM' => 'Manage System Configuration',
            'ACCESS_ADMIN_CONFIGURATION_SITE' => 'Manage Site Configuration',
            'ACCESS_ADMIN_CONFIGURATION_MEDIA' => 'Manage Media Configuration',
            'ACCESS_ADMIN_CONFIGURATION_INFO' => 'See Server Information',
            'ACCESS_ADMIN_SETTINGS' => 'Settings',
            'ACCESS_ADMIN_PAGES' => 'Manage Pages',
            'ACCESS_ADMIN_MAINTENANCE' => 'Site Maintenance',
            'ACCESS_ADMIN_STATISTICS' => 'Site Statistics',
            'ACCESS_ADMIN_PLUGINS' => 'Manage Plugins',
            'ACCESS_ADMIN_THEMES' => 'Manage Themes',
            'ACCESS_ADMIN_TOOLS' => 'Access to Tools',
            'ACCESS_ADMIN_USERS' => 'Manage Users',
            'USERS' => 'Users',
            'ACL' => 'Access Control',
            'FLEX_CACHING' => 'Flex Caching',
            'FLEX_INDEX_CACHE_ENABLED' => 'Enable Index Caching',
            'FLEX_INDEX_CACHE_LIFETIME' => 'Index Cache Lifetime (seconds)',
            'FLEX_OBJECT_CACHE_ENABLED' => 'Enable Object Caching',
            'FLEX_OBJECT_CACHE_LIFETIME' => 'Object Cache Lifetime (seconds)',
            'FLEX_RENDER_CACHE_ENABLED' => 'Enable Render Caching',
            'FLEX_RENDER_CACHE_LIFETIME' => 'Render Cache Lifetime (seconds)',
            'DEBUGGER_CENSORED' => 'Censor Sensitive Data',
            'DEBUGGER_CENSORED_HELP' => 'Clockwork Provider ONLY: If Yes, censor potentially sensitive information (POST parameters, cookies, files, configuration and most array/object data in log messages)',
            'LANGUAGE_TRANSLATIONS' => 'Translations',
            'LANGUAGE_TRANSLATIONS_HELP' => 'If false, translation keys are used instead of translated strings. This feature can be used to help to fix bad translations or to find hardcoded English strings.',
            'STRICT_BLUEPRINT_COMPAT' => 'Blueprint Compatibility',
            'STRICT_BLUEPRINT_COMPAT_HELP' => 'Enables backward compatible strict support for blueprints. If turned off, the new behavior makes the form validation to fail if there is extra data which is not defined in the blueprint.',
            'RESET' => 'Reset',
            'LOGOS' => 'Logos',
            'PRESETS' => 'Presets',
            'COLOR_SCHEME_LABEL' => 'Color Scheme',
            'COLOR_SCHEME_HELP' => 'Choose a color scheme from a list of predefined combinations, or add your own style',
            'COLOR_SCHEME_NAME' => 'Custom Color Scheme Name',
            'COLOR_SCHEME_NAME_HELP' => 'Give a name to your custom theme for exporting and sharing',
            'COLOR_SCHEME_NAME_PLACEHOLDER' => 'Shades of Blue',
            'PRIMARY_ACCENT_LABEL' => 'Primary Accent',
            'PRIMARY_ACCENT_HELP' => 'Select which color set the primary accent should use for it\'s color scheme',
            'SECONDARY_ACCENT_LABEL' => 'Secondary Accent',
            'SECONDARY_ACCENT_HELP' => 'Select which color set the secondary accent should use for it\'s color scheme',
            'TERTIARY_ACCENT_LABEL' => 'Tertiary Accent',
            'TERTIARY_ACCENT_HELP' => 'Select which color set the tertiary accent should use for it\'s color scheme',
            'WEB_FONTS_LABEL' => 'Web Fonts',
            'WEB_FONTS_HELP' => 'Use custom web fonts',
            'HEADER_FONT_LABEL' => 'Header Font',
            'HEADER_FONT_HELP' => 'Font used for headers, side nav and section titles',
            'BODY_FONT_LABEL' => 'Body Font',
            'BODY_FONT_HELP' => 'Primary font used throughout the body of the theme',
            'CUSTOM_CSS_LABEL' => 'Custom CSS',
            'CUSTOM_CSS_PLACEHOLDER' => 'Put your custom CSS in here...',
            'CUSTOM_CSS_HELP' => 'Custom CSS that will be added to every admin page',
            'CUSTOM_FOOTER' => 'Custom Footer',
            'CUSTOM_FOOTER_HELP' => 'You can use HTML and/or Markdown syntax here',
            'CUSTOM_FOOTER_PLACEHOLDER' => 'Enter HTML/Markdown to override default footer',
            'LOGIN_SCREEN_CUSTOM_LOGO_LABEL' => 'Login Custom Logo',
            'TOP_LEFT_CUSTOM_LOGO_LABEL' => 'Primary Custom Logo',
            'LOAD_PRESET' => 'Load Preset',
            'RECOMPILE' => 'Recompile',
            'EXPORT' => 'Export',
            'QUICKTRAY_RECOMPILE' => 'QuickTray Recompile Icon',
            'QUICKTRAY_RECOMPILE_HELP' => 'Will recompile the preset SCSS to pickup any changes or new plugins',
            'CODEMIRROR' => 'CodeMirror Editor',
            'CODEMIRROR_THEME' => 'Editor Theme',
            'CODEMIRROR_THEME_DESC' => '**NOTE:** Use the [CodeMirror Themes Demo](https://codemirror.net/demo/theme.html?target=_blank) to see these in action. **_Paper_** is the default Grav theme.',
            'CODEMIRROR_FONTSIZE' => 'Editor Font Size',
            'CODEMIRROR_FONTSIZE_SM' => 'Small Font',
            'CODEMIRROR_FONTSIZE_MD' => 'Medium Font',
            'CODEMIRROR_FONTSIZE_LG' => 'Large Font',
            'CODEMIRROR_MD_FONT' => 'Markdown Editor Font',
            'CODEMIRROR_MD_FONT_SANS' => 'Sans Font',
            'CODEMIRROR_MD_FONT_MONO' => 'Mono/Fixed Width Font',
            'CUSTOM_PRESETS' => 'Custom Presets',
            'CUSTOM_PRESETS_HELP' => 'Drag-n-drop a theme .yaml file here, or you can create an array of presets with text based keys',
            'CUSTOM_PRESETS_PLACEHOLDER' => 'Put your presets here',
            'GENERAL' => 'General',
            'CONTENT_EDITOR' => 'Content Editor',
            'CONTENT_EDITOR_HELP' => 'Custom editors can be preferred for content editing',
            'BAD_FILENAME' => 'Bad filename',
            'SHOW_SENSITIVE' => 'Show Sensitive Data',
            'SHOW_SENSITIVE_HELP' => 'Clockwork Provider ONLY: Censor potentially sensitive information (POST parameters, cookies, files, configuration and most array/object data in log messages)',
            'VALID_LINK_ATTRIBUTES' => 'Valid Link Attributes',
            'VALID_LINK_ATTRIBUTES_HELP' => 'Attributes that will be automatically added to the media HTML element',
            'CONFIGURATION' => 'Configuration',
            'CUSTOMIZATION' => 'Customization',
            'EXTRAS' => 'Extras',
            'BASICS' => 'Basics',
            'ADMIN_CACHING' => 'Enable Admin Caching',
            'ADMIN_CACHING_HELP' => 'Caching in the admin can be controlled independently from the front-end site',
            'ADMIN_PATH' => 'Administrator path',
            'ADMIN_PATH_PLACEHOLDER' => 'Default route for administrator (relative to base)',
            'ADMIN_PATH_HELP' => 'If you want to change the URL for the administrator, you can provide a path here',
            'LOGO_TEXT' => 'Logo text',
            'LOGO_TEXT_HELP' => 'Text to display in place of the default Grav logo',
            'CONTENT_PADDING' => 'Content padding',
            'CONTENT_PADDING_HELP' => 'Enable/Disable content padding around content area to provide more space',
            'BODY_CLASSES' => 'Body classes',
            'BODY_CLASSES_HELP' => 'Add a space separated name of custom body classes',
            'SIDEBAR_ACTIVATION' => 'Sidebar Activation',
            'SIDEBAR_ACTIVATION_HELP' => 'Control how the sidebar is activated',
            'SIDEBAR_HOVER_DELAY' => 'Hover delay',
            'SIDEBAR_HOVER_DELAY_APPEND' => 'millseconds',
            'SIDEBAR_ACTIVATION_TAB' => 'Tab',
            'SIDEBAR_ACTIVATION_HOVER' => 'Hover',
            'SIDEBAR_SIZE' => 'Sidebar Size',
            'SIDEBAR_SIZE_HELP' => 'Control the width of the sidebar',
            'SIDEBAR_SIZE_AUTO' => 'Automatic width',
            'SIDEBAR_SIZE_SMALL' => 'Small width',
            'EDIT_MODE' => 'Edit mode',
            'EDIT_MODE_HELP' => 'Auto will use blueprint if available, if none found, it will use "Expert" mode.',
            'FRONTEND_PREVIEW_TARGET' => 'Preview pages target',
            'FRONTEND_PREVIEW_TARGET_INLINE' => 'Inline in Admin',
            'FRONTEND_PREVIEW_TARGET_NEW' => 'New tab',
            'FRONTEND_PREVIEW_TARGET_CURRENT' => 'Current tab',
            'PARENT_DROPDOWN' => 'Parent dropdown',
            'PARENT_DROPDOWN_BOTH' => 'Show slug and folder',
            'PARENT_DROPDOWN_FOLDER' => 'Show folder',
            'PARENT_DROPDOWN_FULLPATH' => 'Show fullpath',
            'PARENTS_LEVELS' => 'Parents levels',
            'PARENTS_LEVELS_HELP' => 'The number of levels to show in parent select list',
            'MODULAR_PARENTS' => 'Modular parents',
            'MODULAR_PARENTS_HELP' => 'Show modular pages in the parent select list',
            'SHOW_GITHUB_LINK' => 'Show GitHub Link',
            'SHOW_GITHUB_LINK_HELP' => 'Show the "Found an issue? Please report it on GitHub." message.',
            'PAGES_LIST_DISPLAY_FIELD' => 'Pages List Display Field',
            'PAGES_LIST_DISPLAY_FIELD_HELP' => 'Field of the page to use in the list of pages if present. Defaults/Fallback to title.',
            'AUTO_UPDATES' => 'Automatically check for updates',
            'AUTO_UPDATES_HELP' => 'Shows an informative message, in the admin panel, when an update is available.',
            'TIMEOUT' => 'Timeout',
            'TIMEOUT_HELP' => 'Sets the session timeout in seconds',
            'HIDE_PAGE_TYPES' => 'Hide page types in Admin',
            'HIDE_MODULAR_PAGE_TYPES' => 'Hide modular page types in Admin',
            'DASHBOARD' => 'Dashboard',
            'WIDGETS_DISPLAY' => 'Widget Display Status',
            'NOTIFICATIONS' => 'Notifications',
            'FEED_NOTIFICATIONS' => 'Feed Notifications',
            'FEED_NOTIFICATIONS_HELP' => 'Display feed-based notifications',
            'DASHBOARD_NOTIFICATIONS' => 'Dashboard Notifications',
            'DASHBOARD_NOTIFICATIONS_HELP' => 'Display dashboard-based notifications',
            'PLUGINS_NOTIFICATIONS' => 'Plugins Notifications',
            'PLUGINS_NOTIFICATIONS_HELP' => 'Display plugins-targeted notifications',
            'THEMES_NOTIFICATIONS' => 'Themes Notifications',
            'THEMES_NOTIFICATIONS_HELP' => 'Display themes-targeted notifications',
            'LOGO_BG_HELP' => 'Logo bg',
            'LOGO_LINK_HELP' => 'Logo link',
            'NAV_BG_HELP' => 'Nav bg',
            'NAV_TEXT_HELP' => 'Nav text',
            'NAV_LINK_HELP' => 'Nav link',
            'NAV_SELECTED_BG_HELP' => 'Nav selected bg',
            'NAV_SELECTED_LINK_HELP' => 'Nav selected link',
            'NAV_HOVER_BG_HELP' => 'Nav hover bg',
            'NAV_HOVER_LINK_HELP' => 'Nav hover link',
            'TOOLBAR_BG_HELP' => 'Toolbar bg',
            'TOOLBAR_TEXT_HELP' => 'Toolbar text',
            'PAGE_BG_HELP' => 'Page bg',
            'PAGE_TEXT_HELP' => 'Page text',
            'PAGE_LINK_HELP' => 'Page link',
            'CONTENT_BG_HELP' => 'Content bg',
            'CONTENT_TEXT_HELP' => 'Content text',
            'CONTENT_LINK_HELP' => 'Content link',
            'CONTENT_LINK2_HELP' => 'Content link 2',
            'CONTENT_HEADER_HELP' => 'Content header',
            'CONTENT_TABS_BG_HELP' => 'Content tabs bg',
            'CONTENT_TABS_TEXT_HELP' => 'Content tabs text',
            'CONTENT_HIGHLIGHT_HELP' => 'Content highlight',
            'BUTTON_BG_HELP' => 'Button bg',
            'BUTTON_TEXT_HELP' => 'Button text',
            'NOTICE_BG_HELP' => 'Notice bg',
            'NOTICE_TEXT_HELP' => 'Notice text',
            'UPDATES_BG_HELP' => 'Updates bg',
            'UPDATES_TEXT_HELP' => 'Updates text',
            'CRITICAL_BG_HELP' => 'Critical bg',
            'CRITICAL_TEXT_HELP' => 'Critical text',
            'BUTTON_COLORS' => 'Button colors',
            'CONTENT_COLORS' => 'Content colors',
            'TABS_COLORS' => 'Tabs colors',
            'CRITICAL_COLORS' => 'Critical colors',
            'LOGO_COLORS' => 'Logo colors',
            'NAV_COLORS' => 'Nav colors',
            'NOTICE_COLORS' => 'Notice colors',
            'PAGE_COLORS' => 'Page colors',
            'TOOLBAR_COLORS' => 'Toolbar colors',
            'UPDATE_COLORS' => 'Update colors',
            'POPULARITY' => 'Popularity',
            'VISITOR_TRACKING' => 'Visitor tracking',
            'VISITOR_TRACKING_HELP' => 'Enable the visitors stats collecting feature',
            'DAYS_OF_STATS' => 'Days of stats',
            'DAYS_OF_STATS_HELP' => 'Keep stats for the specified number of days, then drop them',
            'IGNORE_URLS' => 'Ignore',
            'IGNORE_URLS_HELP' => 'URLs to ignore',
            'DAILY_HISTORY' => 'Daily history',
            'MONTHLY_HISTORY' => 'Monthly history',
            'VISITORS_HISTORY' => 'Visitors history',
            'MEDIA_RESIZE' => 'Page Media Image Resizer',
            'PAGEMEDIA_RESIZER' => '> The following settings apply to images uploaded through the page media. Resize Width / Height will automatically resize down and proportionally an image if exceeds the limits set in the inputs. Resolution min and max values define the size ranges for uploaded images. Set the fields to 0 to prevent any manipulation.',
            'RESIZE_WIDTH' => 'Resize Width',
            'RESIZE_WIDTH_HELP' => 'Resize wide images down to the set value',
            'RESIZE_HEIGHT' => 'Resize Height',
            'RESIZE_HEIGHT_HELP' => 'Resize tall images down to the set value',
            'RES_MIN_WIDTH' => 'Resolution Min Width',
            'RES_MIN_WIDTH_HELP' => 'The minimum width allowed for an image to be added',
            'RES_MIN_HEIGHT' => 'Resolution Min Height',
            'RES_MIN_HEIGHT_HELP' => 'The minimum height allowed for an image to be added',
            'RES_MAX_WIDTH' => 'Resolution Max Width',
            'RES_MAX_WIDTH_HELP' => 'The maximum width allowed for an image to be added',
            'RES_MAX_HEIGHT' => 'Resolution Max Height',
            'RES_MAX_HEIGHT_HELP' => 'The maximum height allowed for an image to be added',
            'RESIZE_QUALITY' => 'Resize Quality',
            'RESIZE_QUALITY_HELP' => 'The quality to use when resizing an image. Between 0 and 1 value.',
            'PIXELS' => 'pixels',
            'ACCESS_ADMIN_CONFIGURATION_SECURITY' => 'Manage Security Configuration',
            'SESSION_DOMAIN' => 'Session domain',
            'SESSION_DOMAIN_HELP' => 'Use only if you you rewrite the site domain for example in a Docker Container.',
            'SESSION_PATH' => 'Session path',
            'SESSION_PATH_HELP' => 'Use only if you you rewrite the site path for example in a Docker Container.',
            'REDIRECT_OPTION_NO_REDIRECT' => 'No redirect',
            'REDIRECT_OPTION_DEFAULT_REDIRECT' => 'Use default redirect code',
            'REDIRECT_OPTION_301' => '301 - Moved permanently',
            'REDIRECT_OPTION_302' => '302 - Moved temporarily',
            'REDIRECT_OPTION_303' => '303 - See Other',
            'IMAGES_CLS_TITLE' => 'Cumulative Layout Shift (CLS)',
            'IMAGES_CLS_AUTO_SIZES' => 'Enable Auto Sizes',
            'IMAGES_CLS_AUTO_SIZES_HELP' => 'Automatically add \'width\' and \'height\' attributes to images to address CLS',
            'IMAGES_CLS_ASPECT_RATIO' => 'Enable Aspect Ratio',
            'IMAGES_CLS_ASPECT_RATIO_HELP' => 'Optional CSS variable that gets applied via a \'style\' attribute which can be used in CSS for custom styling',
            'IMAGES_CLS_RETINA_SCALE' => 'Retina Scale Factor',
            'IMAGES_CLS_RETINA_SCALE_HELP' => 'Will take the calculated size and divide by scale factor to display a higher resolution image at a smaller pixel size for better handling of HiDPI resolutions',
            'AUTOREGENERATE_FOLDER_SLUG' => 'Auto-regenerate based on page title',
            'ENABLE' => 'Enable',
            'PLUGINS_MUST_BE_ENABLED' => 'Plugin must be enabled to configure',
            'ACTIVATION_REQUIRED' => 'Activation required to configure'
        ]
    ]
];
