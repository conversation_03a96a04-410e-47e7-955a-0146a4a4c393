<?php
return [
    '@class' => 'Grav\\Common\\File\\CompiledYamlFile',
    'filename' => 'C:/xampp8.2.4/htdocs/drain-form/user/plugins/markdown-notices/languages.yaml',
    'modified' => 1711576431,
    'size' => 1557,
    'data' => [
        'en' => [
            'PLUGIN_MARKDOWN_NOTICES' => [
                'USE_BUILT_IN_CSS' => 'Use built-in CSS',
                'BASE_CLASSES' => 'Base classes',
                'BASE_CLASSES_HELP' => 'These classes will be added before the class level',
                'BASE_CLASSES_PLACEHOLDER' => 'e.g. notices',
                'LEVEL_CLASSES' => 'Level classes',
                'LEVEL_CLASSES_HELP' => 'The classes to use for each level of notices depth',
                'LEVEL_CLASSES_PLACEHOLDER' => 'e.g. yellow, red, blue, green'
            ]
        ],
        'ru' => [
            'PLUGIN_MARKDOWN_NOTICES' => [
                'USE_BUILT_IN_CSS' => 'Использовать встроенный CSS',
                'BASE_CLASSES' => 'Базовые классы',
                'BASE_CLASSES_HELP' => 'Эти классы будут добавлены до уровня класса',
                'BASE_CLASSES_PLACEHOLDER' => 'например notices',
                'LEVEL_CLASSES' => 'Классы уровней',
                'LEVEL_CLASSES_HELP' => 'Эти классы используются на каждом уровне глубины уведомлений',
                'LEVEL_CLASSES_PLACEHOLDER' => 'например yellow, red, blue, green'
            ]
        ],
        'uk' => [
            'PLUGIN_MARKDOWN_NOTICES' => [
                'USE_BUILT_IN_CSS' => 'Використовувати вбудований CSS',
                'BASE_CLASSES' => 'Базові класи',
                'BASE_CLASSES_HELP' => 'Ці класи будуть додані до рівня класу',
                'BASE_CLASSES_PLACEHOLDER' => 'наприклад notices',
                'LEVEL_CLASSES' => 'Класи рівнів',
                'LEVEL_CLASSES_HELP' => 'Ці класи використовуються на кожному рівні глибини повідомлень',
                'LEVEL_CLASSES_PLACEHOLDER' => 'наприклад yellow, red, blue, green'
            ]
        ]
    ]
];
