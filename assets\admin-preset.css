body {
  background: #F6F6F6;
  color: #6f7b8a;
}
a {
  color: #0090D9;
}
a:hover {
  color: #007fc0;
}
.text-gray, .text-grey {
  color: #B7B9BD;
}
.text-update {
  color: #77559D;
}
blockquote {
  border-left: 10px solid #ededed;
  background: #fafafa;
}
blockquote p {
  color: #646e7c;
}
blockquote cite {
  color: #6f7b8a;
}
code {
  background: #f9f2f4;
  color: #9c1d3d;
}
pre {
  background: #f6f6f6;
  border: 1px solid #dddddd;
}
pre code {
  color: #237794;
}
hr {
  border-bottom: 2px solid #f7f7f7;
}
.label {
  background: #0090D9;
  color: #ffffff;
}
.badge {
  background: #77559D;
  color: #ffffff;
}
.light-border {
  border-color: #f7f7f7 !important;
}
.subtle-text {
  color: #8b95a1 !important;
}
td, thead th {
  border-bottom: 1px solid #f7f7f7;
}
tr td.gpm-details {
  background-color: #fafafa;
}
.tab-bar {
  background: #e6e6e6;
  color: #808080;
}
.tab-bar li.active span, .tab-bar li.active a {
  background: #ffffff;
  color: #6f7b8a;
}
.tab-bar span, .tab-bar a {
  color: #808080;
}
.tab-bar span:hover, .tab-bar a:hover {
  color: #9a9a9a;
  background: white;
}
.button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #0090D9;
}
.button:focus, .button:hover {
  background: #26a1df;
  color: #ffffff;
}
.button.dropdown-toggle {
  border-left: 1px solid #007fc0;
}
.button.dropdown-toggle {
  border-left: 1px solid #007fc0;
}
.button.secondary {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #0073ae;
}
.button.secondary:focus, .button.secondary:hover {
  background: #2688ba;
  color: #ffffff;
}
.button.secondary.dropdown-toggle {
  border-left: 1px solid #006295;
}
.button.secondary.dropdown-toggle {
  border-left: 1px solid #006295;
}
.button.danger {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #F45857;
}
.button.danger:focus, .button.danger:hover {
  background: #cf4b4a;
  color: #ffffff;
}
.button.danger.dropdown-toggle {
  border-left: 1px solid #f6706f;
}
.button-group .dropdown-menu {
  background-color: #0090D9;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}
.button-group .dropdown-menu .divider {
  background-color: #0090D9;
}
.button-group .dropdown-menu li > a {
  color: #ffffff;
}
.button-group .dropdown-menu li > a:focus, .button-group .dropdown-menu li > a:hover {
  color: #ffffff;
}
.button-group .dropdown-menu li > a:focus:hover, .button-group .dropdown-menu li > a:hover:hover {
  background: #26a1df;
}
.button-group .dropdown-menu.language-switcher a.active {
  background-color: #0daeff;
}
#admin-main .admin-block a.gumroad-button {
  background: #F45857 !important;
  color: #ffffff !important;
}
#admin-login-wrapper {
  background: radial-gradient(circle at center, #535a6b 40%, #272a31 100%);
}
#admin-login {
  background: #3D424E;
}
#admin-login.wide form label {
  color: #B7B9BD;
}
#admin-login.wide form input::-webkit-input-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-login.wide form input::-moz-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-login.wide form input:-moz-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-login.wide form input:-ms-input-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-login .wrapper-spacer h3 {
  color: rgba(255, 255, 255, 0.4);
  border-bottom: 3px solid rgba(255, 255, 255, 0.1);
}
#admin-login .instructions, #admin-login p {
  color: rgba(183, 185, 189, 0.8);
}
#admin-login h1 {
  background-color: #323640;
}
#admin-login h1.text-logo {
  color: #FFFFFF;
}
#admin-login h1 svg path:first-child {
  fill: #2b2f37;
}
#admin-login h1 svg path:last-child {
  fill: rgba(255, 255, 255, 0.9) !important;
}
#admin-login h5 {
  color: #B7B9BD;
}
#admin-login form input {
  background: #444957;
  color: #ffffff;
  border: 1px solid #31353e;
}
#admin-login form input::-webkit-input-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-login form input::-moz-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-login form input:-moz-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-login form input:-ms-input-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#admin-sidebar {
  background: #3D424E;
}
#admin-sidebar a {
  color: rgba(255, 255, 255, 0.9);
}
#admin-sidebar a:hover {
  color: #ffffff;
}
#admin-logo {
  background-color: #323640;
}
#admin-logo .grav-logo path {
  fill: rgba(255, 255, 255, 0.9) !important;
}
#admin-logo .grav-logo:hover path {
  fill: #FFFFFF !important;
}
#admin-logo a, #admin-logo i {
  color: #e6e6e6;
}
#admin-logo a:hover, #admin-logo i:hover {
  color: #FFFFFF;
}
#open-handle {
  background-color: #272a32;
  border-left: 1px solid #3d424e;
}
#open-handle i {
  color: rgba(255, 255, 255, 0.6);
}
#open-handle i:hover {
  color: rgba(255, 255, 255, 0.8);
}
#admin-nav-quick-tray {
  background: #393d48;
  border-bottom: 1px solid #363b45;
  color: #ffffff;
}
#admin-nav-quick-tray i:hover {
  color: white;
}
.block-userinfo img {
  border: 4px solid #e6e6e6;
}
#offline-status {
  background-color: #77559D;
  color: #ffffff;
}
#admin-user-details, .admin-user-details {
  border-bottom: 1px solid #484e5c;
}
#admin-user-details:hover img, .admin-user-details:hover img {
  box-shadow: 0px 0px 0 50px #444957;
}
#admin-user-details .admin-user-names h4, #admin-user-details .admin-user-names h5, .admin-user-details .admin-user-names h4, .admin-user-details .admin-user-names h5 {
  color: #ffffff;
}
#admin-menu li .badges .count {
  color: #ffffff;
  background-color: #323640;
}
#admin-menu li .badges .updates {
  color: #ffffff;
  background-color: #06A599;
}
#admin-menu li a:hover {
  background: #434753;
  color: #ffffff;
}
#admin-menu li a:hover .fa {
  color: #ffffff;
}
#admin-menu li.selected a {
  background: #323640;
  color: #ffffff;
  border-left: 9px solid #007ab8;
}
#admin-menu li.selected a .fa {
  color: #ffffff;
}
#admin-main .grav-update {
  background: #77559D;
  color: #ffffff;
}
#admin-main .grav-update a.button, #admin-main .grav-update span.button, #admin-main .grav-update button.button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #5f447e;
}
#admin-main .grav-update a.button:focus, #admin-main .grav-update a.button:hover, #admin-main .grav-update span.button:focus, #admin-main .grav-update span.button:hover, #admin-main .grav-update button.button:focus, #admin-main .grav-update button.button:hover {
  background: #513a6b;
  color: #ffffff;
}
#admin-main .grav-update a.button.dropdown-toggle, #admin-main .grav-update span.button.dropdown-toggle, #admin-main .grav-update button.button.dropdown-toggle {
  border-left: 1px solid #6b4d8f;
}
#admin-main .titlebar {
  background: #ffffff;
  color: #3D424E;
}
#admin-main .titlebar .button-bar .button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #0090D9;
}
#admin-main .titlebar .button-bar .button:focus, #admin-main .titlebar .button-bar .button:hover {
  background: #26a1df;
  color: #ffffff;
}
#admin-main .titlebar .button-bar .button.dropdown-toggle {
  border-left: 1px solid #007fc0;
}
#admin-main .titlebar .button-bar .button.danger {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #F45857;
}
#admin-main .titlebar .button-bar .button.danger:focus, #admin-main .titlebar .button-bar .button.danger:hover {
  background: #cf4b4a;
  color: #ffffff;
}
#admin-main .titlebar .button-bar .button.danger.dropdown-toggle {
  border-left: 1px solid #f6706f;
}
#admin-main .titlebar .button-bar .button.success {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #77559D;
}
#admin-main .titlebar .button-bar .button.success:focus, #admin-main .titlebar .button-bar .button.success:hover {
  background: #8b6fac;
  color: #ffffff;
}
#admin-main .titlebar .button-bar .button.success.dropdown-toggle {
  border-left: 1px solid #6a4c8c;
}
#admin-main .titlebar .button-bar .button.success.dropdown-toggle {
  border-left-color: #654886;
}
#admin-main .titlebar .button-bar .button.success + .dropdown-menu {
  background-color: #77559D;
}
#admin-main .titlebar .button-bar span.button {
  color: rgba(255, 255, 255, 0.45);
  border-radius: 4px;
  background: rgba(0, 144, 217, 0.5);
}
#admin-main .titlebar .button-bar span.button:focus, #admin-main .titlebar .button-bar span.button:hover {
  background: rgba(88, 182, 230, 0.575);
  color: rgba(255, 255, 255, 0.6);
}
#admin-main .titlebar .button-bar span.button.dropdown-toggle {
  border-left: 1px solid rgba(0, 127, 192, 0.5);
}
#admin-main .lines-button {
  background: rgba(0, 0, 0, 0.1);
}
#admin-main .lines-button .lines, #admin-main .lines-button .lines:before, #admin-main .lines-button .lines:after {
  background: #3D424E;
}
#admin-main .admin-block {
  background: #ffffff;
  color: #6f7b8a;
}
#admin-main .admin-block h1 {
  color: #414147;
  border-bottom: 1px solid #f7f7f7;
}
#admin-main .danger.button-bar, #admin-main .success.button-bar {
  background: #f7f7f7;
}
#admin-main .danger .button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #F45857;
}
#admin-main .danger .button:focus, #admin-main .danger .button:hover {
  background: #cf4b4a;
  color: #ffffff;
}
#admin-main .danger .button.dropdown-toggle {
  border-left: 1px solid #f6706f;
}
#admin-main .content-padding div[class*=notifications-container] .alert.note {
  background: #ffffff;
  color: #6f7b8a;
}
#admin-main .content-padding div[class*=notifications-container] .alert.note.alert {
  border-bottom: 2px solid #f2f2f2;
}
#admin-main .content-padding div[class*=notifications-container] .alert.note a:not(.button) {
  color: #0090D9;
}
#admin-main .content-padding div[class*=notifications-container] .alert.note a:not(.button):hover {
  color: #007fc0;
}
#admin-main .content-padding div[class*=notifications-container] .alert.warning {
  background: #F45857;
  color: #ffffff;
}
#admin-main .content-padding div[class*=notifications-container] .alert.warning.alert {
  border-bottom: 2px solid #f2403f;
}
#admin-main .content-padding div[class*=notifications-container] .alert.warning a {
  color: #ffffff;
}
#admin-main .content-padding div[class*=notifications-container] .alert.warning a:hover {
  color: white;
}
#admin-main #notifications .badge.alert.note {
  background: #06A599;
  color: #ffffff;
}
#admin-main #notifications .badge.alert.warning {
  background: #F45857;
  color: #ffffff;
}
.sidebar-mobile-open #admin-main .lines-button .lines, .sidebar-mobile-open #admin-main .lines-button .lines:before, .sidebar-mobile-open #admin-main .lines-button .lines:after {
  background: #FFFFFF;
}
#dependency-missing {
  margin-bottom: 1.75rem;
}
#dependency-missing .alert {
  padding: 1rem 1.5rem;
}
#dependency-missing .alert h1 {
  color: #ffffff;
  border-bottom: 0;
  display: inline;
}
#admin-dashboard .admin-block li {
  border-bottom: 1px solid #f7f7f7;
}
#admin-dashboard .primary-accent {
  background-color: #0078b5;
  background-image: -webkit-linear-gradient(#0078b5, #0090D9);
  background-image: linear-gradient(#0078b5, #0090D9);
}
#admin-dashboard .primary-accent h1 {
  color: #ffffff;
  border-bottom: 0;
}
#admin-dashboard .primary-accent p {
  color: rgba(255, 255, 255, 0.95);
}
#admin-dashboard .primary-accent .stat i {
  color: rgba(255, 255, 255, 0.75);
}
#admin-dashboard .primary-accent .numeric em {
  color: rgba(255, 255, 255, 0.85);
}
#admin-dashboard .primary-accent .chart-loader {
  color: #0073ae;
}
#admin-dashboard .secondary-accent {
  background-color: #058379;
  background-image: -webkit-linear-gradient(#058379, #06A599);
  background-image: linear-gradient(#058379, #06A599);
}
#admin-dashboard .secondary-accent h1 {
  color: #ffffff;
  border-bottom: 0;
}
#admin-dashboard .secondary-accent p {
  color: rgba(255, 255, 255, 0.95);
}
#admin-dashboard .secondary-accent .stat i {
  color: rgba(255, 255, 255, 0.75);
}
#admin-dashboard .secondary-accent .numeric em {
  color: rgba(255, 255, 255, 0.85);
}
#admin-dashboard .secondary-accent .chart-loader {
  color: #05847a;
}
.sidebar-mobile-open #admin-main .lines-button .lines, .sidebar-mobile-open #admin-main .lines-button .lines:before, .sidebar-mobile-open #admin-main .lines-button .lines:after {
  background: #FFFFFF;
}
form h1, form h3 {
  border-bottom: 3px solid #f7f7f7;
}
form h1, form h2, form h3, form h4, form h5 {
  color: #414147;
}
form .note {
  color: #F45857;
}
form .required {
  color: #da4b46;
}
form .overlay {
  background: #fafafa;
}
form .overlay pre {
  background: #fefefe;
}
form .form-border {
  border: 1px solid #e6e6e6;
}
form .form-field-colors {
  background-color: #fcfcfc;
  color: #646e7c;
}
form .form-select-wrapper:after, form .selectize-control.single .selectize-input:after {
  color: #646e7c;
}
form .selectize-control.single .selectize-input, form .selectize-control.single .selectize-input.full, form .selectize-control.single .selectize-input.items, form .selectize-control.single .selectize-input.active, form .selectize-control.multi .selectize-input, form .selectize-control.multi .selectize-input.full, form .selectize-control.multi .selectize-input.items, form .selectize-control.multi .selectize-input.active {
  color: #646e7c;
  border-color: #e6e6e6;
  background-color: #fcfcfc;
}
form .selectize-control.single .selectize-input input, form .selectize-control.multi .selectize-input input {
  color: #646e7c;
}
form .selectize-control.single .selectize-input a.remove, form .selectize-control.multi .selectize-input a.remove {
  color: #F6F6F6 !important;
}
form .form-input-wrapper .form-input-addon {
  border: 1px solid #e6e6e6;
  background: #ffffff;
}
form .form-input-wrapper .form-input-addon.copy-to-clipboard {
  background: #0090D9;
  color: #ffffff;
}
form .form-input-wrapper .form-input-addon.copy-to-clipboard:hover {
  background: #007fc0;
}
form .form-input-wrapper input[name="data[folder]"].highlight {
  background-color: #ffffd7;
}
form .selectize-control.single.plugin-remove_button .selectize-input .item, form .selectize-control.single.plugin-remove_button .selectize-input .remove-single, form .selectize-control.multi .selectize-input .item, form .selectize-control.multi .selectize-input .remove-single {
  color: #ffffff !important;
  background: #6f7b8a;
}
form .selectize-control.single.plugin-remove_button .selectize-input .item.active, form .selectize-control.single.plugin-remove_button .selectize-input .remove-single.active, form .selectize-control.multi .selectize-input .item.active, form .selectize-control.multi .selectize-input .remove-single.active {
  background: #06A599;
  color: #ffffff;
}
form select {
  border: 1px solid #e6e6e6;
  background: #fcfcfc;
  color: #646e7c;
}
form input[type=text], form input[type=password], form input[type=email], form input[type=date], form input[type=tel], form input[type=time], form input[type=week], form input[type=month], form input[type=number], form input[type=color], form input[type=url] {
  border: 1px solid #e6e6e6;
  background: #fcfcfc;
  color: #646e7c;
}
form input[readonly=readonly] {
  background: #ffffff;
}
form textarea {
  color: #646e7c;
  border: 1px solid #e6e6e6;
  background: #fcfcfc;
}
form .form-frontmatter-wrapper {
  border: 1px solid #e6e6e6;
}
form .button-bar {
  background: #f7f7f7;
}
form .checkboxes label:before {
  background: #fcfcfc;
  border: 1px solid #e6e6e6;
}
form input::-webkit-input-placeholder, form textarea::-webkit-input-placeholder, form select::-webkit-input-placeholder {
  color: rgba(100, 110, 124, 0.4);
}
form input::-moz-placeholder, form textarea::-moz-placeholder, form select::-moz-placeholder {
  color: rgba(100, 110, 124, 0.4);
}
form input:-moz-placeholder, form textarea:-moz-placeholder, form select:-moz-placeholder {
  color: rgba(100, 110, 124, 0.4);
}
form input:-ms-input-placeholder, form textarea:-ms-input-placeholder, form select:-ms-input-placeholder {
  color: rgba(100, 110, 124, 0.4);
}
form .g-colorpicker {
  width: 230px;
}
form .g-colorpicker .g-colorpicker-preview-wrap {
  border-color: #e6e6e6;
}
form .form-input-file {
  border: 2px dashed #e6e6e6;
}
form .form-input-file p {
  color: #646e7c;
}
form .form-input-file ul {
  color: #ffffff;
  background: #0090D9;
}
form .file-thumbnail-remove {
  background: #0090D9;
}
form .file-thumbnail-remove .fa {
  color: #ffffff;
}
form .file-thumbnail-remove:hover {
  background: #ffffff;
}
form .file-thumbnail-remove:hover .fa {
  color: #0090D9;
}
form input[type=range]::-webkit-slider-runnable-track {
  background: #fafafa;
  border: 1px solid #e6e6e6;
}
form input[type=range]::-webkit-slider-thumb {
  border: 1px solid #e6e6e6;
  background: #0090D9;
}
form input[type=range]:focus::-webkit-slider-runnable-track {
  background: #fafafa;
}
form input[type=range]::-moz-range-track {
  background: #fafafa;
  border: 1px solid #e6e6e6;
}
form input[type=range]::-moz-range-progress {
  background: #fafafa;
}
form input[type=range]::-moz-range-thumb {
  border: 1px solid #e6e6e6;
  background: #0090D9;
}
form input[type=range]::-ms-fill-lower, form input[type=range]::-ms-fill-upper {
  background: #fafafa;
  border: 1px solid #e6e6e6;
}
form input[type=range]::-ms-thumb {
  border: 1px solid #e6e6e6;
  background: #0090D9;
}
form input[type=range]:focus::-ms-fill-lower, form input[type=range]:focus::-ms-fill-upper {
  background: #fafafa;
}
form .form-display-wrapper table {
  border: 1px solid #e6e6e6;
}
form .form-display-wrapper table th {
  background-color: whitesmoke;
  border-bottom: 3px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
}
form .form-display-wrapper table td {
  border-right: 1px solid #e6e6e6;
}
form .form-display-wrapper table tr {
  border-bottom: 1px solid #e6e6e6;
}
.grav-editor-resizer {
  background-color: #e6e6e6;
}
.form-order-wrapper ul.orderable li {
  border: 1px solid #e6e6e6;
  background: white;
  color: #6f7b8a;
}
.form-order-wrapper ul.orderable li.drag-handle {
  background: #fcfcfc;
  color: #646e7c;
}
.form-order-wrapper ul.orderable li[data-active-id] {
  border-color: #0090D9;
}
.form-order-wrapper ul.orderable.disabled li {
  opacity: 0.7;
}
.form-list-wrapper ul[data-collection-holder] > li {
  border: 1px solid #e6e6e6;
  background: white;
  color: #8b95a1;
}
.form-list-wrapper ul[data-collection-holder] > li .item-actions {
  color: #58626e;
}
.form-list-wrapper .collection-sort {
  background: #fafafa;
  border-right: 1px solid #e6e6e6;
}
.form-fieldset {
  background-color: #fcfcfc;
  border: 1px solid #e6e6e6;
}
.form-fieldset--label {
  background-color: whitesmoke;
}
.form-fieldset--label:hover, .form-fieldset input:checked + .form-fieldset--label {
  background-color: #f0f0f0;
}
#admin-main #admin-topbar {
  background: #e6e6e6;
}
#admin-main .form-tabs {
  background: #e6e6e6;
}
#admin-main .form-tabs.side-tabs > .tabs-nav {
  background: #ffffff;
}
#admin-main .form-tabs.side-tabs > .tabs-nav a {
  color: #0090D9;
  cursor: pointer;
}
#admin-main .form-tabs.side-tabs > .tabs-nav a:hover {
  background: #F6F6F6;
}
#admin-main .form-tabs.side-tabs > .tabs-nav a.active {
  background: #f3f3f3;
  color: #808080;
}
#admin-main .form-tabs.side-tabs > .tabs-content {
  box-shadow: -20px 0 50px -40px rgba(0, 0, 0, 0.3);
}
#admin-main .form-tabs .tabs-nav a {
  color: #808080;
}
#admin-main .form-tabs .tabs-nav a:hover {
  color: #737373;
  background: #d9d9d9;
}
#admin-main .form-tabs .tabs-nav a.active {
  background: #ffffff;
  color: #6f7b8a;
}
#admin-main .form-tabs.subtle {
  background: #ffffff;
}
#admin-main .form-tabs.subtle .tabs-nav {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), #f7f7f7);
}
#admin-main .form-tabs.subtle a {
  color: #646f7c;
  border: 0 solid transparent;
  border-bottom: 1px solid #e6e6e6;
}
#admin-main .form-tabs.subtle a:hover {
  color: #59626e;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0), #ededed);
}
#admin-main .form-tabs.subtle a.active {
  border-top: 1px solid #e6e6e6;
  border-left: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
  border-bottom: 0;
}
#admin-main .form-tabs.subtle a.active:hover {
  background: #ffffff;
}
#admin-main .form-tabs.subtle a.active:first-child {
  border-left: 0;
}
#admin-main .form-tabs.subtle a.active:last-child {
  border-right: 0;
}
#admin-main .form-tabs .tabs-content {
  background: #ffffff;
}
.switch-grav {
  background-color: #fcfcfc;
  border: 1px solid #e6e6e6;
}
.switch-grav label {
  color: #6f7b8a;
  transition: background-color 0.5s ease;
}
.switch-grav.switch-toggle input.highlight:checked + label {
  background: #0090D9;
  color: #ffffff;
}
.switch-grav.switch-toggle input:checked + label {
  color: #ffffff;
  background: #6f7b8a;
}
.toast {
  background-color: #030303;
}
.toast-success {
  background-color: #77559D;
}
.toast-success .button {
  background: #8462aa;
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #9172b3;
}
.toast-success .button:focus, .toast-success .button:hover {
  background: #7b6198;
  color: #ffffff;
}
.toast-success .button.dropdown-toggle {
  border-left: 1px solid #9e83bc;
}
.toast-error {
  background-color: #F45857;
}
.toast-error .button {
  background-color: #f12827;
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #d70f0e;
}
.toast-error .button:focus, .toast-error .button:hover {
  background: #dd3332;
  color: #ffffff;
}
.toast-error .button.dropdown-toggle {
  border-left: 1px solid #bf0e0d;
}
.toast-info {
  background-color: #06A599;
}
.toast-info .button {
  background-color: #04746b;
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #02433e;
}
.toast-info .button:focus, .toast-info .button:hover {
  background: #285f5b;
  color: #ffffff;
}
.toast-info .button.dropdown-toggle {
  border-left: 1px solid #022a27;
}
.info {
  background: #77559D;
  color: #ffffff;
}
.info a {
  color: #f2f2f2;
}
.info a:hover {
  color: #ffffff;
}
.info a.button, .info span.button, .info button.button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #5f447e;
}
.info a.button:focus, .info a.button:hover, .info span.button:focus, .info span.button:hover, .info button.button:focus, .info button.button:hover {
  background: #513a6b;
  color: #ffffff;
}
.info a.button.dropdown-toggle, .info span.button.dropdown-toggle, .info button.button.dropdown-toggle {
  border-left: 1px solid #6b4d8f;
}
.info-reverse {
  color: #77559D;
}
.notice, .note {
  background: #06A599;
  color: #ffffff;
}
.notice a, .note a {
  color: #f2f2f2;
}
.notice a:hover, .note a:hover {
  color: #ffffff;
}
.notice a.button, .notice span.button, .notice button.button, .note a.button, .note span.button, .note button.button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #05847a;
}
.notice a.button:focus, .notice a.button:hover, .notice span.button:focus, .notice span.button:hover, .notice button.button:focus, .notice button.button:hover, .note a.button:focus, .note a.button:hover, .note span.button:focus, .note span.button:hover, .note button.button:focus, .note button.button:hover {
  background: #047068;
  color: #ffffff;
}
.notice a.button.dropdown-toggle, .notice span.button.dropdown-toggle, .notice button.button.dropdown-toggle, .note a.button.dropdown-toggle, .note span.button.dropdown-toggle, .note button.button.dropdown-toggle {
  border-left: 1px solid #069d91;
}
.notice-reverse, .note-reverse {
  color: #06A599;
}
.error, .warning {
  background: #F45857;
  color: #ffffff;
}
.error a, .warning a {
  color: #f2f2f2;
}
.error a:hover, .warning a:hover {
  color: #ffffff;
}
.error a.button, .error span.button, .error button.button, .warning a.button, .warning span.button, .warning button.button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #c34646;
}
.error a.button:focus, .error a.button:hover, .error span.button:focus, .error span.button:hover, .error button.button:focus, .error button.button:hover, .warning a.button:focus, .warning a.button:hover, .warning span.button:focus, .warning span.button:hover, .warning button.button:focus, .warning button.button:hover {
  background: #a63c3c;
  color: #ffffff;
}
.error a.button.dropdown-toggle, .error span.button.dropdown-toggle, .error button.button.dropdown-toggle, .warning a.button.dropdown-toggle, .warning span.button.dropdown-toggle, .warning button.button.dropdown-toggle {
  border-left: 1px solid #c95959;
}
.error-reverse, .warning-reverse {
  color: #F45857;
}
.primary-accent {
  background: #007fc0;
  color: #ffffff;
}
.primary-accent .button-bar {
  background: #0078b5;
}
.primary-accent .button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #0090D9;
}
.primary-accent .button:focus, .primary-accent .button:hover {
  background: #26a1df;
  color: #ffffff;
}
.primary-accent .button.dropdown-toggle {
  border-left: 1px solid #007fc0;
}
.secondary-accent {
  background: #058c82;
  color: #ffffff;
}
.secondary-accent .button-bar {
  background: #058379;
}
.secondary-accent .button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #06A599;
}
.secondary-accent .button:focus, .secondary-accent .button:hover {
  background: #2bb3a8;
  color: #ffffff;
}
.secondary-accent .button.dropdown-toggle {
  border-left: 1px solid #058c82;
}
.secondary-accent .button-group .dropdown-menu {
  background: #06A599;
}
.secondary-accent .button-group .dropdown-menu li a:hover {
  background: #2bb3a8;
}
.card-item {
  border: 1px solid #e6e6e6;
  background: white;
}
.card-item .gpm-actions {
  background: #fafafa;
}
.card-item.active-theme {
  border: 1px solid #06A599;
}
.card-item.active-theme .gpm-actions {
  background: #06A599;
  color: #ffffff;
}
.user-details h2 {
  color: #414147;
}
.user-details h2 span {
  color: #6f7b8a;
}
.user-details .gravatar {
  color: #a7afb8;
}
.user-details .gravatar a {
  color: #6f7b8a !important;
}
#admin-main .grav-editor-toolbar .dropdown-menu {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.075);
  border: 1px solid #e6e6e6;
  background: white;
}
#admin-main .grav-editor-toolbar {
  border: 1px solid #e6e6e6;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  background: white;
}
#admin-main .grav-editor-toolbar ul a {
  color: #6f7b8a;
}
#admin-main .grav-editor-toolbar ul a:hover, #admin-main .grav-editor-toolbar ul a:focus {
  background: #f7f7f7;
  color: #646e7c;
  border-top: 1px solid #e6e6e6;
}
#admin-main .grav-editor-toolbar ul .editor-active a {
  cursor: auto;
  background: white;
  border-top: 1px solid #e6e6e6;
  border-left: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
}
#admin-main .grav-editor-toolbar ul .editor-active a:hover {
  background: #f7f7f7;
  color: #646e7c;
  border-top: 0;
  border-bottom: 1px solid #e6e6e6;
}
#admin-main .grav-editor-toolbar ul .editor-active:last-child a, #admin-main .grav-editor-toolbar ul .editor-active:last-child a:hover {
  border-right: 1px solid transparent;
}
#admin-main .grav-editor-toolbar ul .button-disabled a {
  color: rgba(111, 123, 138, 0.5);
}
#admin-main .grav-editor-toolbar ul .button-disabled a:hover, #admin-main .grav-editor-toolbar ul .button-disabled a:focus {
  background: white;
  color: #6f7b8a;
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
}
#admin-main .grav-editor-hide-toolbar + div {
  border: 1px solid #e6e6e6;
}
.grav-editor-content, .grav-editor-preview {
  border: 1px solid #e6e6e6;
  border-top: 0;
}
.grav-mdeditor-content {
  border: 1px solid #e6e6e6;
}
[data-mode=split] .grav-mdeditor-code {
  border-right: 1px solid #e6e6e6;
}
.primary-accent .ct-chart .ct-series.ct-series-a .ct-bar {
  stroke: rgba(255, 255, 255, 0.85) !important;
}
.primary-accent .ct-chart .ct-series.ct-series-a .ct-slice-donut {
  stroke: #ffffff !important;
}
.primary-accent .ct-chart .ct-series.ct-series-b .ct-slice-donut {
  stroke: rgba(255, 255, 255, 0.2) !important;
}
.primary-accent .ct-chart .ct-label {
  fill: rgba(255, 255, 255, 0.5);
  color: rgba(255, 255, 255, 0.5);
}
.primary-accent .ct-chart .ct-grid {
  stroke: rgba(255, 255, 255, 0.2);
}
.secondary-accent .ct-chart .ct-series.ct-series-a .ct-bar {
  stroke: rgba(255, 255, 255, 0.85) !important;
}
.secondary-accent .ct-chart .ct-series.ct-series-a .ct-slice-donut {
  stroke: #ffffff !important;
}
.secondary-accent .ct-chart .ct-series.ct-series-b .ct-slice-donut {
  stroke: rgba(255, 255, 255, 0.2) !important;
}
.secondary-accent .ct-chart .ct-label {
  fill: rgba(255, 255, 255, 0.5);
  color: rgba(255, 255, 255, 0.5);
}
.secondary-accent .ct-chart .ct-grid {
  stroke: rgba(255, 255, 255, 0.2);
}
.dropzone {
  background: #fcfcfc;
  border: 1px #e6e6e6 solid;
}
.dropzone.dz-drag-hover {
  border-color: #ccc;
  background: rgba(0, 0, 0, 0.04);
}
.dropzone .dz-preview {
  border: 1px solid #f2f2f2;
}
.dropzone .dz-preview .dz-success-mark, .dropzone .dz-preview .dz-error-mark {
  color: #ffffff;
}
.dropzone .dz-preview .dz-success-mark {
  background-color: #0daeff;
}
.dropzone .dz-preview .dz-error-mark {
  background-color: #F45857;
}
.dropzone .dz-preview .dz-progress {
  background: #d7d7d7;
}
.dropzone .dz-preview .dz-progress .dz-upload {
  background-color: #0daeff;
}
.dropzone .dz-preview .dz-error-message {
  background: white;
  color: #F45857;
}
.dropzone .dz-preview:hover .dz-details {
  box-shadow: 0px 0px 20px -5px rgba(0, 0, 0, 0.4);
}
.dropzone .dz-preview .dz-remove, .dropzone .dz-preview .dz-unset, .dropzone .dz-preview .dz-view, .dropzone .dz-preview .dz-insert, .dropzone .dz-preview .dz-metadata {
  background: #ededed;
}
.dropzone .dz-preview .dz-remove:hover:after, .dropzone .dz-preview .dz-unset:hover:after, .dropzone .dz-preview .dz-view:hover:after, .dropzone .dz-preview .dz-insert:hover:after, .dropzone .dz-preview .dz-metadata:hover:after {
  color: #0090D9;
}
.dropzone .dz-preview .dz-remove:after, .dropzone .dz-preview .dz-unset:after, .dropzone .dz-preview .dz-view:after, .dropzone .dz-preview .dz-insert:after, .dropzone .dz-preview .dz-metadata:after {
  color: #6f7b8a;
}
.dropzone .dz-preview .dz-remove:hover:after {
  color: #F45857;
}
.dropzone.dz-clickable .dz-message, .dropzone.dz-clickable .dz-message span {
  color: #a7afb8;
}
.dropzone.dz-clickable .dz-message .dz-button, .dropzone.dz-clickable .dz-message span .dz-button {
  background-color: #f2f2f2;
}
.gpm > table > tbody > tr {
  border-bottom: 1px solid #f7f7f7;
}
.gpm > table > tbody > tr:hover {
  background: #f2f2f2;
}
.gpm .gpm-name {
  color: #99a2ad;
}
.gpm .gpm-version {
  color: #8b95a1;
}
.gpm .gpm-ribbon, .gpm .badge.update {
  background: #77559D;
}
.gpm .gpm-ribbon a, .gpm .gpm-ribbon, .gpm .badge.update a, .gpm .badge.update {
  color: #ffffff !important;
}
.gpm .gpm-ribbon a:hover, .gpm .gpm-ribbon:hover, .gpm .badge.update a:hover, .gpm .badge.update:hover {
  background: #654885;
}
.gpm .gpm-update .gpm-name {
  color: #0090D9;
}
.gpm .gpm-item-info {
  border-bottom: 3px solid #f7f7f7;
}
.gpm .gpm-item-info .gpm-item-icon {
  color: #fafafa;
}
.gpm .gpm-actions .disabled {
  color: #8b95a1;
}
.gpm .gpm-testing {
  background-color: #06A599;
  color: #ffffff;
}
.pages-list {
  border-top: 1px solid #f7f7f7;
}
.pages-list .row {
  border-bottom: 1px solid #f7f7f7;
}
.pages-list .row:hover {
  background: white;
}
.pages-list .row p.page-route {
  color: #6f7b8a;
}
.pages-list .row p.page-route .spacer {
  color: #6f7b8a;
}
.pages-list .row .badge.lang {
  background-color: #e6e6e6;
  color: #808080;
}
.pages-list .row .badge.lang.info {
  background-color: #77559D;
  color: #ffffff;
}
.pages-list .page-home {
  color: #8b95a1;
}
.pages-list .page-info {
  color: #8b95a1;
}
#admin-topbar #admin-lang-toggle {
  z-index: 3;
}
#admin-topbar #admin-lang-toggle button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: #77559D;
}
#admin-topbar #admin-lang-toggle button:focus, #admin-topbar #admin-lang-toggle button:hover {
  background: #8b6fac;
  color: #ffffff;
}
#admin-topbar #admin-lang-toggle button.dropdown-toggle {
  border-left: 1px solid #6a4c8c;
}
#admin-topbar #admin-lang-toggle .dropdown-menu {
  background: #77559D;
}
#admin-topbar .switch-grav {
  background-color: white;
}
#admin-topbar .switch-toggle input:checked + label {
  color: #ffffff;
}
#admin-topbar .switch-toggle input + label {
  color: #808080;
  background: #d9d9d9;
}
#admin-topbar .switch-toggle input.highlight:checked + label {
  background: #0090D9;
}
body .selectize-dropdown .optgroup-header {
  color: #000000;
  border-bottom: 1px solid #eee;
  background-color: #fafafa;
}
body .bootstrap-datetimepicker-widget.dropdown-menu.bottom {
  background-color: #fcfcfc;
  color: #646e7c;
  border-color: #e6e6e6;
}
body .bootstrap-datetimepicker-widget.dropdown-menu.bottom:before {
  border-bottom-color: #e6e6e6;
}
body .bootstrap-datetimepicker-widget.dropdown-menu.bottom:after {
  border-bottom-color: #fcfcfc;
}
body .bootstrap-datetimepicker-widget table td span:hover, body .bootstrap-datetimepicker-widget table th:hover, body .bootstrap-datetimepicker-widget table td.day:hover {
  background: whitesmoke !important;
}
body .bootstrap-datetimepicker-widget table td.active, body .bootstrap-datetimepicker-widget table td.active:hover {
  background-color: #77559D !important;
  color: #ffffff !important;
}
.scheduler-content pre {
  background: #fafafa;
  border-color: #e6e6e6;
}
.scheduler-content pre code {
  color: #414147;
}
#phpinfo th {
  background: #fcfcfc;
}
#phpinfo td {
  word-wrap: break-word;
}
#phpinfo td:first-child {
  color: #414147;
}
.remodal {
  background: #ffffff;
  color: #6f7b8a;
}
.remodal-close {
  color: #6f7b8a;
}
.remodal-close:hover, .remodal-close:focus {
  color: #424951;
}
* {
  /* Works on Chrome/Edge/Safari */
}
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(111, 123, 138, 0.4) #ffffff;
}
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
*::-webkit-scrollbar-track {
  background: #ffffff;
}
*::-webkit-scrollbar-thumb {
  background-color: rgba(111, 123, 138, 0.4);
}
.simplebar-scrollbar:before {
  background-color: #6f7b8a;
}
#admin-sidebar .simplebar-scrollbar:before {
  background-color: #B7B9BD;
}
#page-filtering .page-shortcuts .button {
  color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  background: rgba(100, 110, 124, 0.5);
}
#page-filtering .page-shortcuts .button:focus, #page-filtering .page-shortcuts .button:hover {
  background: rgba(153, 160, 169, 0.575);
  color: #ffffff;
}
#page-filtering .page-shortcuts .button.dropdown-toggle {
  border-left: 1px solid rgba(88, 98, 110, 0.5);
}
.reports-content .report-output table thead tr th {
  background-color: #fafafa;
  color: #414147;
}
.preview-wrapper iframe {
  border: 1px solid #eee;
}
#backups-stats .backups-usage-wrapper > div {
  background: linear-gradient(-90deg, #006ea6 0, #005d8d 100%);
}
#backups-stats .backups-usage-wrapper > div.full {
  background: linear-gradient(-90deg, #f2403f 0, #8f0a09 100%);
}
.logs-output table.noflex td.level .badge.error, .logs-output table.noflex td.level .badge.critical, .logs-output table.noflex td.level .badge.alert {
  background-color: #DC3023;
  color: #fff;
}
.logs-output table.noflex td.level .badge.notice, .logs-output table.noflex td.level .badge.warning {
  background-color: #E08A1E;
  color: #fff;
}
.logs-output table.noflex td.level .badge.debug {
  background-color: #26A65B;
  color: #fff;
}
.logs-output table.noflex td.level .badge.info {
  background-color: #22A7F0;
  color: #fff;
}
.logs-output table.noflex td.level .badge.emergency {
  background-color: #8E44AD;
  color: #fff;
}
.permission-container {
  overflow: hidden;
}
[data-grav-field="parents"] .form-input-wrapper:hover {
  color: #0090D9;
}
.grav-loader, .grav-loader:after {
  border-radius: 50%;
  width: 10em;
  height: 10em;
}
.grav-loader {
  margin: 60px auto;
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 1.1em solid rgba(111, 123, 138, 0.2);
  border-right: 1.1em solid rgba(111, 123, 138, 0.2);
  border-bottom: 1.1em solid rgba(111, 123, 138, 0.2);
  border-left: 1.1em solid #6f7b8a;
  transform: translateZ(0);
  animation: gravloader 1.1s infinite linear;
}
@-webkit-keyframes gravloader {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes gravloader {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
input[type=range].rangefield {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  margin: 10px 0;
  width: 230px;
  background: none;
}
input[type=range].rangefield:focus {
  outline: none;
}
input[type=range].rangefield::-webkit-slider-runnable-track {
  width: 230px;
  height: 20px;
  cursor: pointer;
  animate: 0.2s;
  border-radius: 25px;
}
input[type=range].rangefield::-webkit-slider-thumb {
  height: 24px;
  width: 35px;
  border-radius: 6px;
  cursor: pointer;
  -webkit-appearance: none;
  margin-top: -3px;
}
input[type=range].rangefield::-moz-focus-outer {
  border: 0;
}
input[type=range].rangefield::-moz-range-track {
  width: 230px;
  height: 20px;
  cursor: pointer;
  animate: 0.2s;
  border-radius: 25px;
}
input[type=range].rangefield::-moz-range-progress {
  height: 20px;
  border-radius: 25px;
}
input[type=range].rangefield::-moz-range-thumb {
  height: 24px;
  width: 35px;
  border-radius: 6px;
  cursor: pointer;
}
input[type=range].rangefield::-ms-track {
  width: 230px;
  height: 20px;
  cursor: pointer;
  animate: 0.2s;
  background: transparent;
  border-color: transparent;
  color: transparent;
}
input[type=range].rangefield::-ms-fill-lower, input[type=range].rangefield input[type=range]::-ms-fill-upper {
  border-radius: 50px;
}
input[type=range].rangefield::-ms-thumb {
  height: 24px;
  width: 35px;
  border-radius: 6px;
  cursor: pointer;
}
input[type=range].rangefield ~ input[type=number].rangefield {
  background: none;
  display: inline-block;
  width: 60px;
  text-align: right;
  border: 0;
  line-height: 16px;
  vertical-align: middle;
  padding: 0 0 0 5px;
}
span.range-append {
  display: inline-block;
  line-height: 20px;
  vertical-align: middle;
  margin-left: -3px;
}
#pages-filters {
  background: #3D424E;
  color: rgba(183, 185, 189, 0.9);
}
#pages-filters .button-border {
  color: #B7B9BD;
  border-radius: 4px;
  border: 1px solid #535a6b;
  color: #B7B9BD !important;
}
#pages-filters .button-border:hover {
  border-color: #6d7381;
  color: #B7B9BD;
}
#pages-filters .button-border.dropdown-toggle {
  border-left: 1px solid #484e5c;
}
#pages-filters .filters-bar input {
  color: #ffffff;
  border-color: #585f70;
  background-color: #4a515f;
}
#pages-filters .filters-bar input::-webkit-input-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#pages-filters .filters-bar input::-moz-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#pages-filters .filters-bar input:-moz-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#pages-filters .filters-bar input:-ms-input-placeholder {
  color: rgba(183, 185, 189, 0.5);
}
#pages-filters .filters-advanced fieldset {
  border: 1px solid #484e5c;
}
#pages-filters .filters-advanced fieldset legend {
  background: #3D424E;
  color: #B7B9BD;
  font-weight: bold;
}
#pages-filters form .checkboxes label:before {
  background-color: #4f5565;
  border-color: #585f70;
  color: #ffffff;
}
#pages-columns {
  background: #ffffff;
  border-top: 1px solid rgba(230, 230, 230, 0.5);
}
#pages-columns.fjs-container .fjs-col {
  border-right: 1px solid rgba(230, 230, 230, 0.5);
  border-bottom: 1px solid rgba(230, 230, 230, 0.5);
}
#pages-columns .fjs-item:hover .fjs-info > b {
  color: #0090D9;
}
#pages-columns .fjs-item:hover .fjs-action-toggle, #pages-columns .fjs-item:hover .fjs-children {
  color: #0090D9 !important;
}
#pages-columns .fjs-item .fjs-icon {
  background-color: #999;
  box-shadow: 0px 0px 0px 1px white;
}
#pages-columns .fjs-item .fjs-icon.home {
  background-color: #2980B9;
}
#pages-columns .fjs-item .fjs-icon.visible {
  background-color: #3498DB;
}
#pages-columns .fjs-item .fjs-icon.lock {
  background-color: #E67E22;
}
#pages-columns .fjs-item .fjs-icon.modular {
  background-color: #9B59B6;
}
#pages-columns .fjs-item .fjs-icon:after {
  border: 1px solid #ffffff;
}
#pages-columns .fjs-item .fjs-icon.badge-published:after {
  background-color: #27AE60;
}
#pages-columns .fjs-item .fjs-icon.badge-unpublished:after {
  background-color: #E74C3C;
}
#pages-columns .fjs-item .fjs-icon .badge-lang {
  background-color: #e1e1e1;
  color: #737373;
  border: 1px solid #ffffff;
}
#pages-columns .fjs-item .fjs-icon .badge-lang.translated {
  background-color: #77559D;
  color: #ffffff;
}
#pages-columns .fjs-item .fjs-icon .badge-lang.not-available {
  background-color: #323640;
  color: #ffffff;
}
#pages-columns .fjs-item .fjs-info > b {
  color: #6f7b8a;
}
#pages-columns .fjs-item .fjs-info > em {
  color: #8b95a1;
}
#pages-columns .fjs-item .fjs-actions .child-count {
  background-color: #e6e6e6;
  color: #6f7b8a;
}
#pages-columns .fjs-item .fjs-action-toggle {
  color: #0090D9;
}
#pages-columns .fjs-item .fjs-action-toggle, #pages-columns .fjs-item .fjs-children {
  color: rgba(111, 123, 138, 0.5);
  border-radius: 4px;
  border: 1px solid #e6e6e6;
}
#pages-columns .fjs-item .fjs-action-toggle:hover, #pages-columns .fjs-item .fjs-children:hover {
  border-color: #c3c3c3;
  color: rgba(111, 123, 138, 0.5);
}
#pages-columns .fjs-item .fjs-action-toggle.dropdown-toggle, #pages-columns .fjs-item .fjs-children.dropdown-toggle {
  border-left: 1px solid #f2f2f2;
}
#pages-columns .fjs-item .fjs-action-toggle:hover, #pages-columns .fjs-item .fjs-children:hover {
  border-color: #d9d9d9;
}
#pages-columns .fjs-item > a {
  position: relative;
}
#pages-columns .fjs-item.fjs-active > .fjs-item-wrapper {
  background-color: whitesmoke;
}
#pages-columns .fjs-item.fjs-active > .fjs-item-wrapper a {
  background: none;
}
#pages-columns .fjs-item.filters-hit > .fjs-item-wrapper {
  background-color: #ffffeb;
}
#pages-columns .fjs-item.filters-hit.fjs-active > .fjs-item-wrapper {
  background-color: #ffffd6;
}
#pages-columns .tags .tag {
  margin: 0 0.15rem;
}
#pages-columns .tags .tag-published {
  background-color: #219e0d;
}
#pages-columns .tags .tag-visible {
  background-color: #018fd9;
}
#pages-columns .tags .tag-non-routable {
  background-color: #ff392a;
}
#pages-columns .langs .badge {
  align-items: center;
  background-color: #e1e1e1;
  color: #737373;
}
#pages-columns .langs .lang:hover .badge {
  background-color: #0090D9;
  color: #F6F6F6;
}
#pages-columns .langs .lang-translated .fa {
  color: #219e0d;
}
#pages-columns .langs .lang-non-translated .fa {
  color: #ff392a;
}
#pages-columns .dropdown-menu {
  background-color: #f2f2f2 !important;
}
#pages-columns .dropdown-menu .divider {
  border-top: 1px solid #e6e6e6;
}
#pages-columns .dropdown-menu td, #pages-columns .dropdown-menu thead th {
  border-bottom: 1px solid #e6e6e6;
}
#pages-columns .dropdown-menu .dropdown-item {
  color: #6f7b8a;
  background-color: inherit;
}
#pages-columns .dropdown-menu .dropdown-item:hover {
  color: #0090D9;
  background-color: #ebebeb !important;
}
#pages-columns .dropdown-menu .dropdown-item.danger .fa {
  color: #F45857;
}
#pages-columns .dropdown-menu .dropdown-item.danger:hover {
  background-color: #F45857 !important;
}
#pages-columns .dropdown-menu .dropdown-item.danger:hover .fa {
  color: #ffffff;
}
#pages-columns .dropdown-menu .action-bar {
  background: #ececec;
}
#pages-columns .dropdown-menu .action-bar a {
  color: #6f7b8a;
}
#pages-columns .dropdown-menu .action-bar a:hover {
  background: #F6F6F6 !important;
  color: #0090D9 !important;
}
#pages-columns .dropdown-menu .fa {
  color: #999;
}
#pages-columns .dropdown-menu a:not(.lang):hover .fa {
  color: #0090D9;
}
#pages-columns .dropdown-menu .infos tr td {
  color: #6f7b8a;
}
#pages-columns .dropdown-menu .infos tr td:first-child {
  color: #8b95a1;
}
#pages-content-wrapper .fjs-path-bar {
  background-color: #ffffff;
}
.breadcrumb-node:hover span {
  text-decoration: none;
  color: #0090D9;
}
.breadcrumb-node > i:not(.fa) {
  background-color: #999;
  box-shadow: 0px 0px 0px 1px #fff;
}
.breadcrumb-node > i:not(.fa).home {
  background-color: #2980B9;
}
.breadcrumb-node > i:not(.fa).visible {
  background-color: #3498DB;
}
.breadcrumb-node > i:not(.fa).lock {
  background-color: #E67E22;
}
.breadcrumb-node > i:not(.fa).modular {
  background-color: #9B59B6;
}
[data-remodal-id].parents-container form > h1 {
  border-bottom: 1px solid #e6e6e6;
}
[data-remodal-id].parents-container .fjs-col {
  border-right: 1px solid #e6e6e6;
}
[data-remodal-id].parents-container .fjs-item a {
  color: #0090D9;
}
.parents-content.fjs-container .fjs-active a {
  background-color: #0090D9;
  color: #ffffff;
}
.permissions-container .switch-toggle input.label0:checked + label {
  color: #ffffff;
  background: #C0392B !important;
}
.permissions-container .switch-toggle input.label1:checked + label {
  color: #ffffff;
  background-color: #3D9970 !important;
}
.permissions-container .badge.badge-denied {
  background: #C0392B !important;
}
.permissions-container .badge.badge-access {
  background-color: #3D9970 !important;
}
.permissions-container .badge.badge-super {
  background-color: #8E44AD !important;
}
.permissions-container fieldset {
  border: 1px solid #e6e6e6;
}
.permissions-container fieldset legend {
  background-color: #ffffff;
  color: #414147;
}
.permissions-container fieldset fieldset legend {
  border-bottom: 1px solid #e6e6e6;
}
.permissions-container .crudp-container .checkboxes.toggleable label {
  border-color: #e6e6e6;
  background-color: #fcfcfc;
}
.permissions-container .crudp-container .checkboxes input[type=checkbox] + label:before {
  color: rgba(111, 123, 138, 0.3);
}
.permissions-container .crudp-container .checkboxes[data-_check-status="1"] input[type=checkbox] + label {
  background-color: #3D9970;
  color: #ffffff;
}
.permissions-container .crudp-container .checkboxes[data-_check-status="1"] input[type=checkbox] + label:before {
  color: #ffffff;
}
.permissions-container .crudp-container .checkboxes[data-_check-status="2"] input[type=checkbox] + label {
  background-color: #C0392B;
  color: #ffffff;
}
.permissions-container .crudp-container .checkboxes[data-_check-status="2"] input[type=checkbox] + label:before {
  color: #ffffff;
}
.permission-container .permission-name span:not(.badge) {
  background-color: #ffffff;
}
.permission-container .permission-name span.badge {
  border: 2px solid #ffffff;
}
.icon-set {
  background: white;
}
.jqCron-cross, .jqCron-selector-title {
  border: 1px solid #e6e6e6;
}
.jqCron-container.disable .jqCron-cross:hover, .jqCron-container.disable .jqCron-selector-title:hover, .jqCron-cross, .jqCron-selector-title {
  background: #fcfcfc;
  border-color: #e6e6e6;
}
.jqCron-cross:hover, .jqCron-selector-title:hover {
  background-color: #e3e3e3;
}
.jqCron-selector-list {
  background-color: #e3e3e3;
  border: 1px solid #e6e6e6;
}
.jqCron-selector-list li.selected {
  background: #0090D9;
  color: #ffffff;
}
.jqCron-selector-list li:hover {
  background: #fcfcfc;
}
.jqCron-error .jqCron-selector-title {
  background: #F45857;
  border: 1px solid #f12827;
  color: #ffffff;
}
.jqCron-container.disable * {
  color: #363c43;
}
.jqCron-container.disable .jqCron-selector-title {
  background: #ffffff !important;
}
.hint-icon {
  color: #06A599;
}
#admin-main .admin-block .report-output .toast .btn {
  color: #ffffff;
  border: 1px solid #295;
  background-color: #24a259;
}
#admin-main .admin-block .report-output .toast .btn:hover {
  border-color: #209150;
  background-color: #295;
}
#admin-main .admin-block .report-output .toast .btn.btn-error {
  border: 1px solid #ab3326;
  background-color: #b33528;
}
#admin-main .admin-block .report-output .toast .btn.btn-error:hover {
  border-color: #a33024;
  background-color: #ab3326;
}
#admin-main .admin-block .report-output .toast .btn.btn-warning {
  border: 1px solid #d67118;
  background-color: #df7619;
}
#admin-main .admin-block .report-output .toast .btn.btn-warning:hover {
  border-color: #cd6d17;
  background-color: #d67118;
}
.report-output ul.problems {
  background-color: #ffffff;
  color: #6f7b8a;
}
.report-output ul.problems .toast {
  color: #ffffff;
}
.report-output ul.problems .toast.toast-success {
  background-color: #27AE60;
}
.report-output ul.problems .toast.toast-error {
  background-color: #C0392B;
}
.report-output ul.problems .toast.toast-warning {
  background-color: #E67E22;
}
.report-output ul.problems ul.details {
  background-color: #ffffff;
}
.report-output ul.problems ul.details code {
  color: #0090D9;
  background: rgba(240, 247, 254, 0.8);
}
.report-output ul.problems ul.details .menu-item {
  border-top: 1px solid #f7f7f7;
}
.report-output ul.problems ul.details .menu-badge .label.label-success {
  background: #27AE60;
}
.report-output ul.problems ul.details .menu-badge .label.label-error {
  background: #C0392B;
}
